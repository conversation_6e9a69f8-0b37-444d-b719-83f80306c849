// Test script to create a character programmatically
const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Test character creation
async function testCharacterCreation() {
    console.log('[TEST] Starting character creation test...');
    
    try {
        // First, let's try to execute the create character command
        const result = await ipc<PERSON><PERSON><PERSON>.invoke('core:commands.execute', {
            payload: {
                commandId: 'book-ide.characters:commands.createCharacter',
                args: {}
            }
        });
        
        console.log('[TEST] Command execution result:', result);
    } catch (error) {
        console.error('[TEST] Error creating character:', error);
    }
}

// Run the test
testCharacterCreation();
