// @ts-check

import eslint from '@eslint/js';
import tslint from 'typescript-eslint';
import eslintReact from "@eslint-react/eslint-plugin";

export default tslint.config(
  eslint.configs.recommended,
  tslint.configs.recommended,
  tslint.configs.stylistic,
  tslint.configs.strict,
  eslintReact.configs["recommended-typescript"],
  {
    files: ['/src/**/*', '/src/**/*'],
  },
  {
    ignores: [
      'node_modules/**',
      '.vite/**',
      'dist/**',
      'build/**',
      'out/**'
    ],
  },
  {
    languageOptions: {
      parser: tslint.parser,
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
    rules: {
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_',
        },
      ],
      '@eslint-react/dom/no-missing-button-type': 'off', // Disable button type requirement
    },
  }
);
