import { defineConfig } from 'vite';
import path from "node:path";
import { fileURLToPath } from 'node:url';
import { glob } from 'glob';

// Получаем путь к текущей директории в ESM
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  resolve: {
    alias: {
        // Используем новый __dirname на основе import.meta.url
        '@main': path.resolve(__dirname, './src/main'),
        '@renderer': path.resolve(__dirname, './src/renderer'),
        '@shared': path.resolve(__dirname, './src/shared'),
        '@': path.resolve(__dirname, './src'),
        '@extensions': path.resolve(__dirname, './src/extensions'),
        '@services': path.resolve(__dirname, './src/main/services'),
    },
  },
  mode: process.env.MODE === 'development' ? 'development' : 'production',
  build: {
    rollupOptions: {
      input: {
          // Основная точка входа
          main: 'src/main/main.ts',
          // Точки входа для расширений
          ...Object.fromEntries(
             glob.sync('src/extensions/*/main/index.ts').map((file) => [
               // Генерируем имя чанка как 'extensions/books/main/index'
               path.relative('src', file.slice(0, file.length - path.extname(file).length)),
               file
             ])
           )
       },
       external: [
         'electron',
         'better-sqlite3',
         'node:module', 'node:path', 'node:fs', 'node:url',
       ],
       output: {
        format: 'es', // Меняем формат на ESM
      },
    },
    minify: process.env.MODE !== 'development',
    emptyOutDir: false, // Не очищаем перед сборкой, чтобы сохранить расширения
  },
});
