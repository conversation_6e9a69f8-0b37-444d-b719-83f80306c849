import {defineConfig} from 'vite';
import path from "node:path";

export default defineConfig({
  root: './',
  resolve: {
    alias: {
      '@main': path.resolve(__dirname, './src/main'),
      '@renderer': path.resolve(__dirname, './src/renderer'),
      '@shared': path.resolve(__dirname, './src/shared'),
      '@': path.resolve(__dirname, './src'),
      '@services': path.resolve(__dirname, './src/main/services'),
    },
  },
});
