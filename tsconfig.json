{"compilerOptions": {"target": "ESNext", "module": "ESNext", "jsx": "react-jsx", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "strict": true, "types": ["vite/client"], "paths": {"@main/*": ["src/main/*"], "@renderer/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"], "@/*": ["src/*"], "@services/*": ["src/main/services/*"]}}, "include": ["src/**/*", "forge.env.d.ts", "forge.config.ts", "eslint.config.mjs", "vite.preload.config.ts", "vite.renderer.config.ts", "vite.main.config.ts", "vite.extensions.config.ts"]}