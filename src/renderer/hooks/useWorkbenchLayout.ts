import React, { useMemo } from 'react';

interface WorkbenchLayoutProps {
  isSidebarVisible: boolean;
  sidebarWidth: number;
  isPanelVisible: boolean;
  panelHeight: number;
}

interface WorkbenchLayoutResult {
  gridStyle: React.CSSProperties;
  activityBarStyle: React.CSSProperties;
  sidebarStyle: React.CSSProperties;
  sidebarResizerStyle: React.CSSProperties;
  editorGroupStyle: React.CSSProperties;
  panelResizerStyle: React.CSSProperties;
  panelStyle: React.CSSProperties;
  statusBarStyle: React.CSSProperties;
}

/**
 * Custom hook to calculate workbench layout styles based on visibility and dimensions.
 * @param props - Layout properties including visibility flags and dimensions.
 * @returns An object containing calculated CSS styles for grid layout and individual components.
 */
export function useWorkbenchLayout({
  isSidebarVisible,
  sidebarWidth,
  isPanelVisible,
  panelHeight,
}: WorkbenchLayoutProps): WorkbenchLayoutResult {

  // Define constant styles outside useMemo where possible
  const sidebarStyleBase: React.CSSProperties = { gridColumn: '2 / 3', gridRow: '1 / 2' };
  const sidebarResizerStyleBase: React.CSSProperties = { gridColumn: '3 / 4', gridRow: '1 / 2' };
  const panelResizerStyleBase: React.CSSProperties = { gridColumn: '2 / 5', gridRow: '2 / 3' };
  const panelStyleBase: React.CSSProperties = { gridColumn: '2 / 5', gridRow: '3 / 4', overflow: 'hidden' };

  // useMemo for gridStyle as it depends on all props
  const gridStyle = useMemo(() => ({
      display: 'grid',
      gridTemplateColumns: `48px ${isSidebarVisible ? `${sidebarWidth}px 5px` : '0px 0px'} 1fr`,
      gridTemplateRows: `1fr ${isPanelVisible ? `5px ${panelHeight}px` : '0px 0px'} 22px`,
      height: '100%',
      overflow: 'hidden',
  }), [isSidebarVisible, sidebarWidth, isPanelVisible, panelHeight]);

  // useMemo for styles depending only on isPanelVisible
  const activityBarStyle = useMemo(() => {
      const statusBarRow = isPanelVisible ? 4 : 3;
      return { gridColumn: '1 / 2', gridRow: `1 / ${statusBarRow}` };
  }, [isPanelVisible]);

  const statusBarStyle = useMemo(() => {
      const statusBarRow = isPanelVisible ? 4 : 3;
      return { gridColumn: '1 / 5', gridRow: `${statusBarRow} / ${statusBarRow + 1}` };
  }, [isPanelVisible]);

  // useMemo for styles depending only on isSidebarVisible
  const editorGroupStyle = useMemo(() => {
      const editorGroupColStart = isSidebarVisible ? 4 : 2;
      return { gridColumn: `${editorGroupColStart} / 5`, gridRow: '1 / 2', overflow: 'hidden' };
  }, [isSidebarVisible]);


  // Return memoized and constant styles
  return {
    gridStyle,
    activityBarStyle,
    sidebarStyle: sidebarStyleBase, // Return constant
    sidebarResizerStyle: sidebarResizerStyleBase, // Return constant
    editorGroupStyle,
    panelResizerStyle: panelResizerStyleBase, // Return constant
    panelStyle: panelStyleBase, // Return constant
    statusBarStyle,
  };
}
