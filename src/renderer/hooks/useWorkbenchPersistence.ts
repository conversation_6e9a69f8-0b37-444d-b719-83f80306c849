import {useCallback, useEffect} from 'react';
import {useWorkbenchStore} from '@renderer/state/workbenchStore';
import {useEditorTabsStore} from '@renderer/state/editorTabsStore';
import {configurationService} from '@renderer/core/services/configuration.service';
import type {IpcErrorData} from '@shared/types/ipc';
import {debounce} from 'lodash';
import {shallow} from 'zustand/shallow';
import { EditorTab } from '@shared/types/ui';

// Type for the combined state to persist
interface PersistedState {
  layout: {
    sidebarWidth: number;
    panelHeight: number;
    isSidebarVisible: boolean;
    isPanelVisible: boolean;
  };
  tabs: {
    openTabs: EditorTab[];
    activeTabId: string | null;
  }
}

// Debounced save function - now uses configurationService.updateValue
const debouncedSaveState = debounce(async (stateToSave: PersistedState) => {
  console.log('[useWorkbenchPersistence] Debounced save state:', stateToSave);
  try {
    // Save layout and editor state using configurationService
    await Promise.all([
      configurationService.updateValue('workbench.layout', stateToSave.layout),
      configurationService.updateValue('workbench.tabs', stateToSave.tabs)
    ]);
    console.log('[useWorkbenchPersistence] Layout and editor state saved successfully via ConfigurationService.');
  } catch (error) {
    // Error handling might need adjustment based on how ConfigurationService throws errors
    const errorData = error as IpcErrorData; // Keep type assertion for now, adjust if needed
    console.error(`[useWorkbenchPersistence] Failed to save workbench state via ConfigurationService:`, errorData?.message || error);
  }
}, 1000);

/**
 * Hook to manage persistence for both workbench layout and editor tabs state.
 */
export function useWorkbenchPersistence() {
  const {restoreLayoutState} = useWorkbenchStore.getState();
  const {setTabsState} = useEditorTabsStore.getState();
  
  const loadAndApplyState = useCallback(async () => {
    console.log('[useWorkbenchPersistence] Loading layout and editor state via ConfigurationService...');
    try {
      // Load settings using configurationService.getValue
      const [layoutSettings, tabsSettings] = await Promise.all([
        configurationService.getValue<PersistedState['layout']>('workbench.layout'),
        configurationService.getValue<PersistedState['tabs']>('workbench.tabs')
      ]);
      
      // No need for explicit type casting here as getValue is generic
      
      console.log('[useWorkbenchPersistence] Loaded settings via ConfigurationService:', {
        layout: layoutSettings,
        tabs: tabsSettings
      });
      
      restoreLayoutState(layoutSettings); // Pass potentially undefined value
      
      if (tabsSettings && Array.isArray(tabsSettings.openTabs)) {
        // Reconstruct full EditorTab objects, including componentName
        const restoredTabs: EditorTab[] = tabsSettings.openTabs.map(savedTab => ({
          id: savedTab.id,
          editorType: savedTab.editorType,
          extensionId: savedTab.extensionId,
          title: savedTab.title,
          dataId: savedTab.dataId,
          icon: savedTab.icon,
          componentName: savedTab.componentName || '', // Restore componentName, default to empty string
          isDirty: false,
        }));
        setTabsState(restoredTabs, tabsSettings.activeTabId ?? null);
      } else {
        console.log('[useWorkbenchPersistence] No valid editor state found to restore.');
        setTabsState([], null);
      }
      console.log('[useWorkbenchPersistence] Layout and editor state restored and applied.');
      
    } catch (error) {
      console.error('[useWorkbenchPersistence] Error loading state via ConfigurationService:', error);
      restoreLayoutState(undefined); // Restore default on error
      setTabsState([], null); // Restore default on error
    }
  }, [restoreLayoutState, setTabsState]);
  
  useEffect(() => {
    loadAndApplyState();
  }, [loadAndApplyState]);
  
  useEffect(() => {
    console.log('[useWorkbenchPersistence] Subscribing to state changes for persistence...');
    
    const triggerSave = () => {
      const layoutState = useWorkbenchStore.getState();
      const tabsState = useEditorTabsStore.getState();
      
      const stateToSave: PersistedState = {
        layout: {
          sidebarWidth: layoutState.sidebarWidth,
          panelHeight: layoutState.panelHeight,
          isSidebarVisible: layoutState.isSidebarVisible,
          isPanelVisible: layoutState.isPanelVisible,
        },
        // Create the PersistedTabsState object or null based on open tabs
        tabs: tabsState.openTabs.length > 0 ? {
          openTabs: tabsState.openTabs.map((tab): EditorTab => ({ // Map to PersistedTabData
            id: tab.id,
            editorType: tab.editorType,
            extensionId: tab.extensionId,
            title: tab.title,
            dataId: tab.dataId,
            icon: tab.icon,
            componentName: tab.componentName,
            isDirty: tab.isDirty,
          })),
          activeTabId: tabsState.activeTabId,
        } : tabsState
      };
      // stateToSave now correctly matches the PersistedState interface
      debouncedSaveState(stateToSave);
    };
    
    const unsubLayout = useWorkbenchStore.subscribe(
      (state) => ({
        sidebarWidth: state.sidebarWidth,
        panelHeight: state.panelHeight,
        isSidebarVisible: state.isSidebarVisible,
        isPanelVisible: state.isPanelVisible,
      }),
      triggerSave,
      {equalityFn: shallow, fireImmediately: false}
    );
    
    const unsubEditor = useEditorTabsStore.subscribe(
      (state) => ({openTabs: state.openTabs, activeTabId: state.activeTabId}),
      triggerSave,
      {equalityFn: shallow, fireImmediately: false}
    );
    
    return () => {
      console.log('[useWorkbenchPersistence] Unsubscribing from state changes.');
      unsubLayout();
      unsubEditor();
      debouncedSaveState.cancel();
    };
  }, []);
  
}
