import { useRef, useMemo, useCallback } from 'react';
import { throttle } from 'lodash';

interface UsePanelResizingProps {
  sidebarWidth: number;
  panelHeight: number;
  setSidebarWidth: (width: number) => void;
  setPanelHeight: (height: number) => void;
}

export function usePanelResizing({
  sidebarWidth,
  panelHeight,
  setSidebarWidth,
  setPanelHeight,
}: UsePanelResizingProps) {

  const sidebarSashRef = useRef({ startX: 0, startWidth: 0 });
  const panelSashRef = useRef({ startY: 0, startHeight: 0 });

  // Используем throttle для ограничения частоты вызовов
  const throttledSetSidebarWidth = useMemo(() => throttle(setSidebarWidth, 50), [setSidebarWidth]);
  const throttledSetPanelHeight = useMemo(() => throttle(setPanelHeight, 50), [setPanelHeight]);

  const handleSidebarResize = useCallback((deltaX: number) => {
      const newWidth = sidebarSashRef.current.startWidth + deltaX;
      throttledSetSidebarWidth(newWidth);
  }, [throttledSetSidebarWidth]);

  const handlePanelResize = useCallback((deltaY: number) => {
      // Изменение высоты панели происходит в обратную сторону (тянем вверх - увеличиваем)
      const newHeight = panelSashRef.current.startHeight - deltaY;
      throttledSetPanelHeight(newHeight);
  }, [throttledSetPanelHeight]);

  // Функция для обработчика onMouseDown сайдбара
  const handleSidebarSashMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
      sidebarSashRef.current = { startX: e.clientX, startWidth: sidebarWidth };
  }, [sidebarWidth]);

  // Функция для обработчика onMouseDown панели
  const handlePanelSashMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
      panelSashRef.current = { startY: e.clientY, startHeight: panelHeight };
  }, [panelHeight]);

  return {
    handleSidebarResize,
    handlePanelResize,
    handleSidebarSashMouseDown,
    handlePanelSashMouseDown,
  };
}
