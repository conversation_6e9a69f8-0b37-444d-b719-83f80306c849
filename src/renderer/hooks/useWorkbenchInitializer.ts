import { useState, useEffect } from 'react';
import { useWorkbenchStore } from '@renderer/state/workbenchStore';
import type { ViewContainerDescriptor, ViewDescriptor } from '@main/services/view.service';
import { IpcErrorData, ViewsGetRegisteredResult, ViewContainersGetRegisteredResult } from '@shared/types/ipc'; // Убрал SettingsGetValueResult, SettingsGetValueArgs
import { IpcChannels } from '@shared/constants/ipc-channels';
import { ipcRendererService } from '@renderer/core/services/ipcRendererService';

interface InitializerResult {
  viewContainers: ViewContainerDescriptor[];
  sidebarViews: ViewDescriptor[];
  panelViews: ViewDescriptor[];
  statusBarItems: ViewDescriptor[];
  loading: boolean;
  error: string | null;
}

export function useWorkbenchInitializer(): InitializerResult {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewContainers, setViewContainers] = useState<ViewContainerDescriptor[]>([]);
  const [sidebarViews, setSidebarViews] = useState<ViewDescriptor[]>([]);
  const [panelViews, setPanelViews] = useState<ViewDescriptor[]>([]);
  const [statusBarItems, setStatusBarItems] = useState<ViewDescriptor[]>([]);

  // Получаем только setActiveViewContainerId и activeViewContainerId
  const { setActiveViewContainerId, activeViewContainerId } = useWorkbenchStore.getState();

  useEffect(() => {
    console.log('[useWorkbenchInitializer] Initializing views and containers...');
    let isMounted = true; // Флаг для предотвращения обновления состояния после размонтирования

    const initializeApp = async () => {
      setLoading(true);
      setError(null);
      console.log('[useWorkbenchInitializer] Fetching views and containers...');
      try {
        // Загружаем виды и контейнеры
        const [containersResult, viewsResult] = await Promise.all([
          ipcRendererService.invoke<ViewContainersGetRegisteredResult>(IpcChannels.VIEWS_GET_REGISTERED_CONTAINERS),
          ipcRendererService.invoke<ViewsGetRegisteredResult>(IpcChannels.VIEWS_GET_REGISTERED)
        ]);

        if (!isMounted) {
          console.log('[useWorkbenchInitializer] Component unmounted after fetching views/containers.');
          return;
        }

        console.log('[useWorkbenchInitializer] Views and containers loaded.');

        // Устанавливаем View Containers
        const typedContainersResult = containersResult as ViewContainerDescriptor[];
        setViewContainers(typedContainersResult);

        // Устанавливаем активный View Container по умолчанию, если он еще не установлен
        if (typedContainersResult.length > 0 && activeViewContainerId === null) {
          setActiveViewContainerId(typedContainersResult[0].id);
          console.log(`[useWorkbenchInitializer] Set default active view container: ${typedContainersResult[0].id}`);
        }

        // Распределяем Views по локациям
        const typedViewsResult = viewsResult as ViewDescriptor[];
        setSidebarViews(typedViewsResult.filter(v => v.location === 'sidebar'));
        setPanelViews(typedViewsResult.filter(v => v.location === 'panel'));
        setStatusBarItems(typedViewsResult.filter(v => v.location === 'statusbar'));

        console.log('[useWorkbenchInitializer] Static initialization complete.');

      } catch (err) {
        // ipcRendererService уже логирует ошибку
        const errorData = err as IpcErrorData; // Предполагаем, что ошибка имеет структуру IpcErrorData
        console.error('[useWorkbenchInitializer] Error during static initialization:', errorData);
        if (isMounted) {
            setError(errorData.message || 'Unknown error during view/container initialization');
        }
      } finally {
        if (isMounted) {
            setLoading(false);
            console.log('[useWorkbenchInitializer] Static initialization finished.');
        }
      }
    };

    initializeApp();

    return () => {
        isMounted = false;
        console.log('[useWorkbenchInitializer] Effect cleanup.'); // Add log for cleanup
    };
  }, [setActiveViewContainerId]); // Убрали activeViewContainerId из зависимостей

  return { viewContainers, sidebarViews, panelViews, statusBarItems, loading, error };
}
