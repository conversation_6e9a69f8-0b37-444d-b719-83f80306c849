import {useEffect, useRef, useState} from 'react';
// Import types and service
import {CommandsExecuteArgs, IpcErrorData, KeybindingsGetActiveResult} from '@shared/types/ipc'; // Removed KeybindingInfo from here
import {KeybindingInfo} from '@shared/types/keybindings'; // Import KeybindingInfo from its new location
import {IpcChannels} from '@shared/constants/ipc-channels'; // Импортируем константы
import {ipcRendererService} from '@renderer/core/services/ipcRendererService';

/**
 * Custom hook to manage global application hotkeys by executing commands via IPC.
 * Fetches active keybindings from the main process and sets up a keydown listener.
 */
export function useGlobalHotkeys(): void {
  // Храним активные привязки в состоянии или ref. Ref лучше, чтобы не вызывать ререндер при обновлении.
  const activeKeybindingsRef = useRef<Map<string, string>>(new Map());
  const [isLoading, setIsLoading] = useState(true); // Состояние загрузки привязок
  
  // Загружаем привязки при монтировании
  useEffect(() => {
    let isMounted = true;
    const fetchKeybindings = async () => {
      console.log('[useGlobalHotkeys] Fetching active keybindings...');
      setIsLoading(true);
      try {
        // Используем новый сервис и канал
        const result = await ipcRendererService.invoke<KeybindingsGetActiveResult>(IpcChannels.KEYBINDINGS_GET_ACTIVE); // Используем константу
        if (isMounted) {
          // ipcRendererService throws on error, no need for isIpcError check
          const bindingsMap = new Map<string, string>();
          // Use KeybindingInfo type
          result.forEach((binding: KeybindingInfo) => {
            if (binding && typeof binding.key === 'string' && typeof binding.command === 'string') {
              bindingsMap.set(binding.key, binding.command);
            }
          });
          activeKeybindingsRef.current = bindingsMap;
          console.log('[useGlobalHotkeys] Active keybindings loaded:', activeKeybindingsRef.current);
        }
      } catch (error) {
        // Error is already logged by ipcRendererService
        const errorData = error as IpcErrorData;
        console.error('[useGlobalHotkeys] Failed to fetch keybindings:', errorData.message, error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    
    fetchKeybindings();
    
    // TODO: Добавить механизм обновления привязок, если контекст в Main изменился
    // Например, через IPC событие от Main или периодический запрос
    
    return () => {
      isMounted = false;
    };
  }, []); // Пустой массив зависимостей для выполнения один раз
  
  // Устанавливаем слушатель событий клавиатуры
  useEffect(() => {
    // Не устанавливаем слушатель, пока привязки не загружены
    if (isLoading) {
      console.log('[useGlobalHotkeys] Waiting for keybindings to load...');
      return;
    }
    
    const handleKeyDown = (event: KeyboardEvent) => {
      const key = event.code;
      const ctrl = event.ctrlKey;
      const meta = event.metaKey; // Command key on macOS
      const shift = event.shiftKey;
      const alt = event.altKey; // Option key on macOS
      
      // Формируем строку-представление сочетания клавиш
      let keyCombo = '';
      if (meta) keyCombo += 'Cmd+';
      if (ctrl && !meta) keyCombo += 'Ctrl+';
      if (alt) keyCombo += 'Alt+';
      if (shift) keyCombo += 'Shift+';
      keyCombo += key.replace('Key', '').replace('Digit', '');
      
      // Ищем команду в загруженных активных привязках
      const commandId = activeKeybindingsRef.current.get(keyCombo);
      
      if (commandId) {
        console.log(`[useGlobalHotkeys] Detected key combo: ${keyCombo}, executing command via core:commands.execute: ${commandId}`);
        event.preventDefault();
        // Вызываем команду через новый сервис
        const execute = async () => {
          try {
            const args: CommandsExecuteArgs = {commandId, args: {}};
            await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args); // Используем константу
            console.log(`[useGlobalHotkeys] Command ${commandId} executed successfully.`);
          } catch (err) {
            const errorData = err as IpcErrorData;
            console.error(`[useGlobalHotkeys] Error executing command ${commandId}:`, errorData.message, err);
            // Можно показать уведомление пользователю
          }
        };
        execute(); // Call the async function
      }
    };
    
    console.log('[useGlobalHotkeys] Adding keydown listener (bindings loaded)');
    window.addEventListener('keydown', handleKeyDown);
    
    // Cleanup function to remove the event listener
    return () => {
      console.log('[useGlobalHotkeys] Removing keydown listener');
      window.removeEventListener('keydown', handleKeyDown);
    };
    // Пустой массив зависимостей, т.к. ссылка на ref стабильна, а isLoading обработан выше
  }, [isLoading]); // Перезапускаем эффект, когда isLoading меняется
}
