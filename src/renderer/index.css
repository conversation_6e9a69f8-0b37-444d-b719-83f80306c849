/* Global styles and CSS Variables */

:root {
  /* Dark Theme Palette */
  --app-foreground: #cccccc;
  --app-background: #181818;
  --app-editor-background: #1e1e1e;
  --app-sideBar-background: #252526;
  --app-activityBar-background: #333333;
  --app-panel-background: var(--app-editor-background);
  --app-statusBar-background: #007acc;
  --app-statusBar-foreground: #ffffff;
  --app-list-hoverBackground: #2a2d2e;
  --app-list-activeSelectionBackground: #094771;
  --app-list-inactiveSelectionBackground: #37373d;
  --app-border: #3c3c3c;
  --app-input-background: #3c3c3c;
  --app-input-foreground: var(--app-foreground);
  --app-button-background: #0e639c;
  --app-button-foreground: #ffffff;
  --app-button-hoverBackground: #1177bb;
  --app-scrollbarSlider-background: #4e4e4e80;
  --app-scrollbarSlider-hoverBackground: #68686880;
  --app-scrollbarSlider-activeBackground: #686868b0;
  --app-editorGroupHeader-tabsBackground: #2d2d2d;
  --app-tab-inactiveBackground: #2d2d2d;
  --app-tab-activeBackground: var(--app-editor-background);
  --app-tab-border: var(--app-border);
  --app-tab-activeForeground: var(--app-foreground);
  --app-tab-inactiveForeground: #8e8e8e;
  --app-errorForeground: #f48771;
  --app-warningForeground: #cca700; /* Пример */
  --app-infoForeground: #3794ff; /* Пример */

  /* Font settings (can be overridden by user settings later) */
  --editor-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  --editor-font-size: 14px;
  --editor-line-height: 1.5;
}

body {
  margin: 0;
  font-family: var(--editor-font-family);
  font-size: var(--editor-font-size);
  line-height: var(--editor-line-height);
  background-color: var(--app-editor-background); /* Base background */
  color: var(--app-foreground); /* Base text color */
  height: 100vh;
  width: 100vw;
  overflow: hidden; /* Prevent body scroll */
}

/* Basic button styling */
button {
  background-color: var(--app-button-background);
  color: var(--app-button-foreground);
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

button:hover {
  background-color: var(--app-button-hoverBackground);
}

button:disabled {
  background-color: var(--app-input-background);
  color: var(--app-tab-inactiveForeground);
  cursor: not-allowed;
}

/* Basic select styling */
select {
   background-color: var(--app-input-background);
   color: var(--app-input-foreground);
   border: 1px solid var(--app-border);
   padding: 4px 8px;
   border-radius: 3px;
}

/* Basic details/summary styling */
details > summary {
  cursor: pointer;
  padding: 2px 5px;
  list-style: none; /* Remove default marker */
}
details > summary::before {
  content: '▶'; /* Default collapsed marker */
  display: inline-block;
  margin-right: 5px;
  font-size: 0.8em;
  transition: transform 0.1s ease-in-out;
}
details[open] > summary::before {
  transform: rotate(90deg); /* Rotated marker when open */
}

/* Basic list styling */
ul {
  list-style: none;
  padding-left: 15px;
}
li {
  padding: 2px 0;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-track {
  background: var(--app-editor-background); /* Match editor background */
}
::-webkit-scrollbar-thumb {
  background: var(--app-scrollbarSlider-background);
  border-radius: 5px;
}
::-webkit-scrollbar-thumb:hover {
  background: var(--app-scrollbarSlider-hoverBackground);
}
::-webkit-scrollbar-thumb:active {
  background: var(--app-scrollbarSlider-activeBackground);
}
