// src/renderer/core/editors/SettingsEditor.editor.tsx
/* eslint-disable @eslint-react/hooks-extra/no-direct-set-state-in-use-effect */
import React, { useCallback, useEffect, useMemo, useState } from "react";
import type {
  SettingDeclaration,
  SettingControlType,
} from "@shared/types/settings"; // Import SettingControlType
import { IpcErrorData, IpcInvokeResult } from "@shared/types/ipc";
import { IpcChannels } from "@shared/constants/ipc-channels";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import { configurationService } from "@renderer/core/services/configuration.service"; // Import new service
import {
  AvailableTheme,
  themeService,
} from "@renderer/core/services/theme.service";
import { JsonValue } from "@shared/types/common"; // JsonValue is imported here
import "./SettingsEditor.css";

// --- Helper Functions ---

const getCategoryFromId = (id: string): string => {
  const parts = id.split(".");
  const category = parts.length > 1 ? parts[0] : "Other";
  return category.charAt(0).toUpperCase() + category.slice(1);
};

const getLabelFromId = (id: string, label?: string): string => {
  if (label) return label;
  const parts = id.split(".");
  return parts
    .slice(1)
    .map((part) => part.replace(/([A-Z])/g, " $1"))
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
    .join(" ");
};

// Helper to check if a value is a valid JsonValue (Placed correctly after imports)
const isJsonValue = (value: unknown): value is JsonValue => {
  const type = typeof value;
  if (value === null) return true;
  if (type === "string" || type === "number" || type === "boolean") return true;
  if (type === "object") {
    // Ensure value is not null before proceeding with Array/Object checks
    if (value === null) return false; // Should be caught by the null check above, but good for clarity

    if (Array.isArray(value)) {
      // Recursively check array elements
      return value.every(isJsonValue);
    }
    // Check for plain objects, avoid classes, functions etc.
    // Ensure it's a non-null object before checking constructor and values
    if (value && typeof value === "object" && value.constructor === Object) {
      // Now 'value' is confirmed as a non-null plain object, safe for Object.values
      try {
        // Check if all *values* within the object are valid JsonValues
        return Object.values(value).every(isJsonValue);
      } catch (e) {
        // Should not happen with the checks, but catch potential errors
        console.error(
          "Error during recursive isJsonValue object check:",
          e,
          value
        );
        return false;
      }
    }
  }
  // If none of the above, it's not a valid JsonValue (e.g., undefined, function, Symbol)
  return false;
};

// --- Underlying Setting Control Components ---

// Base props for all controls
interface BaseSettingControlProps {
  decl: SettingDeclaration;
  isModified: boolean;
  onChange: (key: string, value: JsonValue) => void;
  onBrowse?: (key: string, currentValue: string) => Promise<void>;
}
interface SettingCheckboxProps extends BaseSettingControlProps {
  value: boolean;
}
interface SettingNumberInputProps extends BaseSettingControlProps {
  value: number;
}
interface SettingStringInputProps extends BaseSettingControlProps {
  value: string;
} // For text, password, path, select, themeSelect

const SettingCheckbox: React.FC<SettingCheckboxProps> = ({
  decl,
  value, // Receive specific boolean value
  onChange,
}) => {
  return (
    <input
      type="checkbox"
      checked={value}
      onChange={(e) => onChange(decl.id, e.target.checked)}
    />
  );
};

const SettingNumberInput: React.FC<SettingNumberInputProps> = ({
  decl,
  value, // Receive specific number value
  isModified,
  onChange,
}) => (
  <input
    type="number"
    value={value} // Use the passed number value directly
    min={decl.minimum}
    max={decl.maximum}
    // Ensure value sent back is a number, handle potential NaN
    onChange={(e) => {
      const numValue = e.target.valueAsNumber;
      // Use default from declaration if NaN, ensuring it's a number or fallback to 0
      const fallbackDefault =
        typeof decl.default === "number" ? decl.default : 0;
      onChange(decl.id, isNaN(numValue) ? fallbackDefault : numValue);
    }}
    className={isModified ? "modified" : ""}
  />
);

const SettingTextInput: React.FC<SettingStringInputProps> = ({
  decl,
  value, // Receive specific string value
  isModified,
  onChange,
}) => (
  <input
    type="text"
    value={value} // Use the passed string value directly
    onChange={(e) => onChange(decl.id, e.target.value)}
    className={isModified ? "modified" : ""}
  />
);

const SettingPasswordInput: React.FC<SettingStringInputProps> = ({
  decl,
  value, // Receive specific string value
  isModified,
  onChange,
}) => (
  <input
    type="password"
    value={value} // Use the passed string value directly
    onChange={(e) => onChange(decl.id, e.target.value)}
    className={isModified ? "modified" : ""}
    placeholder="Enter your API Key"
  />
);

const SettingSelect: React.FC<SettingStringInputProps> = ({
  decl,
  value, // Receive specific string value
  isModified,
  onChange,
}) => (
  <select
    value={value} // Use the passed string value directly
    onChange={(e) => onChange(decl.id, e.target.value)}
    className={isModified ? "modified" : ""}
  >
    {(decl.enum ?? []).map((enumValue, index) => (
      <option key={enumValue} value={enumValue}>
        {decl.enumDescriptions?.[index] ?? enumValue}
      </option>
    ))}
  </select>
);

const SettingThemeSelect: React.FC<SettingStringInputProps> = ({
  decl,
  value, // Receive specific string value
  isModified,
  onChange,
}) => {
  const [availableThemes, setAvailableThemes] = useState<AvailableTheme[]>([]);

  useEffect(() => {
    setAvailableThemes(themeService.getAvailableThemes());
  }, []);

  return (
    <select
      value={value} // Use the passed string value directly
      onChange={(e) => onChange(decl.id, e.target.value)}
      className={isModified ? "modified" : ""}
    >
      {availableThemes.map((theme) => (
        <option key={theme.id} value={theme.id}>
          {theme.label}
        </option>
      ))}
    </select>
  );
};

const SettingPathInput: React.FC<SettingStringInputProps> = ({
  decl,
  value, // Receive specific string value
  isModified,
  onChange,
  onBrowse,
}) => (
  <div className="setting-path-input">
    <input
      type="text"
      value={value} // Use the passed string value directly
      onChange={(e) => onChange(decl.id, e.target.value)} // Path is still a string
      className={isModified ? "modified" : ""}
      readOnly
    />
    {onBrowse && (
      <button
        type="button"
        onClick={() => onBrowse(decl.id, value)} // Pass current string value
        className="browse-button"
      >
        Browse...
      </button>
    )}
  </div>
);

// --- Setting Control Wrapper ---

interface SettingControlWrapperProps {
  decl: SettingDeclaration;
}

const SettingControlWrapper: React.FC<SettingControlWrapperProps> = ({
  decl,
}) => {
  // Ensure initial state is a valid JsonValue (use null if default is undefined or not JsonValue)
  const safeDefault: JsonValue = isJsonValue(decl.default)
    ? decl.default
    : null; // isJsonValue is now in scope
  const [currentValue, setCurrentValue] = useState<JsonValue>(safeDefault);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch initial value
  useEffect(() => {
    let isMounted = true;
    const fetchValue = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Pass safeDefault as the fallback to getValue
        const value = await configurationService.getValue<JsonValue>(
          decl.id,
          safeDefault
        );
        if (isMounted) {
          // Ensure fetched value is also valid JsonValue before setting state
          setCurrentValue(isJsonValue(value) ? value : safeDefault);
        }
      } catch (err) {
        if (isMounted) {
          const errorData = err as IpcErrorData;
          setError(`Failed to load value: ${errorData.message || String(err)}`);
          setCurrentValue(safeDefault); // Fallback to safe default on error
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    fetchValue();
    return () => {
      isMounted = false;
    };
  }, [decl.id, safeDefault]); // Use safeDefault in dependency array

  // Subscribe to external changes
  useEffect(() => {
    const disposable = configurationService.onDidChangeConfiguration((e) => {
      if (e.key === decl.id) {
        console.log(
          `[SettingControlWrapper ${decl.id}] Received external update:`,
          e.newValue
        );
        // Ensure updated value is valid JsonValue
        setCurrentValue(isJsonValue(e.newValue) ? e.newValue : safeDefault);
      }
    });
    return () => disposable.dispose();
  }, [decl.id, safeDefault]); // Include safeDefault

  const handleLocalChange = useCallback(
    async (key: string, value: JsonValue) => {
      setCurrentValue(value); // Optimistic update
      try {
        await configurationService.updateValue(key, value);
      } catch (err) {
        const errorData = err as IpcErrorData;
        console.error(
          `[SettingControlWrapper ${key}] Failed to save setting:`,
          errorData.message,
          err
        );
        alert(
          `Failed to save setting "${key}": ${
            errorData.message || "Unknown error"
          }`
        );
        // Revert optimistic update on error by re-fetching
        try {
          const actualValue = await configurationService.getValue<JsonValue>(
            decl.id,
            safeDefault
          );
          setCurrentValue(isJsonValue(actualValue) ? actualValue : safeDefault);
        } catch (fetchErr) {
          console.error(
            `[SettingControlWrapper ${key}] Failed to re-fetch value after save error:`,
            fetchErr
          );
          setCurrentValue(safeDefault); // Fallback to safe default if re-fetch fails
        }
      }
    },
    [decl.id, safeDefault]
  ); // Use safeDefault in dependency array

  const handleBrowseDirectory = useCallback(
    async (key: string, currentVal: string) => {
      console.log(`[SettingControlWrapper ${key}] Browsing directory...`);
      try {
        const result: IpcInvokeResult<Electron.OpenDialogReturnValue> =
          await ipcRendererService.invoke(IpcChannels.DIALOG_SHOW_OPEN_DIALOG, {
            title: `Select Directory for ${getLabelFromId(key)}`,
            defaultPath: currentVal || undefined,
            properties: ["openDirectory", "createDirectory"],
          });

        if (result.success && result.data) {
          const dialogData = result.data;
          if (!dialogData.canceled && dialogData.filePaths.length > 0) {
            await handleLocalChange(key, dialogData.filePaths[0]);
          }
        } else if (!result.success) {
          console.error(
            `[SettingControlWrapper ${key}] IPC call failed:`,
            result.error
          );
          alert(
            `Error opening dialog: ${
              result.error?.message || "Unknown IPC error"
            }`
          );
        }
      } catch (err) {
        const errorData = err as IpcErrorData;
        console.error(
          `[SettingControlWrapper ${key}] Error opening dialog:`,
          errorData.message,
          err
        );
        alert(`Error opening dialog: ${errorData.message || "Unknown error"}`);
      }
    },
    [decl.id, handleLocalChange]
  ); // handleLocalChange is stable due to useCallback

  if (isLoading) {
    return <div className="setting-control-loading">Loading...</div>;
  }

  if (error) {
    return (
      <div className="setting-control-error" title={error}>
        Error!
      </div>
    );
  }

  // Determine control type
  // Explicitly type the variable that holds the derived control type
  let controlTypeToRender: SettingControlType | "json" | "text" =
    decl.controlType ?? "text"; // Default to 'text'

  if (!decl.controlType) {
    // Infer if not explicitly set
    switch (decl.type) {
      case "boolean":
        controlTypeToRender = "checkbox";
        break;
      case "number":
        controlTypeToRender = "number";
        break;
      case "string":
        if (decl.id === "ai.apiKey") controlTypeToRender = "password";
        else controlTypeToRender = decl.enum ? "select" : "text";
        break;
      case "enum":
        controlTypeToRender = "select";
        break;
      case "array":
      case "object":
        controlTypeToRender = "json";
        break; // Use 'json' for rendering complex types
      default:
        controlTypeToRender = "text";
    }
  }
  // Override specific IDs
  if (decl.id === "workbench.theme") controlTypeToRender = "themeSelect";
  if (decl.format === "path") controlTypeToRender = "path";

  const isModified = currentValue !== safeDefault;

  // Base props for all controls
  const baseProps: BaseSettingControlProps = {
    decl,
    isModified,
    onChange: handleLocalChange,
    onBrowse:
      controlTypeToRender === "path" ? handleBrowseDirectory : undefined,
  };

  // Render the appropriate underlying control with correctly typed value prop
  switch (
    controlTypeToRender // Use the correctly typed variable
  ) {
    case "checkbox": {
      return <SettingCheckbox {...baseProps} value={!!currentValue} />; // Ensure boolean
    }
    case "number": {
      const numValue = Number(currentValue);
      const fallbackNum = typeof decl.default === "number" ? decl.default : 0;
      return (
        <SettingNumberInput
          {...baseProps}
          value={isNaN(numValue) ? fallbackNum : numValue}
        />
      );
    }
    case "select": {
      return (
        <SettingSelect {...baseProps} value={String(currentValue ?? "")} />
      );
    }
    case "themeSelect": {
      return (
        <SettingThemeSelect {...baseProps} value={String(currentValue ?? "")} />
      );
    }
    case "path": {
      return (
        <SettingPathInput {...baseProps} value={String(currentValue ?? "")} />
      );
    }
    case "password": {
      return (
        <SettingPasswordInput
          {...baseProps}
          value={String(currentValue ?? "")}
        />
      );
    }
    case "json": {
      // Handle complex types (array/object) - render read-only JSON
      const stringValue =
        typeof currentValue === "object" && currentValue !== null
          ? JSON.stringify(currentValue, null, 2) // Pretty print JSON
          : String(currentValue ?? "");
      return (
        <textarea
          value={stringValue}
          readOnly
          className={isModified ? "modified" : ""}
          title="JSON editing not yet supported"
          rows={3}
          style={{ width: "100%", resize: "vertical", fontFamily: "monospace" }}
        />
      );
    }
    case "text": // Handles default text and other string types
    default: {
      return (
        <SettingTextInput {...baseProps} value={String(currentValue ?? "")} />
      );
    }
  }
};

// --- Main Settings Editor Component ---

export const SettingsEditor: React.FC = () => {
  const [declarations, setDeclarations] = useState<SettingDeclaration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Load declarations on mount
  useEffect(() => {
    let isMounted = true;
    const loadDeclarations = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const result = await ipcRendererService.invoke<{
          declarations: SettingDeclaration[];
        }>(IpcChannels.SETTINGS_GET_DECLARATIONS);
        if (isMounted) {
          if (result && Array.isArray(result.declarations)) {
            const sortedDeclarations = [...result.declarations].sort((a, b) =>
              a.id.localeCompare(b.id)
            );
            setDeclarations(sortedDeclarations);
            const firstCategory =
              sortedDeclarations.length > 0
                ? getCategoryFromId(sortedDeclarations[0].id)
                : null;
            // Set initial category only if not searching and category not already set
            if (!searchTerm.trim() && selectedCategory === null) {
              setSelectedCategory(firstCategory);
            }
          } else {
            setError("Received invalid data format for setting declarations.");
          }
        }
      } catch (err) {
        if (isMounted) {
          const errorData = err as IpcErrorData;
          setError(
            `Failed to load settings: ${errorData.message || String(err)}`
          );
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    loadDeclarations();
    return () => {
      isMounted = false;
    };
  }, []); // Run only once on mount

  // Memoize categories and filtered settings
  const { categories, displayedSettings } = useMemo(() => {
    if (declarations.length === 0)
      return { categories: [], displayedSettings: [] };
    // Correctly map to get category IDs
    const uniqueCategories = [
      ...new Set(declarations.map((decl) => getCategoryFromId(decl.id))),
    ].sort();
    const lowerSearchTerm = searchTerm.toLowerCase().trim();
    let filtered: SettingDeclaration[] = declarations;
    let currentDisplayCategory = selectedCategory; // Use a local var to avoid state update loop in memo

    if (lowerSearchTerm) {
      // Filter by search term across all declarations
      filtered = declarations.filter(
        (decl) =>
          decl.id.toLowerCase().includes(lowerSearchTerm) ||
          getLabelFromId(decl.id, decl.label)
            .toLowerCase()
            .includes(lowerSearchTerm) ||
          (decl.description &&
            decl.description.toLowerCase().includes(lowerSearchTerm)) ||
          (decl.markdownDescription &&
            decl.markdownDescription.toLowerCase().includes(lowerSearchTerm))
      );
      currentDisplayCategory = null; // No specific category when searching
    } else if (currentDisplayCategory) {
      // Filter by the selected category
      filtered = declarations.filter(
        (decl) => getCategoryFromId(decl.id) === currentDisplayCategory
      );
    } else if (uniqueCategories.length > 0) {
      // Default to the first category if nothing else is selected
      currentDisplayCategory = uniqueCategories[0];
      filtered = declarations.filter(
        (decl) => getCategoryFromId(decl.id) === currentDisplayCategory
      );
    } else {
      filtered = []; // Should not happen if declarations exist
    }

    return { categories: uniqueCategories, displayedSettings: filtered };
  }, [declarations, searchTerm, selectedCategory]);

  // Effect to set default category when search is cleared
  useEffect(() => {
    if (
      !searchTerm.trim() &&
      selectedCategory === null &&
      categories.length > 0
    ) {
      setSelectedCategory(categories[0]);
    }
  }, [searchTerm, selectedCategory, categories]);

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchTerm = e.target.value;
    setSearchTerm(newSearchTerm);
    // If searching, clear category selection
    if (newSearchTerm.trim()) {
      setSelectedCategory(null);
    }
    // If search cleared, category will be reset by the useEffect above
  };

  // Handle category selection
  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
    setSearchTerm(""); // Clear search on category click
  };

  // --- Render Logic ---

  if (isLoading)
    return <div className="settings-editor loading">Loading Settings...</div>;
  if (error) return <div className="settings-editor error">Error: {error}</div>;
  if (declarations.length === 0)
    return <div className="settings-editor empty">No settings available.</div>;

  return (
    <div className="settings-editor layout">
      <div className="settings-sidebar">
        <input
          type="text"
          placeholder="Search settings"
          value={searchTerm}
          onChange={handleSearchChange}
          className="settings-search-input"
        />
        <ul className="settings-categories">
          {categories.map((category) => (
            <li
              key={category}
              className={
                category === selectedCategory && !searchTerm ? "active" : ""
              }
              onClick={() => handleCategoryClick(category)}
            >
              {category}
            </li>
          ))}
        </ul>
      </div>
      <div className="settings-content">
        <h1>
          {searchTerm ? "Search Results" : selectedCategory ?? "Settings"}
        </h1>
        <div className="settings-list">
          {displayedSettings.length > 0 ? (
            displayedSettings.map((decl) => (
              <div key={decl.id} className="setting-item">
                <div className="setting-details">
                  {/* Use button for label if needed for accessibility, or just label */}
                  <label htmlFor={decl.id}>
                    {getLabelFromId(decl.id, decl.label)}
                  </label>
                  <p>{decl.description || decl.markdownDescription}</p>
                </div>
                <div className="setting-control">
                  {/* Render the wrapper component */}
                  <SettingControlWrapper decl={decl} />
                </div>
              </div>
            ))
          ) : (
            <p>
              No settings found
              {searchTerm
                ? " matching your search"
                : selectedCategory
                ? ` in category "${selectedCategory}"`
                : ""}
              .
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsEditor;
