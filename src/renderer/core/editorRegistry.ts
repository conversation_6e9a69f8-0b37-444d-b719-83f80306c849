/**
 * This module uses Vite's import.meta.glob to eagerly find all potential editor components
 * and provides a function to retrieve the dynamic importer for a given component name.
 */
import {ComponentType} from "react";

// Use import.meta.glob to find all .editor.tsx files within extensions' renderer/editors directories
// Assuming conventions like:
// - /src/extensions/*/renderer/editors/*.editor.tsx
// - /src/renderer/components/Editors/*.editor.tsx (e.g., SettingsEditor)
// - /src/renderer/core/editors/*.editor.tsx (Alternative location for core editors)
const modules = import.meta.glob([
  '/src/extensions/*/renderer/editors/*.editor.tsx', // Extension editors
  '/src/renderer/components/Editors/*.editor.tsx',  // Core editors in components/Editors
  '/src/renderer/core/editors/*.editor.tsx'       // Core editors in core/editors
]);

// Create a map from componentName to the importer function
const editorImporters: Record<string, () => Promise<{ default: ComponentType<unknown> }>> = {};

for (const path in modules) {
  const match = path.match(/\/([^/]+)\.editor\.tsx$/);
  if (match && match[1]) {
    const componentName = match[1];
    if (editorImporters[componentName]) {
      console.warn(`[EditorRegistry] Duplicate editor component name detected: ${componentName}. Check paths: ${path} and others.`);
    }
    
    editorImporters[componentName] = modules[path] as () => Promise<{ default: ComponentType<unknown> }>;
    console.log(`[EditorRegistry] Registered editor importer for: ${componentName}`);
  } else {
    console.warn(`[EditorRegistry] Could not extract component name from path: ${path}`);
  }
}

/**
 * Gets the dynamic import function for a registered editor component.
 * @param componentName The name of the component (e.g., 'SceneEditor', 'SettingsEditor').
 * @returns The importer function or undefined if not found.
 */
export function getEditorImporter(componentName: string): (() => Promise<{
  default: ComponentType<unknown>
}>) | undefined {
  const importer = editorImporters[componentName];
  if (!importer) {
    console.error(`[EditorRegistry] No editor importer found for component name: ${componentName}`);
  }
  return importer;
}

console.log('[EditorRegistry] Initialized with importers:', Object.keys(editorImporters));
