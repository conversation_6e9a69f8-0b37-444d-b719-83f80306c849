import type { ConfigurationInspect, IConfigurationService } from '@shared/types/configuration';
import type { JsonValue } from '@shared/types/common'; // Removed unused Disposable
import type { Event } from '@shared/types/utility-types';
import { Emitter } from '@shared/utils/emitter';
import { IpcChannels } from '@shared/constants/ipc-channels';
import { ipcRendererService } from './ipcRendererService'; // Assuming this service exists for IPC calls

/**
 * Renderer-side implementation of the Configuration Service.
 * Acts as a proxy to the main process service, maintaining a local cache.
 */
class RendererConfigurationService implements IConfigurationService {
  private cache: Map<string, JsonValue> = new Map<string, JsonValue>(); // Added type arguments
  private isCacheInitialized = false;
  private cacheInitializationPromise: Promise<void> | null = null;

  private readonly _onDidChangeConfiguration = new Emitter<{ key: string; newValue: JsonValue }>();
  readonly onDidChangeConfiguration: Event<{ key: string; newValue: JsonValue }> = this._onDidChangeConfiguration.event;

  constructor() {
    this.registerIpcListeners();
    // Initialize cache asynchronously, but don't block constructor
    this.initializeCache();
  }

  private async initializeCache(): Promise<void> {
    if (this.isCacheInitialized) {
      return;
    }
    if (!this.cacheInitializationPromise) {
      console.log('[ConfigService Renderer] Initializing cache...');
      this.cacheInitializationPromise = (async () => {
        try {
          // Fetch initial values? Or rely on individual fetches?
          // For simplicity, let's rely on individual fetches for now.
          // Alternatively, fetch all known declarations/values on startup.
          this.isCacheInitialized = true;
          console.log('[ConfigService Renderer] Cache marked as initialized (lazy loading).');
        } catch (error) {
          console.error('[ConfigService Renderer] Failed to initialize cache:', error);
          // Reset promise to allow retrying?
          this.cacheInitializationPromise = null;
        }
      })();
    }
    return this.cacheInitializationPromise;
  }

  private async ensureCacheInitialized(): Promise<void> {
    if (!this.isCacheInitialized) {
      await this.initializeCache();
    }
  }

  // --- IConfigurationService Implementation ---

  async getValue<T extends JsonValue>(key: string, defaultValue?: T): Promise<T> {
    await this.ensureCacheInitialized();

    if (this.cache.has(key)) {
      return this.cache.get(key) as T;
    }

    // If not in cache, fetch from main process
    console.log(`[ConfigService Renderer] Cache miss for '${key}', fetching from main...`);
    try {
      const value = await ipcRendererService.invoke<JsonValue>(
        IpcChannels.CONFIGURATION_GET_VALUE,
        { key, defaultValue }
      );
      this.cache.set(key, value); // Update cache
      return value as T;
    } catch (error) {
      console.error(`[ConfigService Renderer] Failed to get value for '${key}' from main:`, error);
      // Return provided default on error
      return defaultValue as T;
    }
  }

  async updateValue(key: string, value: JsonValue): Promise<void> {
    await this.ensureCacheInitialized();
    try {
      // Send update request to main process - use null for invoke generic when no value is expected
      await ipcRendererService.invoke<null>(
        IpcChannels.CONFIGURATION_UPDATE_VALUE,
        { key, value }
      );
      // Main process will broadcast CONFIGURATION_DID_CHANGE if successful,
      // which will update our cache via the listener.
      // We could optimistically update the cache here, but waiting for confirmation is safer.
      console.log(`[ConfigService Renderer] Update request sent for '${key}'.`);
    } catch (error) {
      console.error(`[ConfigService Renderer] Failed to send update for '${key}' to main:`, error);
      // Re-throw or handle as needed
      throw error;
    }
  }

  async inspect<T extends JsonValue>(key: string): Promise<ConfigurationInspect<T>> {
    await this.ensureCacheInitialized();
    try {
      const inspectionResult = await ipcRendererService.invoke<ConfigurationInspect<T>>(
        IpcChannels.CONFIGURATION_INSPECT,
        { key }
      );
      return inspectionResult;
    } catch (error) {
      console.error(`[ConfigService Renderer] Failed to inspect '${key}' from main:`, error);
      // Return a default/empty inspection object on error?
      return { key } as ConfigurationInspect<T>; // Return minimal info
    }
  }

  // --- IPC Listener ---
  private registerIpcListeners(): void {
    ipcRendererService.on(IpcChannels.CONFIGURATION_DID_CHANGE, (eventData) => {
      const { key, newValue } = eventData as { key: string; newValue: JsonValue };
      console.log(`[ConfigService Renderer] Received configuration change for '${key}':`, newValue);
      // Update cache
      if (this.cache.get(key) !== newValue) {
        this.cache.set(key, newValue);
        // Fire local event
        this._onDidChangeConfiguration.fire({ key, newValue });
      }
    });
    console.log('[ConfigService Renderer] Registered IPC listeners.');
  }

  dispose(): void {
    // Clean up IPC listeners if ipcRendererService provides an 'off' method
    // ipcRendererService.off(IpcChannels.CONFIGURATION_DID_CHANGE, ...);
    this._onDidChangeConfiguration.dispose();
    this.cache.clear();
    console.log('[ConfigService Renderer] Disposed.');
  }
}

// Export a singleton instance
export const configurationService = new RendererConfigurationService();