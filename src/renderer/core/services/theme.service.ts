import { IpcErrorData } from '@shared/types/ipc';
import { IpcChannels } from '@shared/constants/ipc-channels'; // Импортируем константы
import { ipcRendererService } from './ipcRendererService'; // Correct path relative to services directory

// Используем console для логирования в Renderer
const logger = console;

interface ThemeDefinition {
  id: string;
  label: string;
  type: 'light' | 'dark';
  colors: Record<string, string>;
}

export interface AvailableTheme {
  id: string;
  label: string;
}

// Используем import.meta.glob для статического анализа Vite
const themeModules = import.meta.glob('/src/renderer/themes/*.theme.json', { eager: true });

class ThemeService {
  private availableThemes: AvailableTheme[] = [];
  private themeData = new Map<string, ThemeDefinition>();
  private currentThemeSetting: string | null = null;
  private appliedThemeId: string | null = null;
  private prefersDarkMediaQuery: MediaQueryList;
  private isInitialized = false;
  private settingsListenerUnsubscribe: (() => void) | null = null; // Для отписки от IPC

  constructor() {
    this.prefersDarkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    this.loadThemes();
    logger.info('[ThemeService] Instantiated.');
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    logger.info('[ThemeService] Initializing...');
    this.prefersDarkMediaQuery.addEventListener('change', this.handleSystemThemeChange);
    this.listenForSettingsChanges(); // Start listening
    await this.applyInitialTheme(); // Apply theme based on settings
    this.isInitialized = true;
    logger.info('[ThemeService] Initialized.');
  }

  private loadThemes() {
    logger.info('[ThemeService] Loading themes...');
    this.availableThemes = [];
    this.themeData.clear();

    for (const path in themeModules) {
      const module = themeModules[path] as { default?: ThemeDefinition };
      // Check if module and module.default exist before accessing properties
      const theme = module?.default as ThemeDefinition | undefined;

      if (theme && theme.id && theme.label && theme.type && theme.colors) {
        if (this.themeData.has(theme.id)) {
          logger.warn(`[ThemeService] Duplicate theme ID found: ${theme.id}. Skipping ${path}`);
          continue;
        }
        logger.info(`[ThemeService] Loaded theme: ${theme.label} (${theme.id})`);
        this.availableThemes.push({ id: theme.id, label: theme.label });
        this.themeData.set(theme.id, theme);
      } else {
        logger.warn(`[ThemeService] Invalid theme file format or missing default export: ${path}`, module);
      }
    }
    this.availableThemes.sort((a, b) => a.label.localeCompare(b.label));
    logger.info(`[ThemeService] Loaded ${this.availableThemes.length} themes.`);
  }

  private applyThemeVariables(theme: ThemeDefinition | undefined) {
    if (!theme) {
        logger.warn(`[ThemeService] Attempted to apply undefined theme.`);
        return;
    }
    if (this.appliedThemeId === theme.id) {
        logger.info(`[ThemeService] Theme ${theme.id} is already applied.`);
        return;
    }

    logger.info(`[ThemeService] Applying theme variables: ${theme.label} (${theme.id})`);
    const rootStyle = document.documentElement.style;

    // Сбрасываем переменные предыдущей темы (если она была)
    const previousTheme = this.appliedThemeId ? this.themeData.get(this.appliedThemeId) : undefined;
    if (previousTheme) {
        Object.keys(previousTheme.colors).forEach(key => rootStyle.removeProperty(key));
    }

    // Устанавливаем новые переменные
    for (const [variable, value] of Object.entries(theme.colors)) {
      rootStyle.setProperty(variable, value);
    }
    document.documentElement.setAttribute('data-theme-type', theme.type);
    this.appliedThemeId = theme.id;
  }

  private handleSystemThemeChange = () => {
    if (this.currentThemeSetting === 'system') {
        logger.info('[ThemeService] System theme preference changed, reapplying.');
        this.applyTheme('system'); // Re-apply based on new system preference
    }
  }

  // Applies the theme based on ID ('system' or specific theme ID)
  applyTheme(themeId: string): void {
    this.currentThemeSetting = themeId; // Store the setting value ('system' or specific ID)
    let themeToApply: ThemeDefinition | undefined;

    if (themeId === 'system') {
      const prefersDark = this.prefersDarkMediaQuery.matches;
      logger.info(`[ThemeService] Applying system theme. Prefers dark: ${prefersDark}`);
      const targetType = prefersDark ? 'dark' : 'light';
      // Try finding a default theme first
      const defaultThemeId = targetType === 'dark' ? 'dark-default' : 'light-default';
      themeToApply = this.themeData.get(defaultThemeId);
      // If no default, find the first theme of the target type
      if (!themeToApply) {
          themeToApply = [...this.themeData.values()].find(t => t.type === targetType);
      }
    } else {
      // Directly find the theme by the specific ID
      themeToApply = this.themeData.get(themeId);
    }

    if (themeToApply) {
      this.applyThemeVariables(themeToApply);
    } else {
      logger.error(`[ThemeService] Theme with ID '${themeId}' not found. Falling back to default dark.`);
      // Fallback logic
      const fallbackTheme = this.themeData.get('dark-default') ?? [...this.themeData.values()].find(t => t.type === 'dark');
      if (fallbackTheme) {
          this.applyThemeVariables(fallbackTheme);
      } else {
          logger.error(`[ThemeService] No dark themes found! Cannot apply any theme.`);
      }
    }
  }

  getAvailableThemes(): AvailableTheme[] {
    // Ensure 'System Default' is always first
    return [{ id: 'system', label: 'System Default' }, ...this.availableThemes];
  }

  // Fetches the initial theme setting from the main process
  async applyInitialTheme(): Promise<void> {
      logger.info('[ThemeService] Applying initial theme...');
      let initialThemeId = 'system'; // Default theme
      try {
          // Use new configuration service
          const result = await ipcRendererService.invoke<string>(
            IpcChannels.CONFIGURATION_GET_VALUE, 
            { key: 'workbench.theme', defaultValue: 'system' }
          );
          if (typeof result === 'string' && result) {
              initialThemeId = result;
          } else {
               logger.warn(`[ThemeService] Received invalid value for initial theme setting: ${result}. Using default 'system'.`);
          }
      } catch (error) {
          // Error is already logged by ipcRendererService
          const errorData = error as IpcErrorData;
          logger.error(`[ThemeService] Error fetching initial theme setting: ${errorData.message}. Using default 'system'.`, error);
      }
      this.applyTheme(initialThemeId);
  }

  // Listens for changes to the theme setting from the main process
  listenForSettingsChanges(): void {
      if (this.settingsListenerUnsubscribe) {
          logger.warn('[ThemeService] Settings listener already attached.');
          return; // Prevent duplicate subscriptions
      }
      logger.info('[ThemeService] Listening for theme setting changes...');
      try {
          // Use new configuration service channel
          this.settingsListenerUnsubscribe = ipcRendererService.on<{key: string; newValue: unknown}>(IpcChannels.CONFIGURATION_DID_CHANGE, (payload) => {
               // Check if the changed key is the theme setting
               if (payload && payload.key === 'workbench.theme') {
                   const newThemeId = payload.newValue as string; // Assuming theme is string
                   // Check if theme actually changed to avoid unnecessary reapplications
                   if (newThemeId && newThemeId !== this.currentThemeSetting) {
                       logger.info(`[ThemeService] Theme setting changed to '${newThemeId}', applying...`);
                       this.applyTheme(newThemeId);
                   }
               }
           });
      } catch (error) {
           logger.error("[ThemeService] Failed to subscribe to settings changes:", error);
      }
  }

  // Cleans up listeners
  dispose(): void {
    this.prefersDarkMediaQuery.removeEventListener('change', this.handleSystemThemeChange);
    // Unsubscribe from IPC event
    if (this.settingsListenerUnsubscribe) {
        this.settingsListenerUnsubscribe();
        this.settingsListenerUnsubscribe = null;
        logger.info('[ThemeService] Unsubscribed from settings updates.');
    }
    this.isInitialized = false; // Reset initialization flag
    logger.info('[ThemeService] Disposed.');
  }
}

// Export a singleton instance
export const themeService = new ThemeService();

// Initialization should be called from the Renderer entry point
// e.g., in index.tsx:
// useEffect(() => {
//   themeService.initialize();
//   // Return cleanup function if the service has one
//   return () => themeService.dispose?.();
// }, []);
