import mitt, {Emitter, Hand<PERSON>} from 'mitt';

// Define event types and their payloads
interface Events {
  editorDirtyStateChanged: { tabId: string; isDirty: boolean };
  [key: string]: unknown; // Add index signature for mitt compatibility
  [key: symbol]: unknown; // Add symbol index signature for mitt compatibility
}

class RendererEventBus {
  private emitter: Emitter<Events>;
  
  constructor() {
    this.emitter = mitt<Events>();
    console.log('[RendererEventBus] Initialized.');
  }
  
  on<Key extends keyof Events>(type: Key, handler: Handler<Events[Key]>): () => void {
    this.emitter.on(type, handler);
    // Return unsubscribe function
    return () => this.emitter.off(type, handler);
  }
  
  off<Key extends keyof Events>(type: Key, handler: Handler<Events[Key]>): void {
    this.emitter.off(type, handler);
  }
  
  emit<Key extends keyof Events>(type: Key, event: Events[Key]): void {
    console.log(`[RendererEventBus] Emitting event: ${String(type)}`, event);
    this.emitter.emit(type, event);
  }
}

// Export a singleton instance
export const rendererEventBus = new RendererEventBus();
