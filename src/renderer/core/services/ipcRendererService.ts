import {IpcErrorData, IpcEventArgs, IpcInvokeArgs, IpcInvokeResult, LogMessageData,} from '@shared/types/ipc';
import {IpcChannels} from "@shared/constants/ipc-channels";


const log = (level: string, message: string, details: unknown) => {
  window.electronAPI.invoke(IpcChannels.LOG_MESSAGE, {
    payload: {
      level,
      message,
      details
    }
  } as IpcEventArgs<LogMessageData>)
}

const logger = {
  debug: (message: string, details?: unknown) => log("debug", message, details),
  warn: (message: string, details?: unknown) => log("warn", message, details),
  error: (message: string, error?: unknown) => log("error", message, error),
  info: (message: string, data?: unknown) => log("info", message, data),
};

class IpcRendererService {
  /**
   * Invokes an IPC channel handler in the Main process.
   * Handles argument wrapping and unwraps the standardized result/error format.
   *
   * @param channel The IPC channel name (e.g., 'core:settings.get').
   * @param args The arguments payload for the handler.
   * @returns A promise that resolves with the result data if successful.
   * @throws An error object (IpcErrorData) if the Main process handler returned an error.
   */
  async invoke<TResult = unknown, TArgs = unknown>(
    channel: string,
    args?: TArgs
  ): Promise<TResult> {
    // Check if args already wrapped with payload
    if (args && typeof args === 'object' && 'payload' in args) {
      logger.warn(`[IPC Renderer] Invoke <- ${channel}: Received args with payload.`, args);
    }
    // 1. Wrap arguments into the standard IpcInvokeArgs structure
    const invokeArgs: IpcInvokeArgs<TArgs | undefined> = {payload: args};
    logger.info(`[IPC Renderer] Request -> ${channel}`, invokeArgs);
    
    try {
      // 2. Check if the preload API is available
      if (!window.electronAPI?.invoke) {
        throw new Error('[IPC Renderer] window.electronAPI.invoke is not available. Check preload script.');
      }
      // 3. Call the preload API, passing the *wrapped* arguments.
      // The preload API now expects the wrapped args and returns the raw IpcInvokeResult.
      const result: IpcInvokeResult<TResult> = await window.electronAPI.invoke(
        channel,
        invokeArgs // Pass the wrapped arguments
      );
      
      // 4. Process the standardized result from the Main process
      if (result.success) {
        logger.info(`[IPC Renderer] Success <- ${channel}`, {data: result.data});
        return result.data;
      } else {
        logger.error(`[IPC Renderer] Error <- ${channel}`, result.error);
        // Throw the standardized error object from Main
        throw result.error;
      }
    } catch (error) {
      // Catch errors during the invoke call itself or re-throw the Main error
      logger.error(`[IPC Renderer] Invoke Exception for ${channel}:`, error);
      if (typeof error === 'object' && error !== null && 'code' in error && 'message' in error) {
        // Re-throw if it looks like our standardized IpcErrorData
        throw error as IpcErrorData;
      }
      // Otherwise, wrap it as a generic IPC communication error
      throw {
        code: 'IPC_RENDERER_ERROR',
        message: `[IPC Renderer] Failed to invoke channel "${channel}": ${(error instanceof Error) ? error.message : String(error)}`,
        details: error,
      } as IpcErrorData;
    }
  }
  
  /**
   * Registers a listener for an event sent from the Main process.
   *
   * @param channel The IPC channel name (e.g., 'core:settings.changed').
   * @param listener The callback function to execute when the event is received.
   *                 It receives the unwrapped data payload.
   * @returns A function to remove the listener.
   */
  on<TData = unknown>(
    channel: string,
    listener: (data: TData) => void // The listener provided by the consuming code expects only the unwrapped data
  ): () => void {
    if (!window.electronAPI?.onMainEvent) {
      logger.error(`[IPC Renderer] window.electronAPI.onMainEvent is not available for channel ${channel}. Check preload script.`);
      return () => {
        // No-op unsubscribe function when electronAPI is not available
      }; // Return no-op unsubscribe function
    }
    
    logger.info(`[IPC Renderer] Registering listener for channel: ${channel}`);
    
    // 1. Create a wrapper listener that matches the signature expected by the updated preload API
    //    (which is the same as ipcRenderer.on: event + args)
    const wrappedListener = (
      _event: Electron.IpcRendererEvent, // We don't typically need the event object here
      eventArgs: IpcEventArgs<TData> // The raw args received from Main
    ): void => {
      // 2. Extract the payload and call the original listener
      logger.info(`[IPC Renderer] Request <- ${channel}`, eventArgs);
      // Provide a default empty object if payload is missing, though it shouldn't happen with proper Main process sending
      listener(eventArgs?.payload ?? ({} as TData));
      if (eventArgs && typeof eventArgs?.payload === 'undefined') {
        logger.warn(`[IPC Renderer] Event <- ${channel}: Received event without expected payload structure.`, eventArgs);
      }
       logger.info(`[IPC Renderer] Success -> ${channel}`);
    };
    
    // 3. Register the wrapped listener using the preload API
    //    The preload API now simply proxies ipcRenderer.on
    const unsubscribe = window.electronAPI.onMainEvent(channel, wrappedListener);
    
    // 4. Return the unsubscribe function provided by the preload API
    return () => {
      unsubscribe(); // This will call ipcRenderer.removeListener(channel, wrappedListener) via preload
      logger.info(`[IPC Renderer] Unsubscribed listener for channel: ${channel}`);
    };
  }
  
  // Removed the send method as it's not exposed via preload and discouraged.
  // Logging from renderer should ideally use a dedicated invoke channel like 'core:log.message'.
}

// Export a singleton instance
export const ipcRendererService = new IpcRendererService();
