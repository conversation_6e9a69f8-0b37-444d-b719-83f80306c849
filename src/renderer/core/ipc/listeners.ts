import {ipcRendererService} from "@renderer/core/services/ipcRendererService";
import {useWorkbenchStore} from "@renderer/state/workbenchStore";
import {useEditorTabsStore} from "@renderer/state/editorTabsStore";
import {useNotificationStore} from "@renderer/state/notificationStore";
import {IpcChannels} from "@shared/constants/ipc-channels";
import {CommandIds} from "@shared/constants/command-ids";
import {NotificationOptions} from "@shared/types/ui";
import type {
  ContextChangedEventData,
  InputDialogOptions as IpcInputDialogOptions,
  SettingsChangedEventData,
  WorkbenchOpenTabEventData,
} from "@shared/types/ipc";
import type { EditorTab } from "@shared/types/ui";

/**
 * Registers all IPC event listeners for the renderer process.
 * Connects IPC events to Zustand store actions.
 * @returns A function to unsubscribe all listeners.
 */
export function registerIpcListeners(): () => void {
  const unsubscribers: (() => void)[] = [];
  console.log("[IPC Listeners] Registering listeners...");
  
  try {
    // --- Workbench Actions ---
    unsubscribers.push(
      ipcRendererService.on<WorkbenchOpenTabEventData>(
        IpcChannels.WORKBENCH_OPEN_TAB,
        (tabData) => {
          console.log(`[IPC] Received ${IpcChannels.WORKBENCH_OPEN_TAB}`, tabData);
          // Convert EditorTabData to EditorTab
          const editorTab: EditorTab = {
            ...tabData,
            icon: tabData.icon || '', // Convert optional icon to required icon
            isDirty: tabData.isDirty || false, // Ensure isDirty is defined
          };
          // Call the action from the correct store
          useEditorTabsStore.getState().openOrFocusTab(editorTab);
        }
      )
    );
    
    unsubscribers.push(
      ipcRendererService.on<IpcInputDialogOptions>(
        IpcChannels.WORKBENCH_SHOW_INPUT_DIALOG,
        (ipcOptions) => {
          console.log(
            `[IPC] Received ${IpcChannels.WORKBENCH_SHOW_INPUT_DIALOG} ID: ${ipcOptions.dialogId}`,
            ipcOptions
          );
          useWorkbenchStore.getState().openInputDialog(ipcOptions);
        }
      )
    );
    
    unsubscribers.push(
      ipcRendererService.on(IpcChannels.WORKBENCH_TOGGLE_PALETTE, () => {
        console.log(`[IPC] Received ${IpcChannels.WORKBENCH_TOGGLE_PALETTE}`);
        useWorkbenchStore.getState().togglePalette();
      })
    );
    
    unsubscribers.push(
      ipcRendererService.on(IpcChannels.WORKBENCH_TOGGLE_SIDEBAR, () => {
        console.log(`[IPC] Received ${IpcChannels.WORKBENCH_TOGGLE_SIDEBAR}`);
        useWorkbenchStore.getState().toggleSidebarVisibility();
      })
    );
    
    unsubscribers.push(
      ipcRendererService.on(IpcChannels.WORKBENCH_TOGGLE_PANEL, () => {
        console.log(`[IPC] Received ${IpcChannels.WORKBENCH_TOGGLE_PANEL}`);
        useWorkbenchStore.getState().togglePanelVisibility();
      })
    );
    
    // --- Notifications ---
    unsubscribers.push(
      ipcRendererService.on<NotificationOptions>(
        IpcChannels.WORKBENCH_SHOW_NOTIFICATION,
        (payload) => {
          console.log(`[IPC] Received ${IpcChannels.WORKBENCH_SHOW_NOTIFICATION}`, payload);
          if (payload && payload.message && payload.type) {
            useNotificationStore.getState().addNotification({
              message: payload.message,
              type: payload.type,
              duration: payload.duration,
            });
          } else {
            console.warn("[IPC] Received invalid notification data:", payload);
          }
        }
      )
    );
    
    // --- Context & Settings Changes (Example: Log for now) ---
    // You might want to update specific parts of your state based on these
    unsubscribers.push(
      ipcRendererService.on<ContextChangedEventData>(
        IpcChannels.CONTEXT_CHANGED,
        (payload) => {
          console.log(
            `[IPC] Received ${IpcChannels.CONTEXT_CHANGED}: ${payload.key}=${payload.value}`
          );
          // TODO: Potentially update relevant state if needed
        }
      )
    );
    
    unsubscribers.push(
      ipcRendererService.on<SettingsChangedEventData>(
        IpcChannels.SETTINGS_CHANGED,
        (payload) => {
          console.log(
            `[IPC] Received ${IpcChannels.SETTINGS_CHANGED}: ${payload.key}=${payload.newValue}`
          );
          // TODO: Potentially update relevant state or trigger re-fetch of settings
        }
      )
    );
    
    // --- Window State Changes ---
    unsubscribers.push(
      ipcRendererService.on(IpcChannels.WINDOW_MAXIMIZED_EVENT, () => {
        console.log(`[IPC] Received ${IpcChannels.WINDOW_MAXIMIZED_EVENT}`);
        useWorkbenchStore.getState().setWindowMaximized(true);
      })
    );
    
    unsubscribers.push(
      ipcRendererService.on(IpcChannels.WINDOW_UNMAXIMIZED_EVENT, () => {
        console.log(`[IPC] Received ${IpcChannels.WINDOW_UNMAXIMIZED_EVENT}`);
        useWorkbenchStore.getState().setWindowMaximized(false);
      })
    );
    
    // --- Save All Request (Simplified for MVP without dirty tracking) ---
    unsubscribers.push(
      ipcRendererService.on(IpcChannels.WORKBENCH_REQUEST_SAVE_ALL, () => {
        console.log(`[IPC] Received ${IpcChannels.WORKBENCH_REQUEST_SAVE_ALL}. Triggering save for active editor.`);
        // Get activeTabId from the correct store
        const activeTabId = useEditorTabsStore.getState().activeTabId;
        if (activeTabId) {
          // Trigger the save command (Main process will use its context to find the editor)
          ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, {
              commandId: CommandIds.TRIGGER_SAVE_ACTIVE_EDITOR,
              args: {}
            })
            .catch(err => console.error(`[IPC Listeners] Error invoking ${CommandIds.TRIGGER_SAVE_ACTIVE_EDITOR} on saveAll request:`, err));
        } else {
          console.log(`[IPC Listeners] No active editor to save on saveAll request.`);
        }
      })
    );
    
  } catch (error) {
    console.error("[IPC Listeners] Error setting up IPC event listeners:", error);
  }
  
  console.log(`[IPC Listeners] Registered ${unsubscribers.length} listeners.`);
  
  // Return a function that calls all unsubscribe functions
  return () => {
    console.log("[IPC Listeners] Cleaning up listeners...");
    unsubscribers.forEach((unsub) => unsub());
  };
}
