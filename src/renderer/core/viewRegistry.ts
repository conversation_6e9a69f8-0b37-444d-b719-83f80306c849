/**
 * This module uses Vite's import.meta.glob to eagerly find all potential view components
 * and provides a function to retrieve the dynamic importer for a given component name.
 */

// Use import.meta.glob to find all .view.tsx files within extensions' and core components' directories
// The `eager: false` option (default) ensures dynamic imports.
const modules = import.meta.glob([
  '/src/extensions/*/renderer/views/*.view.tsx', // Extension views
  '/src/renderer/components/**/views/*.view.tsx'  // Core views (e.g., in Panel/views)
]);

// Create a map from componentName to the importer function
const viewImporters: Record<string, () => Promise<{ default: React.ComponentType<unknown> }>> = {};

for (const path in modules) {
  // Extract componentName from the path.
  // Example path: /src/extensions/books/renderer/views/BooksExplorerView.view.tsx
  // We want to extract "BooksExplorerView"
  const match = path.match(/\/([^/]+)\.view\.tsx$/);
  if (match && match[1]) {
    const componentName = match[1];
    if (viewImporters[componentName]) {
      console.warn(`[ViewRegistry] Duplicate view component name detected: ${componentName}. Check paths: ${path} and others.`);
      // Decide on handling duplicates - overwrite or throw error? Overwriting for now.
    }
    viewImporters[componentName] = modules[path] as () => Promise<{ default: React.ComponentType<unknown> }>;
    console.log(`[ViewRegistry] Registered view importer for: ${componentName}`);
  } else {
    console.warn(`[ViewRegistry] Could not extract component name from path: ${path}`);
  }
}

/**
 * Gets the dynamic import function for a registered view component.
 * @param componentName The name of the component (e.g., 'BooksExplorerView').
 * @returns The importer function or undefined if not found.
 */
export function getViewImporter(componentName: string): (() => Promise<{ default: React.ComponentType<unknown> }>) | undefined {
  const importer = viewImporters[componentName];
  if (!importer) {
    console.error(`[ViewRegistry] No view importer found for component name: ${componentName}`);
  }
  return importer;
}

console.log('[ViewRegistry] Initialized with importers:', Object.keys(viewImporters));