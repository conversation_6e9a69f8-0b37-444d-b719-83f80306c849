import React, { useEffect, useState } from 'react';
import { ipcRendererService } from '../services/ipcRendererService';
import { ErrorInfo, ErrorSeverity } from '@shared/types/error-handling';
import { IpcChannels } from '@shared/constants/ipc-channels';

interface ErrorMonitorProps {
  onHealthChange?: (health: number) => void;
}

/**
 * Компонент для мониторинга ошибок и состояния здоровья приложения
 */
export const ErrorMonitor: React.FC<ErrorMonitorProps> = ({ onHealthChange }) => {
  const [errors, setErrors] = useState<ErrorInfo[]>([]);
  const [health, setHealth] = useState<number>(1); // 1 = 100% здоровье
  const [showErrors, setShowErrors] = useState<boolean>(false);

  useEffect(() => {
    // Подписываемся на обновления ошибок
    const errorUpdateHandler = (errorInfo: ErrorInfo) => {
      setErrors(prevErrors => {
        // Обновляем существующую ошибку или добавляем новую
        const index = prevErrors.findIndex(e => e.id === errorInfo.id);
        if (index !== -1) {
          const newErrors = [...prevErrors];
          newErrors[index] = errorInfo;
          return newErrors;
        } else {
          return [...prevErrors, errorInfo];
        }
      });
    };

    // Подписываемся на обновления здоровья
    const healthUpdateHandler = (data: { health: number }) => {
      console.log('Health update received:', data.health);
      setHealth(data.health);
      if (onHealthChange) {
        onHealthChange(data.health);
      }
    };

    // Регистрируем обработчики и получаем функции для отписки
    const unsubscribeError = ipcRendererService.on(IpcChannels.ERROR_UPDATE, errorUpdateHandler);
    const unsubscribeHealth = ipcRendererService.on(IpcChannels.ERROR_HEALTH_UPDATE, healthUpdateHandler);

    // Запрашиваем текущее состояние здоровья
    // ipcRendererService.invoke(IpcChannels.ERROR_GET_HEALTH).then(setHealth).catch(console.error);

    // Отписываемся при размонтировании
    return () => {
      unsubscribeError();
      unsubscribeHealth();
    };
  }, [onHealthChange]);

  // Фильтруем ошибки, показываем только последние 10 и только критические/высокие
  const filteredErrors = errors
    .filter(e => e.severity === ErrorSeverity.CRITICAL || e.severity === ErrorSeverity.HIGH)
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, 10);

  // Если нет ошибок или здоровье 100%, не показываем компонент
  if (filteredErrors.length === 0 && health === 1) {
    return null;
  }

  // Определяем цвет индикатора здоровья
  const getHealthColor = () => {
    if (health > 0.8) return 'green';
    if (health > 0.5) return 'orange';
    return 'red';
  };

  return (
    <div className="error-monitor">
      <div
        className="health-indicator"
        style={{
          backgroundColor: getHealthColor(),
          cursor: 'pointer',
          padding: '4px 8px',
          borderRadius: '4px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}
        onClick={() => setShowErrors(!showErrors)}
      >
        <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: getHealthColor() }} />
        <span>Здоровье: {Math.round(health * 100)}%</span>
        {filteredErrors.length > 0 && (
          <span style={{ marginLeft: '8px', fontWeight: 'bold' }}>
            ({filteredErrors.length})
          </span>
        )}
      </div>

      {showErrors && filteredErrors.length > 0 && (
        <div className="error-list" style={{
          position: 'absolute',
          bottom: '40px',
          right: '10px',
          width: '400px',
          maxHeight: '300px',
          overflowY: 'auto',
          backgroundColor: '#fff',
          border: '1px solid #ccc',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          zIndex: 1000
        }}>
          <div style={{ padding: '8px', borderBottom: '1px solid #eee', fontWeight: 'bold' }}>
            Последние ошибки
          </div>
          {filteredErrors.map(error => (
            <div key={error.id} style={{
              padding: '8px',
              borderBottom: '1px solid #eee',
              backgroundColor: error.severity === ErrorSeverity.CRITICAL ? '#fff0f0' : '#fffaf0'
            }}>
              <div style={{ fontWeight: 'bold', color: error.severity === ErrorSeverity.CRITICAL ? 'red' : 'orange' }}>
                [{error.severity.toUpperCase()}] {new Date(error.timestamp).toLocaleTimeString()}
              </div>
              <div>{error.message}</div>
              <div style={{ fontSize: '0.8em', color: '#666' }}>Источник: {error.source}</div>
              {error.recoveryAttempted && (
                <div style={{
                  fontSize: '0.8em',
                  color: error.recoverySuccessful ? 'green' : 'red',
                  marginTop: '4px'
                }}>
                  {error.recoverySuccessful
                    ? '✓ Восстановление успешно'
                    : '✗ Восстановление не удалось'}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
