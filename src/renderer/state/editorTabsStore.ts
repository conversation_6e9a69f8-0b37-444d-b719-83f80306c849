import {create} from 'zustand';
import {subscribeWithSelector} from 'zustand/middleware';
import {ipcRendererService} from '@renderer/core/services/ipcRendererService';
import {rendererEventBus} from '@renderer/core/services/RendererEventBus';
import {IpcChannels} from '@shared/constants/ipc-channels';
import {CommandIds} from '@shared/constants/command-ids';
import type {
  ContextSetValueArgs,
  DialogShowSaveConfirmationArgs,
  DialogShowSaveConfirmationResult
} from '@shared/types/ipc';
import { EditorTab } from '@shared/types/ui';

// Re-export EditorTab for components
export type { EditorTab };


interface EditorTabsState {
  openTabs: EditorTab[];
  activeTabId: string | null;
  
  // Actions
  setTabsState: (tabs: EditorTab[], activeId: string | null) => void;
  openOrFocusTab: (tabData: EditorTab) => void;
  closeTab: (tabIdToClose: string) => Promise<boolean>; // Returns true if closed, false if cancelled
  setActiveTabId: (tabId: string | null) => Promise<boolean>; // Returns true if switched, false if cancelled
  reorderTabs: (dragIndex: number, hoverIndex: number) => void;
  setTabDirty: (tabId: string, isDirty: boolean) => void;
}

// Helper to trigger save command
const triggerSaveCommand = async (): Promise<void> => { // Keep async
  console.log(`[EditorTabsStore] Triggering save command`);
  try {
    // Invoke the command and return the promise
    await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, {
      commandId: CommandIds.TRIGGER_SAVE_ACTIVE_EDITOR,
      args: {}
    });
    // TODO: Implement waiting for save completion event
    console.log(`[EditorTabsStore] Save command triggered (invoke sent)`);
  } catch (err) {
    console.error(`Failed to trigger save command:`, err);
    // Re-throw the error so the caller (closeTab/setActiveTabId) knows it failed
    throw err;
  }
};

// Helper to update main process context
const updateMainContext = (activeTab: EditorTab | null | undefined) => {
  const contextValue = activeTab
    ? {editorType: activeTab.editorType, dataId: activeTab.dataId}
    : null;
  ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, {
    commandId: CommandIds.CORE_CONTEXT_SET,
    args: {key: 'activeEditorContext', value: contextValue} as ContextSetValueArgs
  }).catch(err => console.error("Failed to set active editor context:", err));
};


export const useEditorTabsStore = create(
  subscribeWithSelector<EditorTabsState>((set, get) => ({
    openTabs: [],
    activeTabId: null,
    
    setTabsState: (tabs, activeId) => {
      const cleanTabs = tabs.map(tab => ({...tab, isDirty: false}));
      set({openTabs: cleanTabs, activeTabId: activeId});
      updateMainContext(cleanTabs.find(t => t.id === activeId));
    },
    
    openOrFocusTab: (tabData) => {
      const currentTabs = get().openTabs;
      const existingTab = currentTabs.find(tab => tab.id === tabData.id);
      if (existingTab) {
        existingTab.isDirty = false;
        get().setActiveTabId(existingTab.id);
      } else {
        const newTab: EditorTab = {...tabData, isDirty: false};
        set({
          openTabs: [...currentTabs, newTab],
          activeTabId: newTab.id,
        });
        updateMainContext(newTab);
      }
    },
    
    closeTab: async (tabIdToClose: string): Promise<boolean> => {
      const tabToClose = get().openTabs.find(tab => tab.id === tabIdToClose);
      let proceedToClose = true;
      
      if (tabToClose && tabToClose.isDirty) {
        console.log(`[EditorTabsStore closeTab] Tab ${tabIdToClose} is dirty, showing confirmation...`);
        try {
          const result = await ipcRendererService.invoke<DialogShowSaveConfirmationResult, DialogShowSaveConfirmationArgs>(
            IpcChannels.DIALOG_SHOW_SAVE_CONFIRMATION,
            {message: `Сохранить изменения в "${tabToClose.title}"?`}
          );
          
          if (result === 'save') {
            await triggerSaveCommand();
            // TODO: Wait for save completion
          } else if (result === 'cancel') {
            proceedToClose = false;
          }
        } catch (err) {
          console.error(`[EditorTabsStore closeTab] Error showing/handling save confirmation for ${tabIdToClose}:`, err);
          proceedToClose = false; // Don't close on error
        }
      }
      
      if (!proceedToClose) {
        return false; // Indicate closure was cancelled
      }
      
      // Proceed with closing the tab state
      set(state => {
        const newTabs = state.openTabs.filter(tab => tab.id !== tabIdToClose);
        let newActiveId = state.activeTabId;
        if (state.activeTabId === tabIdToClose) {
          const indexToClose = state.openTabs.findIndex(tab => tab.id === tabIdToClose);
          newActiveId = newTabs[indexToClose]?.id ?? newTabs[indexToClose - 1]?.id ?? newTabs[0]?.id ?? null;
        }
        updateMainContext(newTabs.find(tab => tab.id === newActiveId));
        return {openTabs: newTabs, activeTabId: newActiveId};
      });
      return true; // Indicate closure happened
    },
    
    setActiveTabId: async (tabId: string | null): Promise<boolean> => {
      const previousActiveTabId = get().activeTabId;
      if (previousActiveTabId === tabId) return true;
      
      const previousTab = get().openTabs.find(tab => tab.id === previousActiveTabId);
      let canSwitch = true;
      
      if (previousTab && previousTab.isDirty) {
        console.log(`[EditorTabsStore setActiveTabId] Previous tab ${previousActiveTabId} is dirty, showing confirmation...`);
        try {
          const result = await ipcRendererService.invoke<DialogShowSaveConfirmationResult, DialogShowSaveConfirmationArgs>(
            IpcChannels.DIALOG_SHOW_SAVE_CONFIRMATION,
            {message: `Сохранить изменения в "${previousTab.title}"?`}
          );
          if (result === 'save') {
            await triggerSaveCommand();
            // TODO: Wait for save completion
          } else if (result === 'cancel') {
            canSwitch = false;
          }
        } catch (err) {
          console.error(`[EditorTabsStore setActiveTabId] Error showing/handling save confirmation for ${previousActiveTabId}:`, err);
          canSwitch = false;
        }
      }
      
      if (!canSwitch) {
        return false; // Indicate switch was cancelled
      }
      
      set({activeTabId: tabId});
      updateMainContext(get().openTabs.find(tab => tab.id === tabId));
      return true; // Indicate switch happened
    },
    
    reorderTabs: (dragIndex, hoverIndex) => set(state => {
      const newTabs = [...state.openTabs];
      const [draggedTab] = newTabs.splice(dragIndex, 1);
      newTabs.splice(hoverIndex, 0, draggedTab);
      return {openTabs: newTabs};
    }),
    
    setTabDirty: (tabId, isDirty) => set(state => ({
      openTabs: state.openTabs.map(tab =>
        tab.id === tabId ? {...tab, isDirty} : tab
      ),
    })),
    
  }))
);

// --- Subscribe to Renderer Event Bus ---
rendererEventBus.on('editorDirtyStateChanged', ({tabId, isDirty}) => {
  console.log(`[EditorTabsStore] Received editorDirtyStateChanged event:`, {tabId, isDirty});
  useEditorTabsStore.getState().setTabDirty(tabId, isDirty);
});

// --- Selectors ---
export const useOpenTabs = () => useEditorTabsStore((state) => state.openTabs);
export const useActiveTabId = () => useEditorTabsStore((state) => state.activeTabId);
