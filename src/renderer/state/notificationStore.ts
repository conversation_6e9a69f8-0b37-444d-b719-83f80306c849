import { create } from 'zustand';
import type { Notification } from '@renderer/components/Notifications/NotificationToast'; // Импортируем тип

export interface NotificationState {
  activeNotifications: Notification[]; // For popups
  notificationHistory: Notification[]; // For panel view
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeActiveNotification: (id: number) => void; // Removes from popups
  removeNotificationFromHistory: (id: number) => void; // Removes from history panel
  clearNotificationHistory: () => void; // Clears the history panel
}

let nextId = 0;
const DEFAULT_DURATION = 2000; // 2 seconds

export const useNotificationStore = create<NotificationState>((set, get) => ({
  activeNotifications: [],
  notificationHistory: [],

  addNotification: (notificationData) => {
    const newNotification: Notification = {
      ...notificationData,
      id: nextId++,
    };

    // Add to both lists (history first, then active for potential immediate display)
    set((state) => ({
        notificationHistory: [newNotification, ...state.notificationHistory],
        activeNotifications: [newNotification, ...state.activeNotifications]
    }));

    // Determine duration for auto-removal from active list
    const effectiveDuration = (notificationData.duration && notificationData.duration > 0)
        ? notificationData.duration
        : DEFAULT_DURATION;

    // Start timer to remove from *active* list only
    // Errors might have duration = 0 or undefined, meaning they persist until manually closed
    if (effectiveDuration > 0) { // Only set timeout if duration is positive
        setTimeout(() => {
            get().removeActiveNotification(newNotification.id); // Call the specific removal action
        }, effectiveDuration);
    }
  },

  removeActiveNotification: (id) => set((state) => ({
    activeNotifications: state.activeNotifications.filter(n => n.id !== id),
  })),

  removeNotificationFromHistory: (id) => set((state) => ({
    notificationHistory: state.notificationHistory.filter(n => n.id !== id),
  })),

  clearNotificationHistory: () => set({ notificationHistory: [] }),
}));
