import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

import {
    InputDialogOptions as IpcInputDialogOptions,
    DialogInputResponseData,
} from '@shared/types/ipc';
import { IpcChannels } from '@shared/constants/ipc-channels';
import { ipcRendererService } from '@renderer/core/services/ipcRendererService';
import type { InputDialogOptions as ComponentInputDialogOptions } from '@renderer/components/InputDialog/InputDialog';

export interface WorkbenchState {
  activeViewContainerId: string | null;
  isPaletteOpen: boolean;
  inputDialog: {
    isOpen: boolean;
    dialogId: string | null;
    options: Omit<ComponentInputDialogOptions, 'isOpen' | 'onConfirm' | 'onCancel'> | null;
  };
  isSidebarVisible: boolean;
  isPanelVisible: boolean;
  sidebarWidth: number;
  panelHeight: number;
  isWindowMaximized: boolean;

  setActiveViewContainerId: (containerId: string | null) => void;
  togglePalette: () => void;
  openInputDialog: (options: IpcInputDialogOptions) => void;
  confirmInputDialog: (value: string) => void;
  cancelInputDialog: () => void;
  toggleSidebarVisibility: () => void;
  togglePanelVisibility: () => void;
  setSidebarWidth: (width: number) => void;
  setPanelHeight: (height: number) => void;
  // Update type to include visibility flags
  restoreLayoutState: (layoutState?: { sidebarWidth?: number; panelHeight?: number; isSidebarVisible?: boolean; isPanelVisible?: boolean }) => void;
  setWindowMaximized: (isMaximized: boolean) => void;
}

export const useWorkbenchStore = create(
  subscribeWithSelector<WorkbenchState>((set, get) => ({
    activeViewContainerId: null,
    isPaletteOpen: false,
    inputDialog: { isOpen: false, dialogId: null, options: null },
    isSidebarVisible: true,
    isPanelVisible: true,
    sidebarWidth: 250,
    panelHeight: 150,
    isWindowMaximized: false,

    setActiveViewContainerId: (containerId) => set({ activeViewContainerId: containerId }),

    togglePalette: () => set(state => ({ isPaletteOpen: !state.isPaletteOpen })),

    openInputDialog: (ipcOptions) => {
        const componentOptions: Omit<ComponentInputDialogOptions, 'isOpen' | 'onConfirm' | 'onCancel'> = {
            title: ipcOptions.title,
            prompt: ipcOptions.prompt,
            defaultValue: ipcOptions.defaultValue,
        };
        set({ inputDialog: { isOpen: true, dialogId: ipcOptions.dialogId, options: componentOptions } });
    },

    confirmInputDialog: (value) => {
        const currentDialogId = get().inputDialog.dialogId;
        if (currentDialogId !== null) {
            const responsePayload: DialogInputResponseData = { dialogId: currentDialogId, value };
            ipcRendererService.invoke(IpcChannels.DIALOG_INPUT_RESPONSE, responsePayload)
                .catch(err => console.error("[WorkbenchStore] Error sending input dialog confirm response:", err));
            set({ inputDialog: { isOpen: false, dialogId: null, options: null } });
        }
    },

    cancelInputDialog: () => {
        const currentDialogId = get().inputDialog.dialogId;
        if (currentDialogId !== null) {
            const responsePayload: DialogInputResponseData = { dialogId: currentDialogId, value: null };
            ipcRendererService.invoke(IpcChannels.DIALOG_INPUT_RESPONSE, responsePayload)
                .catch(err => console.error("[WorkbenchStore] Error sending input dialog cancel response:", err));
            set({ inputDialog: { isOpen: false, dialogId: null, options: null } });
        }
    },

    toggleSidebarVisibility: () => set(state => ({ isSidebarVisible: !state.isSidebarVisible })),
    togglePanelVisibility: () => set(state => ({ isPanelVisible: !state.isPanelVisible })),

    setSidebarWidth: (width) => set({ sidebarWidth: Math.max(100, width) }),
    setPanelHeight: (height) => set({ panelHeight: Math.max(50, height) }),

    // Restore full layout state including visibility
    restoreLayoutState: (layoutState) => {
        const updates: Partial<Pick<WorkbenchState, 'sidebarWidth' | 'panelHeight' | 'isSidebarVisible' | 'isPanelVisible'>> = {};
        if (layoutState?.sidebarWidth !== undefined) {
            updates.sidebarWidth = Math.max(100, layoutState.sidebarWidth);
        }
        if (layoutState?.panelHeight !== undefined) {
            updates.panelHeight = Math.max(50, layoutState.panelHeight);
        }
        if (layoutState?.isSidebarVisible !== undefined) {
            updates.isSidebarVisible = layoutState.isSidebarVisible;
        }
        if (layoutState?.isPanelVisible !== undefined) {
            updates.isPanelVisible = layoutState.isPanelVisible;
        }
        if (Object.keys(updates).length > 0) {
            set(updates);
        }
        console.log('[WorkbenchStore] Layout state restored:', {
            sidebarWidth: get().sidebarWidth,
            panelHeight: get().panelHeight,
            isSidebarVisible: get().isSidebarVisible,
            isPanelVisible: get().isPanelVisible,
        });
    },

    setWindowMaximized: (isMaximized) => set({ isWindowMaximized: isMaximized }),
  }))
);

export const useActiveViewContainerId = () => useWorkbenchStore((state) => state.activeViewContainerId);
export const useIsPaletteOpen = () => useWorkbenchStore((state) => state.isPaletteOpen);
export const useInputDialogState = () => useWorkbenchStore((state) => state.inputDialog);
export const useIsSidebarVisible = () => useWorkbenchStore((state) => state.isSidebarVisible);
export const useIsPanelVisible = () => useWorkbenchStore((state) => state.isPanelVisible);
export const useSidebarWidth = () => useWorkbenchStore((state) => state.sidebarWidth);
export const usePanelHeight = () => useWorkbenchStore((state) => state.panelHeight);
export const useIsWindowMaximized = () => useWorkbenchStore((state) => state.isWindowMaximized);
