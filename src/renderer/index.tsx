import React, { useEffect } from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import { Workbench } from "@renderer/components/Workbench/Workbench";
import { TitleBar } from "@renderer/components/TitleBar/TitleBar";
import { themeService } from "@renderer/core/services/theme.service";
import { registerIpcListeners } from "@renderer/core/ipc/listeners"; // Импортируем регистратор слушателей

// Компонент для инициализации сервисов Renderer
const AppInitializer: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  useEffect(() => {
    console.log(
      "[AppInitializer] Initializing renderer services and listeners..."
    );

    themeService.initialize();
    const unsubscribeIpc = registerIpcListeners(); // Регистрируем слушатели IPC

    // Здесь можно инициализировать другие сервисы Renderer

    // Возвращаем функцию очистки для слушателей IPC
    return () => {
      console.log("[AppInitializer] Cleaning up IPC listeners...");
      unsubscribeIpc();
      // themeService.dispose?.(); // Если есть метод очистки для themeService
    };
  }, []); // Пустой массив зависимостей для выполнения один раз при монтировании

  return <>{children}</>;
};

const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Root element #root not found in index.html");
}

const root = ReactDOM.createRoot(rootElement);
// Оборачиваем приложение в AppInitializer
root.render(
  <AppInitializer>
    <div style={{ display: "flex", flexDirection: "column", height: "100vh" }}>
      <TitleBar />
      <Workbench />
    </div>
  </AppInitializer>
);

console.log("👋 React application started in renderer process.");
