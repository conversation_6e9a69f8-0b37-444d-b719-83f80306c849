import React, { useCallback, useEffect, useRef, useState } from "react";
import type { AppMenuItem } from "@shared/types/menu";
import { CommandsExecuteArgs, IpcErrorData } from "@shared/types/ipc";
import { IpcChannels } from "@shared/constants/ipc-channels";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import "./MenuBar.css";

// Компонент для одного пункта подменю
interface SubmenuItemProps {
  item: AppMenuItem;
  onExecute: () => void; // Колбэк для закрытия меню после выполнения
}

const SubmenuItem: React.FC<SubmenuItemProps> = ({ item, onExecute }) => {
  const handleClick = () => {
    if (item.commandId) {
      console.log(
        `Executing menu command via ${IpcChannels.COMMANDS_EXECUTE}: ${item.commandId}`
      ); // Используем константу в логе
      // Используем новый сервис и канал
      const args: CommandsExecuteArgs = { commandId: item.commandId, args: {} };
      ipcRendererService
        .invoke(IpcChannels.COMMANDS_EXECUTE, args) // Используем константу
        .then(() => {
          // isIpcError не нужен, invoke выбросит ошибку
          console.log(
            `Command ${item.commandId} executed successfully via menu.`
          );
        })
        .catch((err: unknown) => {
          // Ошибка уже залогирована ipcRendererService
          const errorData = err as IpcErrorData;
          console.error(
            `Error executing command ${item.commandId} via menu:`,
            errorData
          );
          alert(`Error: ${errorData.message || "Failed to execute command"}`);
        })
        .finally(onExecute); // Закрываем меню в любом случае
    } else if (item.role) {
      // Роли Electron обрабатываются нативно, здесь ничего не делаем
      // или через специальные IPC, если нужно дублировать
      console.warn(
        `Role-based menu item '${item.role}' clicked, but not handled in custom menu.`
      );
      onExecute();
    } else {
      onExecute(); // Просто закрываем меню, если нет действия
    }
  };

  if (item.type === "separator") {
    return <li className="submenu-item separator" />;
  }

  return (
    <li className="submenu-item" onClick={handleClick}>
      {item.label}
      {/* TODO: Отображение горячих клавиш (accelerator) */}
    </li>
  );
};

// Компонент для одного пункта верхнего уровня меню
interface MenuBarItemProps {
  item: AppMenuItem;
  isActive: boolean;
  onClick: () => void;
  onCloseRequest: () => void; // Запрос на закрытие всех меню
}

const MenuBarItem: React.FC<MenuBarItemProps> = ({
  item,
  isActive,
  onClick,
  onCloseRequest,
}) => {
  const menuRef = useRef<HTMLUListElement>(null);

  // Закрытие подменю при клике вне его
  useEffect(() => {
    if (!isActive) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onCloseRequest();
      }
    };
    // Небольшая задержка перед добавлением слушателя, чтобы не сработал на тот же клик, что открыл меню
    const timerId = setTimeout(() => {
      document.addEventListener("mousedown", handleClickOutside);
    }, 0);

    return () => {
      clearTimeout(timerId);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isActive, onCloseRequest]);

  return (
    <div
      className={`menu-bar-item ${isActive ? "active" : ""}`}
      onClick={onClick}
    >
      {item.label}
      {isActive && item.submenu && (
        <ul className="submenu" ref={menuRef}>
          {item.submenu.map((subItem, index) => (
            <SubmenuItem
              key={subItem.commandId || subItem.label || `sep-${index}`}
              item={subItem}
              onExecute={onCloseRequest}
            />
          ))}
        </ul>
      )}
    </div>
  );
};

// Основной компонент MenuBar
export const MenuBar: React.FC = () => {
  const [menuStructure, setMenuStructure] = useState<AppMenuItem[]>([]);
  const [activeMenu, setActiveMenu] = useState<string | null>(null); // Храним label активного меню

  useEffect(() => {
    const fetchMenu = async () => {
      try {
        // Используем новый сервис и канал (предполагаем, что он есть)
        // TODO: Убедиться, что обработчик IpcChannels.MENU_GET_APP_MENU зарегистрирован в handlers.ts
        const result = await ipcRendererService.invoke<AppMenuItem[]>(
          IpcChannels.MENU_GET_APP_MENU
        ); // Используем константу
        setMenuStructure(result);
      } catch (error) {
        console.error("Failed to fetch app menu:", error);
        // Можно показать уведомление
      }
    };
    fetchMenu();
  }, []);

  const handleItemClick = (label: string | undefined) => {
    if (!label) return;
    setActiveMenu((current) => (current === label ? null : label)); // Toggle
  };

  const closeAllMenus = useCallback(() => {
    setActiveMenu(null);
  }, []);

  return (
    <nav className="menu-bar">
      {menuStructure.map((item) => (
        <MenuBarItem
          key={item.label}
          item={item}
          isActive={activeMenu === item.label}
          onClick={() => handleItemClick(item.label)}
          onCloseRequest={closeAllMenus}
        />
      ))}
    </nav>
  );
};
