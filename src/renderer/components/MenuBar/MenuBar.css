.menu-bar {
  display: flex;
  height: 100%; /* Занимает высоту TitleBar */
  padding-left: 10px; /* Отступ слева */
  -webkit-app-region: no-drag; /* Меню не должно перетаскивать окно */
}

.menu-bar-item {
  padding: 0 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  position: relative; /* Для позиционирования подменю */
  font-size: 0.9em;
  color: var(--app-foreground);
}

.menu-bar-item:hover,
.menu-bar-item.active { /* Стиль при открытом подменю */
  background-color: var(--app-list-hoverBackground);
}

.submenu {
  position: absolute;
  top: 100%; /* Появляется под родительским элементом */
  left: 0;
  background-color: var(--app-sideBar-background); /* Фон как у сайдбара */
  border: 1px solid var(--app-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 5px 0;
  min-width: 180px;
  z-index: 1100; /* Выше TitleBar */
  list-style: none;
  margin: 0;
}

.submenu-item {
  padding: 6px 20px;
  cursor: pointer;
  white-space: nowrap;
  display: flex; /* Для возможного добавления иконок/горячих клавиш */
  justify-content: space-between;
  align-items: center;
}

.submenu-item:hover {
  background-color: var(--app-list-activeSelectionBackground); /* Цвет выделения */
  color: var(--app-button-foreground);
}

.submenu-item.separator {
  height: 1px;
  background-color: var(--app-border);
  margin: 5px 0;
  padding: 0;
}

.submenu-item.disabled { /* Если будем добавлять disabled */
  color: var(--app-tab-inactiveForeground);
  cursor: default;
}

.submenu-item.disabled:hover {
  background-color: transparent;
  color: var(--app-tab-inactiveForeground);
}
