.input-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Полупрозрачный фон */
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Выравниваем по верху */
  padding-top: 15vh; /* Отступ сверху */
  z-index: 1000; /* Поверх других элементов */
}

.input-dialog-modal { /* Исправляем имя класса */
  background-color: var(--app-sideBar-background); /* Фон как у сайдбара */
  color: var(--app-foreground);
  padding: 25px; /* Немного увеличим отступы */
  border-radius: 5px;
  border: 1px solid var(--app-border);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  min-width: 300px;
  max-width: 500px;
  display: flex;
  flex-direction: column;
}

.input-dialog-modal h3 { /* Исправляем селектор */
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1em;
  border-bottom: 1px solid var(--app-border);
  padding-bottom: 10px;
}

/* Добавляем стиль для параграфа */
.input-dialog-modal p {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 0.95em; /* Чуть крупнее */
  color: var(--app-foreground); /* Основной цвет текста */
}


.input-dialog-modal input[type="text"] { /* Исправляем селектор */
  width: 100%; /* Убираем calc, т.к. box-sizing: border-box должен быть у input */
  box-sizing: border-box; /* Добавляем */
  padding: 8px 10px; /* Корректируем padding */
  margin-bottom: 20px; /* Увеличим отступ снизу */
  background-color: var(--app-input-background);
  color: var(--app-input-foreground);
  border: 1px solid var(--app-border);
  border-radius: 3px;
  font-size: 1em;
}

.input-dialog-modal input[type="text"]:focus { /* Исправляем селектор */
  outline: none;
  border-color: var(--app-statusBar-background); /* Цвет рамки при фокусе */
}

.input-dialog-modal .input-dialog-buttons { /* Исправляем селектор */
  display: flex;
  justify-content: flex-end;
  gap: 10px; /* Добавляем отступ между кнопками */
  margin-top: 15px; /* Увеличим отступ сверху */
}

.input-dialog-modal .input-dialog-buttons button { /* Исправляем селектор */
  /* Убираем margin-left, используем gap */
  min-width: 80px; /* Немного увеличим */
}

/* Стили для кнопки отмены (делаем ее вторичной) */
.input-dialog-modal .input-dialog-buttons button:first-child { /* Исправляем селектор */
  background-color: transparent; /* Прозрачный фон */
  color: var(--app-foreground);
  border: 1px solid var(--app-border); /* Добавляем рамку */
}
.input-dialog-modal .input-dialog-buttons button:first-child:hover { /* Исправляем селектор */
  background-color: var(--app-list-hoverBackground); /* Фон при наведении */
}

/* Стиль для основной кнопки (OK) остается стандартным */
