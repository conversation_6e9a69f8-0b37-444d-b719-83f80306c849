/* eslint-disable @eslint-react/hooks-extra/no-direct-set-state-in-use-effect */
/* eslint-disable @eslint-react/web-api/no-leaked-timeout */
import React, { useState, useEffect, useRef } from "react";
import "./InputDialog.css";

interface InputDialogProps {
  isOpen: boolean;
  title: string;
  prompt: string;
  defaultValue?: string;
  onConfirm: (value: string) => void;
  onCancel: () => void;
}

export type InputDialogOptions = Omit<
  React.ComponentProps<typeof InputDialog>,
  "isOpen" | "onCancel"
>;

export const InputDialog: React.FC<InputDialogProps> = ({
  isOpen,
  title,
  prompt,
  defaultValue = "",
  onConfirm,
  onCancel,
}) => {
  const [value, setValue] = useState(defaultValue);
  const inputRef = useRef<HTMLInputElement>(null);

  // Сброс значения и фокус при открытии
  useEffect(() => {
    if (isOpen) {
      setValue(defaultValue);
      // Фокус с небольшой задержкой
      setTimeout(() => inputRef.current?.focus(), 50);
    }
  }, [isOpen, defaultValue]);

  // Обработка Enter и Escape
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();
      if (value.trim()) {
        // Подтверждаем только если не пусто
        onConfirm(value.trim());
      }
    } else if (event.key === "Escape") {
      onCancel();
    }
  };

  const handleConfirmClick = () => {
    if (value.trim()) {
      onConfirm(value.trim());
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="input-dialog-overlay" onClick={onCancel}>
      <div className="input-dialog-modal" onClick={(e) => e.stopPropagation()}>
        <h3>{title}</h3>
        <p>{prompt}</p>
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={handleKeyDown}
          className="input-dialog-input"
        />
        <div className="input-dialog-buttons">
          <button onClick={onCancel}>Cancel</button>
          <button onClick={handleConfirmClick} disabled={!value.trim()}>
            OK
          </button>
        </div>
      </div>
    </div>
  );
};
