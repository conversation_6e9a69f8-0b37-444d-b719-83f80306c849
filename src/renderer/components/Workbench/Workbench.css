/* Styles for the main Workbench layout */

.workbench-layout {
  display: grid;
  height: 100vh;
  width: 100vw;
  background-color: var(--app-editor-background);
  color: var(--app-foreground);
}

.sidebar-resizer-hidden {
  /* Позиция задается инлайн-стилем grid-column/grid-row в Workbench.tsx */
  width: 0;
  height: 100%;
}
.panel-resizer-hidden {
  /* Позиция задается инлайн-стилем grid-column/grid-row в Workbench.tsx */
  width: 100%;
  height: 0;
}
