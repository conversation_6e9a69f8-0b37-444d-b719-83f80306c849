import React, { use<PERSON>emo, useCallback } from "react"; // Removed useState
import "./Workbench.css";
import { CommandPalette } from "../CommandPalette/CommandPalette";
import { InputDialog } from "../InputDialog/InputDialog"; // Renamed import
import {
  useWorkbenchStore,
  WorkbenchState,
  useInputDialogState,
} from "@renderer/state/workbenchStore";
import { Sidebar } from "../Sidebar/Sidebar";
import { Panel } from "../Panel/Panel";
import { EditorGroup } from "../EditorGroup/EditorGroup";
import { StatusBar } from "../StatusBar/StatusBar";
import { ActivityBar } from "../ActivityBar/ActivityBar";
import { ResizerSash } from "../ResizerSash/ResizerSash";
import { useWorkbenchInitializer } from "@renderer/hooks/useWorkbenchInitializer";
import { usePanelResizing } from "@renderer/hooks/usePanelResizing";
import { useWorkbenchLayout } from "@renderer/hooks/useWorkbenchLayout";
import { useGlobalHotkeys } from "@renderer/hooks/useGlobalHotkeys";
import { useWorkbenchPersistence } from "@renderer/hooks/useWorkbenchPersistence"; // Import the new hook
import { useShallow } from "zustand/react/shallow";
import { NotificationContainer } from "../Notifications/NotificationContainer"; // Assuming this exists

export const Workbench: React.FC = () => {
  console.log("[Workbench] Rendering...");

  // --- Hooks Section ---
  // 1. Store Selectors
  const {
    // State values
    isPaletteOpen,
    activeViewContainerId,
    isSidebarVisible,
    isPanelVisible,
    sidebarWidth,
    panelHeight,
    togglePalette,
    setSidebarWidth,
    setPanelHeight,
    setActiveViewContainerId,
    storeConfirmInputDialog,
    storeCancelInputDialog,
  } = useWorkbenchStore(
    useShallow((state: WorkbenchState) => ({
      // State values
      isPaletteOpen: state.isPaletteOpen,
      activeViewContainerId: state.activeViewContainerId,
      isSidebarVisible: state.isSidebarVisible,
      isPanelVisible: state.isPanelVisible,
      sidebarWidth: state.sidebarWidth,
      panelHeight: state.panelHeight,
      togglePalette: state.togglePalette,
      setSidebarWidth: state.setSidebarWidth,
      setPanelHeight: state.setPanelHeight,
      setActiveViewContainerId: state.setActiveViewContainerId,
      toggleSidebarVisibility: state.toggleSidebarVisibility,
      togglePanelVisibility: state.togglePanelVisibility,
      storeOpenInputDialog: state.openInputDialog,
      storeConfirmInputDialog: state.confirmInputDialog,
      storeCancelInputDialog: state.cancelInputDialog,
    }))
  );
  const inputDialogState = useInputDialogState();

  // 2. Custom Hooks
  const {
    viewContainers,
    sidebarViews,
    panelViews,
    statusBarItems,
    loading,
    error,
  } = useWorkbenchInitializer();
  const {
    handleSidebarResize,
    handlePanelResize,
    handleSidebarSashMouseDown,
    handlePanelSashMouseDown,
  } = usePanelResizing({
    sidebarWidth,
    panelHeight,
    setSidebarWidth,
    setPanelHeight,
  });
  const {
    gridStyle,
    activityBarStyle,
    sidebarStyle,
    sidebarResizerStyle,
    editorGroupStyle,
    panelResizerStyle,
    panelStyle,
    statusBarStyle,
  } = useWorkbenchLayout({
    isSidebarVisible,
    sidebarWidth,
    isPanelVisible,
    panelHeight,
  });
  useGlobalHotkeys();
  useWorkbenchPersistence();

  // 3. Callbacks
  const handleInputDialogConfirm = useCallback(
    (value: string) => {
      console.log(`[Workbench] InputDialog confirmed with value: "${value}"`);
      storeConfirmInputDialog(value); // Call store action
    },
    [storeConfirmInputDialog]
  );
  const handleInputDialogCancel = useCallback(() => {
    console.log(`[Workbench] InputDialog cancelled`);
    storeCancelInputDialog(); // Call store action
  }, [storeCancelInputDialog]);

  // 4. Memos (Renumbered from 5)
  const activeViewId = useMemo(() => {
    const activeContainer = viewContainers.find(
      (c) => c.id === activeViewContainerId
    );
    return activeContainer?.viewId ?? null;
  }, [activeViewContainerId, viewContainers]);

  // Removed redundant useMemo for sidebarViews and panelViews, assuming they are stable from useWorkbenchInitializer
  // const memoizedSidebarViews = useMemo(() => sidebarViews, [sidebarViews]);
  // const memoizedPanelViews = useMemo(() => panelViews, [panelViews]);

  // Memoize props for Sidebar instead of the element itself
  const sidebarProps = useMemo(() => {
    console.log("[Workbench] Recalculating sidebarProps memo"); // Add log
    return {
      sidebarViews: sidebarViews, // Assuming sidebarViews ref is stable
      activeViewId: activeViewId,
      style: sidebarStyle, // Assuming sidebarStyle ref is stable
    };
  }, [sidebarViews, activeViewId, sidebarStyle]);

  // Memoize props for Panel instead of the element itself
  const panelProps = useMemo(() => {
    console.log("[Workbench] Recalculating panelProps memo"); // Add log
    return {
      panelId: "main-panel", // Add required panelId
      panelViews: panelViews, // Assuming panelViews ref is stable
      style: panelStyle, // Assuming panelStyle ref is stable
    };
  }, [panelViews, panelStyle]);

  // --- Conditional Rendering ---
  if (loading) {
    return <div>Loading Workbench...</div>;
  }
  if (error) {
    return (
      <div style={{ color: "red", padding: "20px" }}>
        Error initializing Workbench: {error}
      </div>
    );
  }

  // --- Final Return ---
  return (
    <div className="workbench-layout" style={gridStyle}>
      <ActivityBar
        containers={viewContainers}
        activeContainerId={activeViewContainerId}
        onContainerClick={setActiveViewContainerId}
        style={activityBarStyle}
      />
      {/* Render Sidebar only if visible, passing memoized props */}
      {isSidebarVisible && <Sidebar {...sidebarProps} />}
      {isSidebarVisible && (
        <ResizerSash
          orientation="vertical"
          onResize={handleSidebarResize}
          className="sidebar-resizer"
          style={sidebarResizerStyle}
          onMouseDown={handleSidebarSashMouseDown}
        />
      )}
      <EditorGroup style={editorGroupStyle} />
      {isPanelVisible && (
        <ResizerSash
          orientation="horizontal"
          onResize={handlePanelResize}
          className="panel-resizer"
          style={panelResizerStyle}
          onMouseDown={handlePanelSashMouseDown}
        />
      )}
      {/* Render Panel only if visible, passing memoized props */}
      {isPanelVisible && <Panel {...panelProps} />}
      <StatusBar items={statusBarItems} style={statusBarStyle} />
      <CommandPalette isOpen={isPaletteOpen} onClose={togglePalette} />
      {inputDialogState.options && (
        <InputDialog
          isOpen={inputDialogState.isOpen}
          onCancel={handleInputDialogCancel}
          onConfirm={handleInputDialogConfirm}
          title={inputDialogState.options.title}
          prompt={inputDialogState.options.prompt}
          defaultValue={inputDialogState.options.defaultValue}
        />
      )}
      <NotificationContainer />
    </div>
  );
};
