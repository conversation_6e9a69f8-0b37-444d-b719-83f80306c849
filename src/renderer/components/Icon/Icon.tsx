import React from 'react';
import * as icons from 'lucide-react';

// Тип для всех возможных имен иконок из lucide-react
type LucideIconName = keyof typeof icons;

interface IconProps extends React.SVGProps<SVGSVGElement> {
  name: LucideIconName | string; // Принимаем имя иконки
  size?: number;
  color?: string;
  strokeWidth?: number;
}

export const Icon: React.FC<IconProps> = ({name, size = 16, color = 'currentColor', strokeWidth = 2, ...props}) => {
  const LucideIcon = icons[name as LucideIconName];
  
  if (!LucideIcon) {
    console.warn(`[Icon] Icon not found: ${name}`);
    return <icons.HelpCircle size={size} color={color} strokeWidth={strokeWidth} {...props} />;
  }
  
  // @ts-expect-error - LucideIcon component accepts these props
  return <LucideIcon size={size} color={color} strokeWidth={strokeWidth} {...props} />;
};
