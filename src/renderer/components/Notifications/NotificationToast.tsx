/* eslint-disable @eslint-react/hooks-extra/no-direct-set-state-in-use-effect */
/* eslint-disable @eslint-react/web-api/no-leaked-timeout */
import React, { useEffect, useState } from "react";
import cn from "classnames";
import { Icon } from "../Icon/Icon";
import type { NotificationType } from "@services/notification.service";
import "./Notifications.css";

export interface Notification {
  id: number;
  message: string;
  type: NotificationType;
  duration?: number; // in ms, undefined for persistent
  // actions?: { label: string; commandId: string }[]; // TODO: Add actions later
}

interface NotificationToastProps {
  notification: Notification;
  onDismiss: (id: number) => void;
}

const typeToIcon: Record<NotificationType, string> = {
  info: "Info",
  warning: "AlertTriangle",
  error: "AlertOctagon",
  success: "Check",
  // Убираем success, т.к. его нет в NotificationType
};

export const NotificationToast: React.FC<NotificationToastProps> = ({
  notification,
  onDismiss,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // Анимация появления
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Таймер для автоматического скрытия
  useEffect(() => {
    if (notification.duration) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        // Даем время на анимацию исчезновения перед удалением из стора
        setTimeout(() => onDismiss(notification.id), 300); // 300ms - длительность анимации
      }, notification.duration);

      return () => clearTimeout(timer);
    }
  }, [notification.duration, notification.id, onDismiss]);

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => onDismiss(notification.id), 300);
  };

  const iconName = typeToIcon[notification.type] || "Info";

  return (
    <div
      className={cn("notification-toast", `notification-${notification.type}`, {
        visible: isVisible,
      })}
    >
      <div className="notification-icon">
        <Icon name={iconName} size={18} />
      </div>
      <div className="notification-content">
        <p>{notification.message}</p>
        {/* TODO: Render action buttons here */}
      </div>
      <button
        className="notification-dismiss"
        onClick={handleDismiss}
        title="Dismiss"
      >
        <Icon name="X" size={16} />
      </button>
    </div>
  );
};
