import React from 'react';
import { useNotificationStore } from '@renderer/state/notificationStore'; // Импортируем стор (создадим его дальше)
import { NotificationToast } from './NotificationToast';
import './Notifications.css'; // Общий файл стилей (создадим его дальше)

export const NotificationContainer: React.FC = () => {
  // Используем activeNotifications и removeActiveNotification для всплывающих уведомлений
  const activeNotifications = useNotificationStore(state => state.activeNotifications);
  const removeActiveNotification = useNotificationStore(state => state.removeActiveNotification);

  if (activeNotifications.length === 0) {
    return null; // Не рендерим ничего, если нет активных уведомлений
  }

  return (
    <div className="notification-container">
      {/* Итерируем по activeNotifications */}
      {activeNotifications.map(notification => (
        <NotificationToast
          key={notification.id}
          notification={notification}
          onDismiss={removeActiveNotification} // Используем removeActiveNotification
        />
      ))}
    </div>
  );
};
