.notification-container {
  position: fixed;
  bottom: 20px; /* Отступ снизу */
  right: 20px; /* Отступ справа */
  z-index: 2000; /* Выше всего остального */
  display: flex;
  flex-direction: column;
  gap: 10px; /* Расстояние между уведомлениями */
  max-width: 350px; /* Максимальная ширина */
}

.notification-toast {
  display: flex;
  align-items: flex-start; /* Выравниваем по верху */
  padding: 12px 15px;
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transform: translateX(100%); /* Начинаем за экраном */
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  background-color: var(--app-sideBar-background); /* Фон по умолчанию */
  color: var(--app-foreground);
  border: 1px solid var(--app-border);
}

.notification-toast.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Стили для разных типов уведомлений */
.notification-toast.notification-info {
  border-left: 4px solid var(--app-infoForeground);
}
/* Убираем success, т.к. его нет в NotificationType */
.notification-toast.notification-warning {
  border-left: 4px solid var(--app-warningForeground);
}
.notification-toast.notification-error {
  border-left: 4px solid var(--app-errorForeground);
}

.notification-icon {
  margin-right: 10px;
  flex-shrink: 0;
  margin-top: 2px; /* Небольшой сдвиг иконки вниз */
}

.notification-toast.notification-info .notification-icon { color: var(--app-infoForeground); }
/* Убираем success */
.notification-toast.notification-warning .notification-icon { color: var(--app-warningForeground); }
.notification-toast.notification-error .notification-icon { color: var(--app-errorForeground); }


.notification-content {
  flex-grow: 1;
  margin-right: 10px; /* Отступ до кнопки закрытия */
}

.notification-content p {
  margin: 0;
  padding: 0;
  line-height: 1.4;
  font-size: 0.95em;
}

.notification-dismiss {
  background: none;
  border: none;
  color: var(--app-tab-inactiveForeground); /* Менее заметный цвет */
  cursor: pointer;
  padding: 0;
  margin-left: auto; /* Прижимаем к правому краю */
  flex-shrink: 0;
  opacity: 0.7;
}

.notification-dismiss:hover {
  color: var(--app-foreground);
  opacity: 1;
}
