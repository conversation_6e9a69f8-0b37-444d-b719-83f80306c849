.title-bar {
  height: 30px; /* Стандартная высота Title Bar */
  background-color: var(--app-activityBar-background); /* Фон как у Activity Bar */
  color: var(--app-foreground);
  display: flex;
  align-items: center;
  padding: 0 10px;
  border-bottom: 1px solid var(--app-border);
  flex-shrink: 0; /* Не сжимается */
  user-select: none; /* Запрещаем выделение текста */
  /* !!! Важно для перетаскивания окна !!! */
  -webkit-app-region: drag;
}

/* Стили для элементов, которые НЕ должны перетаскивать окно */
.title-bar button,
.title-bar .window-controls,
.title-bar .title-bar-menu /* Если будет меню */ {
  -webkit-app-region: no-drag;
}

.title-bar-title {
  font-size: 0.9em;
  /* Убираем margin: 0 auto, чтобы не центрировать, если есть меню */
  /* margin: 0 auto; */
  /* Добавляем отступы слева/справа, чтобы не перекрывать кнопки macOS и меню */
  padding-left: 10px; /* Отступ от меню */
  padding-right: 70px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Стили для кнопок управления UI (слева от кнопок окна) */
.title-bar-actions {
  display: flex;
  align-items: center;
  margin-left: auto; /* Прижимаем к правому краю, перед window-controls */
  padding-right: 5px;
}

.title-bar-actions button {
  background: none;
  border: none;
  color: var(--app-foreground);
  padding: 5px;
  margin-left: 2px;
  font-size: 16px; /* Размер иконок */
  cursor: pointer;
  opacity: 0.7;
  border-radius: 3px;
}

.title-bar-actions button:hover {
  opacity: 1;
  background-color: var(--app-list-hoverBackground);
}


/* Стили для кнопок управления окном */
.window-controls {
  display: flex;
  position: absolute; /* Позиционируем абсолютно */
  right: 10px; /* Справа */
  top: 0;
  height: 100%;
  align-items: center;
}

.window-controls button {
  background: none;
  border: none;
  color: var(--app-foreground);
  padding: 5px 8px;
  font-size: 12px; /* Или иконки */
  cursor: pointer;
  opacity: 0.7;
}

.window-controls button:hover {
  opacity: 1;
  background-color: var(--app-list-hoverBackground);
}

/* Специфичные стили для macOS (скрываем кастомные кнопки, если есть системные) */
body.platform-darwin .window-controls {
  /* Можно скрыть, если titleBarStyle: 'hidden' работает */
   /* display: none; */
   /* Или просто отодвинуть, чтобы не мешать системным */
   right: 80px;
}
