import React, { useEffect, useState } from "react";
import "./TitleBar.css"; // Импортируем стили
import { MenuBar } from "../MenuBar/MenuBar"; // Импортируем MenuBar
import { Co<PERSON>, Minus, PanelBottom, PanelLeft, Square, X } from "lucide-react"; // Импортируем иконки
// Import types and service
import {
  CommandsExecuteArgs,
  IpcErrorData,
  WindowIsMaximizedResult,
} from "@shared/types/ipc";
import { IpcChannels } from "@shared/constants/ipc-channels";
import { CommandIds } from "@shared/constants/command-ids";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import { useWorkbenchStore } from "@renderer/state/workbenchStore"; // Импортируем Zustand store

export const TitleBar: React.FC = () => {
  // Используем состояние из Zustand store
  const isWindowMaximized = useWorkbenchStore(
    (state) => state.isWindowMaximized
  );
  const setWindowMaximized = useWorkbenchStore(
    (state) => state.setWindowMaximized
  ); // Получаем action
  const [platform, setPlatform] = useState<"darwin" | "win32" | "linux">(
    "linux"
  );

  useEffect(() => {
    // Определяем платформу и добавляем класс к body
    const getPlatform = async () => {
      try {
        const plat = await ipcRendererService.invoke<string>(
          IpcChannels.WINDOW_GET_PLATFORM
        ); // Используем константу
        setPlatform(plat as "darwin" | "win32" | "linux"); // Cast to specific platform types
        document.body.classList.add(`platform-${plat}`);
      } catch (error) {
        console.error("Failed to get platform:", error);
      }
    };

    // Получаем начальное состояние окна и устанавливаем его в store
    const checkMaximized = async () => {
      try {
        const maximized =
          await ipcRendererService.invoke<WindowIsMaximizedResult>(
            IpcChannels.WINDOW_IS_MAXIMIZED
          );
        setWindowMaximized(maximized); // Устанавливаем начальное состояние в store
      } catch (error) {
        console.error("Failed to check if window is maximized:", error);
        // Можно установить в false по умолчанию при ошибке
        setWindowMaximized(false);
      }
    };

    getPlatform();
    checkMaximized(); // Вызываем проверку начального состояния

    // Подписки на события теперь обрабатываются в listeners.ts

    return () => {
      // Убираем класс платформы при размонтировании
      document.body.classList.remove(`platform-${platform}`);
    };
  }, []);

  // Используем ipcMain.on в Main, поэтому здесь не нужны invoke
  // Preload не предоставляет прямых методов для этого
  // Вместо этого можно вызывать команды, если они есть, или использовать ipcRendererService.send (если реализовано)
  // Пока оставим как есть, но это не будет работать без изменений в preload/main
  // TODO: Заменить на вызовы команд 'core:window.minimize', 'core:window.maximize' etc. через ipcRendererService.invoke('core:commands.execute', ...)
  const handleMinimize = () =>
    console.warn("Minimize action needs refactoring (use command)"); // window.electronAPI?.minimizeWindow();
  const handleMaximize = () =>
    console.warn("Maximize action needs refactoring (use command)"); // window.electronAPI?.maximizeWindow();
  const handleUnmaximize = () =>
    console.warn("Unmaximize action needs refactoring (use command)"); // window.electronAPI?.unmaximizeWindow();
  const handleClose = () =>
    console.warn("Close action needs refactoring (use command)"); // window.electronAPI?.closeWindow();

  // Не рендерим кастомные кнопки для macOS, если используется 'hidden' titleBarStyle
  const showCustomControls = platform !== "darwin";

  return (
    <div className="title-bar">
      {/* Spacer for macOS controls */}
      <div
        style={{
          width: platform === "darwin" ? "70px" : "10px",
          flexShrink: 0,
        }}
      ></div>
      {/* Вставляем MenuBar только не для macOS */}
      {platform !== "darwin" && <MenuBar />}

      <div className="title-bar-title">
        AI Books IDE {/* TODO: Сделать динамическим */}
      </div>

      {/* Добавляем кнопки управления UI */}
      <div className="title-bar-actions">
        {/* Вызываем команды через IPC с использованием нового сервиса */}
        <button
          onClick={async () => {
            try {
              const args: CommandsExecuteArgs = {
                commandId: CommandIds.TOGGLE_SIDEBAR_VISIBILITY,
                args: {},
              }; // Используем константу
              await ipcRendererService.invoke(
                IpcChannels.COMMANDS_EXECUTE,
                args
              ); // Используем константу
            } catch (err) {
              const errorData = err as IpcErrorData;
              console.error("Error toggling sidebar:", errorData.message, err);
              // Optionally show notification
            }
          }}
          title="Toggle Sidebar"
        >
          <PanelLeft size={16} />
        </button>
        <button
          onClick={async () => {
            try {
              const args: CommandsExecuteArgs = {
                commandId: CommandIds.TOGGLE_PANEL_VISIBILITY,
                args: {},
              }; // Используем константу
              await ipcRendererService.invoke(
                IpcChannels.COMMANDS_EXECUTE,
                args
              ); // Используем константу
            } catch (err) {
              const errorData = err as IpcErrorData;
              console.error("Error toggling panel:", errorData.message, err);
              // Optionally show notification
            }
          }}
          title="Toggle Panel"
        >
          <PanelBottom size={16} />
        </button>
      </div>

      {showCustomControls && (
        <div className="window-controls">
          <button onClick={handleMinimize} title="Minimize">
            <Minus size={12} />
          </button>
          {isWindowMaximized ? ( // Используем состояние из store
            <button onClick={handleUnmaximize} title="Restore Down">
              <Copy size={12} />
            </button>
          ) : (
            <button onClick={handleMaximize} title="Maximize">
              <Square size={12} />
            </button>
          )}
          <button
            onClick={handleClose}
            title="Close"
            className="window-close-button"
          >
            <X size={12} />
          </button>
        </div>
      )}
      {/* Spacer для macOS, если кнопки не отображаются */}
      {!showCustomControls && <div style={{ width: "70px" }}></div>}
    </div>
  );
};
