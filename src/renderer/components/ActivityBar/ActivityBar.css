.activity-bar {
  background-color: var(--app-activityBar-background);
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: 1px solid var(--app-border);
  height: 100%; /* Занимает всю высоту своей ячейки */
}

.activity-bar button {
  background-color: transparent;
  border: none;
  color: var(--app-tab-inactiveForeground); /* Цвет неактивной иконки */
  font-size: 24px; /* Размер иконки */
  padding: 10px 0;
  width: 100%;
  cursor: pointer;
  transition: color 0.2s ease;
  display: flex; /* Для центрирования иконки */
  justify-content: center;
  align-items: center;
}

.activity-bar button:hover {
  color: var(--app-foreground); /* Цвет при наведении */
}

.activity-bar button.active {
  color: var(--app-foreground); /* Цвет активной иконки */
  /* Индикатор слева */
  position: relative;
}

.activity-bar button.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 60%; /* Высота индикатора */
  background-color: var(--app-foreground);
}
