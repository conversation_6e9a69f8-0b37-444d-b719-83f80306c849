.command-palette-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Выравниваем по верху */
  padding-top: 10vh; /* Отступ сверху */
  /* Увеличим z-index на всякий случай */
  z-index: 1100;
}

/* Переименовываем .command-palette в .command-palette-modal для ясности */
.command-palette-modal {
  background-color: var(--app-editor-background);
  color: var(--app-foreground);
  border-radius: 6px;
  border: 1px solid var(--app-border);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
  width: 600px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Скрываем выходящий контент */
}

.command-palette-input {
  width: calc(100% - 20px); /* Учитываем padding */
  padding: 10px;
  background-color: var(--app-input-background);
  color: var(--app-input-foreground);
  border: none;
  border-bottom: 1px solid var(--app-border); /* Граница снизу */
  font-size: 1.1em;
  outline: none;
}

.command-palette-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 400px; /* Ограничиваем высоту списка */
  overflow-y: auto; /* Добавляем скролл */
}

.command-palette-list li {
  padding: 10px 15px; /* Немного увеличим padding */
  cursor: pointer;
  border-bottom: 1px solid var(--app-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.command-palette-list li:last-child {
  border-bottom: none;
}

/* Добавляем стили для деталей и keybindings */
.command-details {
  flex-grow: 1; /* Занимает доступное пространство */
  margin-right: 15px; /* Отступ справа */
  overflow: hidden; /* Обрезаем длинный текст */
  text-overflow: ellipsis;
  white-space: nowrap;
}

.command-keybindings {
  color: var(--app-tab-inactiveForeground); /* Цвет для keybindings */
  font-size: 0.9em;
  white-space: nowrap; /* Не переносим */
  flex-shrink: 0; /* Не сжимаем */
}

/* Стили для заголовка категории */
.command-palette-category-header {
  padding: 8px 15px 4px 15px; /* Отступы */
  font-size: 0.9em;
  font-weight: 600;
  color: var(--app-tab-inactiveForeground); /* Менее яркий цвет */
  background-color: var(--app-sideBar-background); /* Фон как у палитры */
  border-bottom: 1px solid var(--app-border);
  /* Делаем заголовок "липким" при прокрутке */
  position: sticky;
  top: 0;
  z-index: 1; /* Чтобы был над элементами списка */
}

/* Убираем старый стиль для категории, т.к. она теперь внутри .command-details */
/*
.command-palette-list li .command-category {
  font-size: 0.85em;
  color: var(--app-tab-inactiveForeground);
  margin-left: 10px;
}
*/

/* Убираем стиль для категории внутри .command-details, т.к. есть заголовок */
/*
.command-details .command-category {
  font-size: 0.9em;
  color: var(--app-tab-inactiveForeground);
  margin-right: 5px;
}
*/

/* Обновляем стили для выделенного элемента */
.command-palette-list li.selected {
  background-color: var(--app-list-activeSelectionBackground);
  color: var(--app-button-foreground); /* Белый текст на синем фоне - оставляем */
}

/* Обновляем цвет keybindings для выделенного элемента */
/* .command-palette-list li.selected .command-category, */ /* Убираем категорию */
.command-palette-list li.selected .command-keybindings {
   color: var(--app-button-foreground); /* Белый текст */
   opacity: 0.85; /* Чуть менее прозрачный */
}

/* Стиль для "No results" */
.command-palette-item.no-results {
  color: var(--app-tab-inactiveForeground);
  cursor: default;
  justify-content: center; /* Центрируем текст */
}

.command-palette-list li:hover:not(.selected) { /* Применяем hover только если не selected */
  background-color: var(--app-list-hoverBackground);
}
