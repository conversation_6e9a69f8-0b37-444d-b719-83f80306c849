/* eslint-disable @eslint-react/hooks-extra/no-direct-set-state-in-use-effect */
/* eslint-disable @eslint-react/web-api/no-leaked-timeout */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  CommandDescription,
  CommandsExecuteArgs,
  CommandsGetAllResult,
  IpcErrorData,
} from "@shared/types/ipc";
import { IpcChannels } from "@shared/constants/ipc-channels";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import "./CommandPalette.css";

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
}

type PaletteCommand = CommandDescription;

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
}) => {
  const [query, setQuery] = useState("");
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLUListElement>(null);
  const [allCommands, setAllCommands] = useState<PaletteCommand[]>([]);

  useEffect(() => {
    console.log("[CommandPalette] Component mounted.");
    if (isOpen && allCommands.length === 0) {
      const fetchCommands = async () => {
        try {
          console.log(
            `[CommandPalette] Fetching commands via ${IpcChannels.COMMANDS_GET_ALL}...`
          );
          const commandsResult =
            await ipcRendererService.invoke<CommandsGetAllResult>(
              IpcChannels.COMMANDS_GET_ALL
            );
          console.log(
            "[CommandPalette] Received commands result:",
            commandsResult
          );
          const commandsForPalette: PaletteCommand[] = commandsResult.map(
            (cmd) => ({
              ...cmd,
              keybindings: cmd.keybindings,
            })
          );

          setAllCommands(commandsForPalette);
          console.log(
            `[CommandPalette] Loaded ${commandsForPalette.length} commands.`
          );
        } catch (error) {
          console.error("[CommandPalette] Error fetching commands:", error);
          setAllCommands([]);
        }
      };
      fetchCommands();
    }
  }, [isOpen]);

  const filteredCommands = useMemo(() => {
    const lowerQuery = query.toLowerCase().trim();
    if (!lowerQuery) return allCommands;
    return allCommands.filter(
      (cmd) =>
        cmd.showInPalette &&
        (cmd.title.toLowerCase().includes(lowerQuery) ||
          (cmd.category && cmd.category.toLowerCase().includes(lowerQuery)))
    );
  }, [query, allCommands]);

  const executeSelectedCommand = useCallback(async () => {
    const selectedCommand = filteredCommands[selectedIndex];
    if (selectedCommand) {
      console.log(
        `Executing command: ${selectedCommand.id} via ${IpcChannels.COMMANDS_EXECUTE}`
      );
      try {
        const args: CommandsExecuteArgs = {
          commandId: selectedCommand.id,
          args: {},
        };
        await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args);
        console.log(`Command ${selectedCommand.id} executed successfully.`);
      } catch (error) {
        const errorData = error as IpcErrorData;
        console.error(
          `Error executing command ${selectedCommand.id}:`,
          errorData
        );
        alert(
          `Error executing command: ${errorData.message || "Unknown error"}`
        );
      } finally {
        onClose();
      }
    }
  }, [filteredCommands, selectedIndex, onClose]);

  useEffect(() => {
    if (isOpen) {
      setQuery("");
      setSelectedIndex(0);
      setTimeout(() => inputRef.current?.focus(), 50);
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "ArrowDown") {
        event.preventDefault();
        setSelectedIndex((prev) =>
          Math.min(prev + 1, filteredCommands.length - 1)
        );
      } else if (event.key === "ArrowUp") {
        event.preventDefault();
        setSelectedIndex((prev) => Math.max(prev - 1, 0));
      } else if (event.key === "Enter") {
        event.preventDefault();
        executeSelectedCommand();
      } else if (event.key === "Escape") {
        onClose();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    isOpen,
    filteredCommands,
    selectedIndex,
    onClose,
    executeSelectedCommand,
  ]);

  useEffect(() => {
    if (listRef.current && filteredCommands.length > 0) {
      const validIndex = Math.max(
        0,
        Math.min(selectedIndex, filteredCommands.length - 1)
      );
      if (validIndex !== selectedIndex) {
        setSelectedIndex(validIndex);
      }
      const selectedElement = listRef.current.children[
        validIndex
      ] as HTMLLIElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({ block: "nearest" });
      }
    } else if (filteredCommands.length === 0 && selectedIndex !== 0) {
      setSelectedIndex(0);
    }
  }, [selectedIndex, filteredCommands]);

  if (!isOpen) {
    return null;
  }

  const formatKeybindings = (
    bindings?: readonly string[] | undefined
  ): string | null => {
    if (!bindings || bindings.length === 0) return null;
    return Array.from(bindings).join(", ");
  };

  return (
    <div className="command-palette-overlay" onClick={onClose}>
      <div
        className="command-palette-modal"
        onClick={(e) => e.stopPropagation()}
      >
        <input
          ref={inputRef}
          type="text"
          placeholder="Type a command..."
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            setSelectedIndex(0);
          }}
          className="command-palette-input"
        />
        <ul ref={listRef} className="command-palette-list">
          {filteredCommands.length > 0 ? (
            filteredCommands.map((cmd, index, arr) => {
              const keybindingsString = formatKeybindings(cmd.keybindings);
              const showCategoryHeader =
                index === 0 || cmd.category !== arr[index - 1]?.category;

              return (
                <React.Fragment key={cmd.id}>
                  {showCategoryHeader && cmd.category && (
                    <li className="command-palette-category-header">
                      {cmd.category}
                    </li>
                  )}
                  <li
                    className={`command-palette-item ${
                      index === selectedIndex ? "selected" : ""
                    }`}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      executeSelectedCommand();
                    }}
                    onMouseEnter={() => setSelectedIndex(index)}
                    title={`${cmd.category ? cmd.category + ": " : ""}${
                      cmd.title
                    }${keybindingsString ? ` (${keybindingsString})` : ""}`}
                  >
                    <span className="command-details">{cmd.title}</span>
                    {keybindingsString && (
                      <span className="command-keybindings">
                        {keybindingsString}
                      </span>
                    )}
                  </li>
                </React.Fragment>
              );
            })
          ) : (
            <li className="command-palette-item no-results">
              No commands found
            </li>
          )}
        </ul>
      </div>
    </div>
  );
};
