/* Styles for NotificationsView Panel View */

.notifications-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.notifications-view-header {
  padding: 4px 8px;
  border-bottom: 1px solid var(--app-border);
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notifications-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 5px;
}

.no-notifications {
  color: var(--app-disabledForeground);
  text-align: center;
  margin-top: 20px;
}

.notification-item {
  padding: 6px 8px;
  margin-bottom: 4px;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid transparent; /* Base border */
}

.notification-item-info {
  background-color: var(--app-notificationInfo-background);
  border-color: var(--app-notificationInfo-border);
  color: var(--app-notificationInfo-foreground);
}

.notification-item-warning {
  background-color: var(--app-notificationWarning-background);
  border-color: var(--app-notificationWarning-border);
  color: var(--app-notificationWarning-foreground);
}

.notification-item-error {
  background-color: var(--app-notificationError-background);
  border-color: var(--app-notificationError-border);
  color: var(--app-notificationError-foreground);
}

.notification-message {
  margin-right: 10px;
  word-break: break-word; /* Wrap long messages */
}

.notification-dismiss-button {
  background: none;
  border: none;
  color: inherit;
  opacity: 0.7;
  cursor: pointer;
  padding: 0 4px;
  font-size: 1.2em;
  line-height: 1;
  flex-shrink: 0;
}

.notification-dismiss-button:hover {
  opacity: 1;
}
