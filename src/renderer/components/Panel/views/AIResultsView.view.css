/* src/renderer/components/Panel/views/AIResultsView.view.css */
.ai-results-container {
    padding: 10px;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box; /* Ensure padding is included in height */
}

.ai-results-item {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--vscode-editorGroup-border);
}

.ai-results-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.ai-results-snippet {
    font-style: italic;
    color: var(--vscode-textLink-foreground);
    margin-bottom: 5px;
    display: block;
    white-space: pre-wrap; /* Preserve whitespace in snippet */
}

.ai-results-explanation {
    margin-bottom: 3px;
}

.ai-results-context {
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
    margin-bottom: 3px;
}

.ai-results-severity {
    font-weight: bold;
}

.ai-results-severity-high {
    color: var(--vscode-errorForeground);
}

.ai-results-severity-medium {
    color: var(--vscode-testing-iconFailed); /* Or use --vscode-list-warningForeground */
}

.ai-results-severity-low {
    color: var(--vscode-descriptionForeground);
}

.ai-results-error {
    color: var(--vscode-errorForeground);
    padding: 10px;
}

.ai-results-loading,
.ai-results-no-results {
    color: var(--vscode-descriptionForeground);
    padding: 10px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
