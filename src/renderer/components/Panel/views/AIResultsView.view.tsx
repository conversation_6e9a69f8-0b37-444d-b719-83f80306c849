import React from "react";
import "./AIResultsView.view.css"; // Import the CSS file

// TODO: Define a proper type for the inconsistency result based on the schema
interface Inconsistency {
  inconsistent_text: string;
  explanation: string;
  context_violated: string;
  severity: "low" | "medium" | "high";
}

interface AIResultsViewProps {
  results: Inconsistency[] | null;
  isLoading: boolean;
  error: string | null;
}

// Helper to get severity class name
const getSeverityClassName = (severity: "low" | "medium" | "high"): string => {
  switch (severity) {
    case "high":
      return "ai-results-severity-high";
    case "medium":
      return "ai-results-severity-medium";
    case "low":
      return "ai-results-severity-low";
    default:
      return "";
  }
};

export const AIResultsView: React.FC<AIResultsViewProps> = ({
  results,
  isLoading,
  error,
}) => {
  if (isLoading) {
    return <div className="ai-results-loading">Running AI analysis...</div>;
  }

  if (error) {
    return (
      <div className="ai-results-error">Error during analysis: {error}</div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div className="ai-results-no-results">
        No inconsistencies found or analysis not run yet.
      </div>
    );
  }

  return (
    <div className="ai-results-container">
      <h3>AI Consistency Check Results:</h3>
      {results.map((item) => (
        <div
          key={`inconsistency-${item.inconsistent_text
            .substring(0, 30)
            .replace(/\s+/g, "")}-${item.severity}`}
          className="ai-results-item"
        >
          <span className="ai-results-snippet">"{item.inconsistent_text}"</span>
          <div className="ai-results-explanation">{item.explanation}</div>
          <div className="ai-results-context">
            Violated Context: {item.context_violated}
          </div>
          <div
            className={`ai-results-severity ${getSeverityClassName(
              item.severity
            )}`}
          >
            Severity: {item.severity.toUpperCase()}
          </div>
        </div>
      ))}
    </div>
  );
};

export default AIResultsView; // Required for dynamic loading
