.panel-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* Занимает всю высоту ячейки грида */
  background-color: var(--app-panel-background); /* Используем переменную */
  color: var(--app-foreground);
}

.panel-tabs {
  display: flex;
  flex-shrink: 0; /* Вкладки не сжимаются */
  background-color: var(--app-editorGroupHeader-tabsBackground); /* Тот же фон, что и у вкладок редактора */
  border-bottom: 1px solid var(--app-border);
}

.panel-tabs button {
  background-color: transparent;
  border: none;
  border-bottom: 1px solid transparent; /* Для активного состояния */
  border-right: 1px solid var(--app-border);
  color: var(--app-tab-inactiveForeground);
  padding: 6px 12px;
  cursor: pointer;
  font-size: 0.9em;
  transition: color 0.2s ease, background-color 0.2s ease;
}

.panel-tabs button:hover {
  color: var(--app-foreground);
  background-color: var(--app-list-hoverBackground);
}

.panel-tabs button.active {
  color: var(--app-tab-activeForeground);
  background-color: var(--app-panel-background); /* Фон совпадает с контентом */
  border-bottom-color: var(--app-panel-background); /* Скрываем границу под активной вкладкой */
}

.panel-content {
  flex-grow: 1; /* Занимает все доступное место */
  overflow: auto; /* Добавляем скролл для контента */
  padding: 5px 10px;
  font-size: 0.9em;
}

/* Стили для логов */
.output-log {
  font-family: monospace;
  white-space: pre-wrap; /* Перенос строк */
}

.log-entry {
  padding: 2px 0;
  border-bottom: 1px solid var(--app-border); /* Разделитель между логами */
}
.log-entry:last-child {
  border-bottom: none;
}

.log-timestamp {
  color: var(--app-tab-inactiveForeground);
  margin-right: 8px;
}

.log-level {
  margin-right: 8px;
  font-weight: bold;
}

.log-level.log-error { color: var(--app-errorForeground); }
.log-level.log-warn { color: var(--app-warningForeground); }
.log-level.log-info { color: var(--app-infoForeground); }
.log-level.log-debug { color: var(--app-tab-inactiveForeground); }

.log-meta, .log-error {
  display: block; /* Отображаем на новой строке */
  margin-left: 20px; /* Небольшой отступ */
  color: var(--app-tab-inactiveForeground);
  font-size: 0.9em;
}
.log-error {
  color: var(--app-errorForeground);
}

/* Стили для новой обертки контента */
.panel-content-area {
  flex-grow: 1; /* Занимает все доступное место */
  overflow: auto; /* Добавляем скролл для контента */
  position: relative; /* Для позиционирования дочерних элементов, если нужно */
}
