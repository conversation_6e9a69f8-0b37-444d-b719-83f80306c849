import React, {
  lazy,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import type {
  CommandsExecuteArgs,
  IpcErrorData,
  LogEntry,
} from "@shared/types/ipc"; // Добавляем CommandsExecuteArgs
import { IpcChannels } from "@shared/constants/ipc-channels";
import { CommandIds } from "@shared/constants/command-ids";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import type { ViewDescriptor } from "@main/services/view.service";
import { Icon } from "../Icon/Icon";
import { PanelProps } from "@shared/types/ui";
import { getViewImporter } from "@renderer/core/viewRegistry";
import "./Panel.css";

const panelViewComponentCache = new Map<
  string,
  React.LazyExoticComponent<React.ComponentType<unknown>> | null
>();

function getLazyPanelViewComponent(
  // extensionId: string, // No longer needed
  componentName: string
): React.LazyExoticComponent<React.ComponentType<unknown>> | null {
  const cacheKey = componentName; // Use componentName as the key
  if (panelViewComponentCache.has(cacheKey)) {
    return panelViewComponentCache.get(cacheKey) ?? null;
  }

  console.log(`[Panel] Creating LazyViewComponent for: ${componentName}`);

  // Get the importer function from the registry
  const importer = getViewImporter(componentName);

  try {
    if (!importer) {
      throw new Error(`No importer found for component: ${componentName}`);
    }

    const lazyComponent = lazy(() =>
      importer() // Call the importer function provided by the registry
        .then((module) => {
          if (module.default) {
            console.log(
              `[Panel] Successfully imported view module for: ${componentName}`
            );
            return module;
          } else {
            console.error(
              `[Panel] View module loaded, but no default export found for: ${componentName}`
            );
            throw new Error(
              `View module for ${componentName} does not have a default export.`
            );
          }
        })
        .catch((error) => {
          console.error(
            `[Panel] Dynamic import failed for view component: ${componentName}. Error:`,
            error
          );
          // Re-throw the error to be caught by the outer catch block
          throw error;
        })
    );

    panelViewComponentCache.set(cacheKey, lazyComponent);
    return lazyComponent;
  } catch (error) {
    console.error(
      `[Panel] Failed to create lazy view component for ${componentName}. Error:`,
      error
    );
    panelViewComponentCache.set(cacheKey, null); // Cache the error (null component)
    return null;
  }
}

// --- End Corrected getLazyPanelViewComponent ---

// --- Компонент OutputLog ---
const OutputLog: React.FC = () => {
  const [logs] = useState<LogEntry[]>([]);
  const outputRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = 0;
    }
  }, [logs]);

  return (
    <div ref={outputRef} className="panel-content output-log">
      {logs.length === 0 && <span>No output yet...</span>}
      {logs.map((log) => (
        <div
          key={`${log.timestamp}-${log.level}-${log.message.substring(0, 50)}`}
          className={`log-entry log-${log.level}`}
        >
          <span className="log-timestamp">
            {new Date(log.timestamp).toLocaleTimeString()}
          </span>
          <span className="log-level">{`[${log.level.toUpperCase()}]`}</span>
          <span className="log-message">{log.message}</span>
          {log.error && (
            <span className="log-error">
              {log.error.message || "Unknown error"}
            </span>
          )}
          {/* Meta is likely excluded now, but keep the display logic if needed */}
          {/* {log.meta && <span className="log-meta">{JSON.stringify(log.meta)}</span>} */}
        </div>
      ))}
    </div>
  );
};
OutputLog.displayName = "OutputLog";

// --- Компонент PanelViewInstance ---
// Рендерит один экземпляр вида панели
interface PanelViewInstanceProps {
  view: ViewDescriptor;
  isActive: boolean;
}

const PanelViewInstance = React.memo<PanelViewInstanceProps>(
  ({ view, isActive }) => {
    const { componentName, name } = view;
    console.log(
      `[PanelViewInstance] Rendering instance for view: ${view.id}, isActive: ${isActive}`
    );

    // Получаем ленивый компонент
    const LazyViewComponent = useMemo(() => {
      if (!componentName) return null; // Check only componentName
      return getLazyPanelViewComponent(componentName); // Pass only componentName
    }, [componentName]); // Update dependency array

    const fallbackElement = useMemo(
      () => <div className="panel-content">Loading {name}...</div>,
      [name]
    );

    // Обработка ошибки загрузки компонента
    if (LazyViewComponent === null) {
      return (
        <div
          style={{
            display: isActive ? "block" : "none",
            height: "100%",
            padding: "10px",
            color: "red",
          }}
        >
          Error loading panel view: {name}
        </div>
      );
    }

    return (
      <div style={{ display: isActive ? "block" : "none", height: "100%" }}>
        <Suspense fallback={fallbackElement}>
          <LazyViewComponent />
        </Suspense>
      </div>
    );
  }
);
PanelViewInstance.displayName = "PanelViewInstance";

// --- Компонент Panel (обновленный) ---
// Using the imported PanelProps type and extending it
interface PanelComponentProps extends PanelProps {
  panelViews: ViewDescriptor[]; // Принимаем список видов для панели
}

export const Panel: React.FC<PanelComponentProps> = React.memo(
  ({ panelViews, style }) => {
    console.log("[Panel] Rendering container...");
    // ID 'OUTPUT' будет зарезервирован для встроенной вкладки
    const [activePanelViewId, setActivePanelViewId] = useState<string | null>(
      "OUTPUT"
    );

    // Обработчики фокуса для панели
    const handleFocus = useCallback(async () => {
      // Make async
      console.log("[Panel] Focus gained");
      try {
        const commandArgs: CommandsExecuteArgs = {
          commandId: CommandIds.CORE_CONTEXT_SET, // Используем ID команды
          args: { key: "panelFocus", value: true }, // Передаем аргументы как массив
        };
        await ipcRendererService.invoke(
          IpcChannels.COMMANDS_EXECUTE,
          commandArgs
        ); // Вызываем команду
      } catch (err) {
        const errorData = err as IpcErrorData;
        console.error(
          "Failed to set context 'panelFocus' to true via command:",
          errorData.message,
          err
        );
      }
    }, []);

    const handleBlur = useCallback(async () => {
      // Make async
      console.log("[Panel] Focus lost");
      try {
        const commandArgs: CommandsExecuteArgs = {
          commandId: CommandIds.CORE_CONTEXT_SET,
          args: { key: "panelFocus", value: false },
        };
        await ipcRendererService.invoke(
          IpcChannels.COMMANDS_EXECUTE,
          commandArgs
        ); // Вызываем команду
      } catch (err) {
        const errorData = err as IpcErrorData;
        console.error(
          "Failed to set context 'panelFocus' to false via command:",
          errorData.message,
          err
        );
      }
    }, []);

    return (
      <div className="panel" style={style}>
        {/* Добавляем обертку для отслеживания фокуса */}
        <div
          className="panel-container"
          tabIndex={-1} // Позволяет элементу получать фокус
          onFocus={handleFocus}
          onBlur={handleBlur}
          style={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            outline: "none",
          }} // Добавляем стили для flex и outline
        >
          <div className="panel-tabs">
            {/* Встроенная вкладка Output */}
            <button
              onClick={() => setActivePanelViewId("OUTPUT")}
              className={activePanelViewId === "OUTPUT" ? "active" : ""}
              title="Output" // Добавляем title
            >
              <Icon name="Terminal" size={14} /> {/* Иконка Output */}
              <span style={{ marginLeft: "4px" }}>Output</span>
            </button>
            {/* Вкладки из расширений */}
            {panelViews.map((view) => (
              <button
                key={view.id}
                onClick={() => setActivePanelViewId(view.id)}
                className={activePanelViewId === view.id ? "active" : ""}
                title={view.name} // Используем name для title
              >
                <Icon name={view.icon || "FileText"} size={14} />{" "}
                {/* Используем иконку из ViewDescriptor */}
                <span style={{ marginLeft: "4px" }}>{view.name}</span>
              </button>
            ))}
            {/* TODO: Добавить кнопку закрытия/скрытия панели */}
          </div>
          {/* Рендерим все виды, но показываем только активный */}
          <div className="panel-content-area">
            {" "}
            {/* Новая обертка */}
            {/* Встроенный OutputLog */}
            <div
              style={{
                display: activePanelViewId === "OUTPUT" ? "block" : "none",
                height: "100%",
              }}
            >
              <OutputLog />
            </div>
            {/* Виды из расширений */}
            {panelViews.map((view) => (
              <PanelViewInstance
                key={view.id}
                view={view}
                isActive={view.id === activePanelViewId}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }
);
Panel.displayName = "Panel";
