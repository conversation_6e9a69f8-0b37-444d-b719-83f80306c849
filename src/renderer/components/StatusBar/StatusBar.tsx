import React, { useMemo, Suspense, useState, useEffect } from "react";
import "./StatusBar.css";
import type { ViewDescriptor } from "@main/services/view.service";
// Import types and service
import { AppGetVersionResult, IpcErrorData } from "@shared/types/ipc";
import { IpcChannels } from "@shared/constants/ipc-channels"; // Импортируем константы
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import { ErrorMonitor } from "@renderer/core/components/ErrorMonitor";

interface StatusBarProps {
  style?: React.CSSProperties; // Принимаем стиль для позиционирования
  items: ViewDescriptor[]; // Добавляем items
}

// Функция для динамической загрузки компонентов статус-бара
// Используем import.meta.glob для статического анализа Vite
const itemComponents = import.meta.glob(
  "/src/extensions/*/renderer/statusbar/*.statusbar.tsx"
);

const loadItemComponent = (
  extensionId: string,
  componentName: string
): React.LazyExoticComponent<React.ComponentType<ViewDescriptor>> | null => {
  // Извлекаем имя директории из extensionId (например, 'ai-books.books' -> 'books')
  const extensionDirName = extensionId;
  if (!extensionDirName) {
    console.error(
      `[StatusBar] Could not extract directory name from extensionId: ${extensionId}`
    );
    return null;
  }

  // Формируем ожидаемый путь к компоненту, используя имя директории
  const path = `/src/extensions/${extensionDirName}/renderer/statusbar/${componentName}.statusbar.tsx`;
  const componentLoader = itemComponents[path];

  if (componentLoader) {
    // Возвращаем лениво загружаемый компонент
    return React.lazy(
      componentLoader as () => Promise<{
        default: React.ComponentType<ViewDescriptor>;
      }>
    );
  }
  console.warn(`[StatusBar] Component not found for path: ${path}`);
  return null;
};

export const StatusBar: React.FC<StatusBarProps> = React.memo(
  ({ style, items }) => {
    const [appVersion, setAppVersion] = useState<string>("N/A");

    // Получаем версию приложения при монтировании
    useEffect(() => {
      const fetchVersion = async () => {
        try {
          // Используем новый сервис и канал
          const version = await ipcRendererService.invoke<AppGetVersionResult>(
            IpcChannels.APP_GET_VERSION
          ); // Используем константу
          setAppVersion(version);
        } catch (err) {
          const errorData = err as IpcErrorData;
          console.error("Failed to get app version:", errorData.message, err);
          setAppVersion("Error"); // Indicate error in status bar
        }
      };
      fetchVersion();
    }, []); // Пустой массив зависимостей - выполнить один раз

    // Разделяем и сортируем элементы по alignment и priority
    const leftItems = useMemo(
      () =>
        items
          .filter(
            (item) => item.location === "statusbar" && item.alignment === "left"
          )
          .sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0)),
      [items]
    );

    const rightItems = useMemo(
      () =>
        items
          .filter(
            (item) =>
              item.location === "statusbar" &&
              (item.alignment === "right" || !item.alignment)
          ) // По умолчанию справа
          .sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0)), // Сортируем справа налево (меньший приоритет правее)
      [items]
    );

    // Функция для рендеринга одного элемента
    const renderItem = (item: ViewDescriptor) => {
      const Component = loadItemComponent(item.extensionId, item.componentName);
      if (!Component) return null; // Не рендерим, если компонент не найден

      // Оборачиваем каждый динамический компонент в Suspense
      // Передаем item.id как key для React
      return (
        <Suspense
          key={item.id}
          fallback={<span className="statusbar-item-loading">...</span>}
        >
          {/* Добавляем title для всплывающей подсказки, если нужно */}
          <div className="statusbar-item" title={item.name}>
            {/* Передаем свойства дескриптора как props компонента */}
            <Component {...item} />
          </div>
        </Suspense>
      );
    };

    return (
      <div className="status-bar" style={style}>
        {/* Левая часть */}
        <div className="statusbar-section statusbar-left">
          {/* Рендерим отсортированные левые элементы */}
          {leftItems.map(renderItem)}
        </div>

        {/* Правая часть */}
        <div className="statusbar-section statusbar-right">
          {/* Рендерим отсортированные правые элементы */}
          {rightItems.map(renderItem)}
          {/* Статичные элементы можно оставить здесь или тоже сделать регистрируемыми */}
          {/* <span className="statusbar-item">Ln 1, Col 1</span> */}
          {/* <span className="statusbar-item">UTF-8</span> */}
          <span className="statusbar-item">{`v${appVersion}`}</span>{" "}
          {/* Отображаем версию приложения */}
          <div className="statusbar-item">
            <ErrorMonitor />
          </div>
        </div>
      </div>
    );
  }
);

StatusBar.displayName = "StatusBar";
