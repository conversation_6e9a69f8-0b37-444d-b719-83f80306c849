.status-bar {
  background-color: var(--app-statusBar-background);
  color: var(--app-statusBar-foreground);
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.85em;
  height: 22px; /* Фиксированная высота */
  flex-shrink: 0;
  user-select: none;
  gap: 15px; /* Добавляем промежуток между левой и правой секциями */
}

/* Стили для секций (левая и правая) */
.statusbar-section {
  display: flex;
  align-items: center;
  gap: 15px; /* Промежуток между элементами внутри секции */
}

/* Стили для каждого элемента статус-бара (динамического или статического) */
.statusbar-item {
  /* Убираем специфичный margin для span */
  /* margin-right: 15px; */
  white-space: nowrap; /* Предотвращаем перенос текста */
  cursor: default; /* Стандартный курсор */
}

/* Убираем отступ у последнего элемента в каждой секции (если нужно) */
/* .statusbar-section > .statusbar-item:last-child {
  margin-right: 0;
} */

/* Стили для индикатора загрузки */
.statusbar-item-loading {
  font-style: italic;
  color: var(--app-statusBar-foreground-muted); /* Используем приглушенный цвет */
}
