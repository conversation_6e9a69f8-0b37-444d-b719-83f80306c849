import React, { useState, useCallback, useRef } from 'react';
import './ResizerSash.css';
import cn from 'classnames';

interface ResizerSashProps {
  orientation: 'vertical' | 'horizontal';
  onResize: (delta: number) => void; // Колбэк с изменением (deltaX или deltaY)
  className?: string;
  onMouseDown?: (event: React.MouseEvent<HTMLDivElement>) => void;
  style?: React.CSSProperties; // Добавляем проп style
}

export const ResizerSash: React.FC<ResizerSashProps> = ({
  orientation,
  onResize,
  className,
  onMouseDown,
  style, // Получаем style из пропсов
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const startPosRef = useRef(0);

  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    startPosRef.current = orientation === 'vertical' ? event.clientX : event.clientY;
    event.preventDefault();

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const currentPos = orientation === 'vertical' ? moveEvent.clientX : moveEvent.clientY;
      const delta = currentPos - startPosRef.current;
      onResize(delta);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

  }, [orientation, onResize]);

  return (
    <div
      className={cn(
        'resizer-sash',
        `resizer-sash-${orientation}`,
        { 'is-dragging': isDragging },
        className
      )}
      style={style} // Применяем стиль
      onMouseDown={(e) => {
          handleMouseDown(e);
          onMouseDown?.(e);
      }}
    />
  );
};
