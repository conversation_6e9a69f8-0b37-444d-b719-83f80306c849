.resizer-sash {
  background-color: transparent; /* По умолчанию невидимый */
  position: relative;
  z-index: 50; /* Выше основного контента, но ниже оверлеев */
  transition: background-color 0.1s ease-in-out;
}

.resizer-sash:hover {
  background-color: var(--app-statusBar-background); /* Подсветка при наведении */
  transition-duration: 0s; /* Мгновенная подсветка */
}

.resizer-sash.is-dragging {
  background-color: var(--app-statusBar-background); /* Подсветка во время перетаскивания */
}

.resizer-sash-vertical {
  cursor: ew-resize; /* Курсор для горизонтального изменения размера */
  width: 5px; /* Ширина области захвата */
  height: 100%;
  /* Позиционирование будет задано в Workbench.css */
}

.resizer-sash-horizontal {
  cursor: ns-resize; /* Курсор для вертикального изменения размера */
  height: 5px; /* Высота области захвата */
  width: 100%;
  /* Позиционирование будет задано в Workbench.css */
}
