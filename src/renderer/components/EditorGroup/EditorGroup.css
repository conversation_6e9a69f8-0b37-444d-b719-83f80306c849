.editor-group {
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Важн<PERSON> для editor-area */
  height: 100%; /* Занимает всю высоту своей ячейки грида */
  background-color: var(--app-editor-background); /* Фон редактора */
}

.editor-tabs {
  background-color: var(--app-editorGroupHeader-tabsBackground);
  flex-shrink: 0; /* Вкладки не должны сжиматься */
  border-bottom: 1px solid var(--app-border);
  padding: 0 5px;
  white-space: nowrap; /* Предотвращаем перенос вкладок */
  overflow-x: auto;
}

/* Use the new class name */
.editor-tab-item {
  display: inline-flex; /* Use flex for better alignment */
  align-items: center; /* Center items vertically */
  padding: 6px 10px 6px 12px; /* Adjust padding (top/bottom reduced) */
  border-right: 1px solid var(--app-border);
  color: var(--app-tab-inactiveForeground);
  background-color: var(--app-tab-inactiveBackground);
  font-size: 0.9em;
  position: relative;
  max-width: 200px; /* Возвращаем максимальную ширину для всей вкладки */
  /* Стили обрезки текста остаются на .tab-title */
  vertical-align: middle; /* Keep this for fallback if flex fails */
}
.editor-tab-item:hover {
  background-color: var(--app-list-hoverBackground);
}

.editor-tab-item.active-tab {
  background-color: var(--app-tab-activeBackground);
  color: var(--app-tab-activeForeground);
  border-bottom-color: transparent; /* Убираем нижнюю границу у активной вкладки */
}

/* Prevent icon from shrinking */
.tab-icon {
  flex-shrink: 0;
  display: inline-flex; /* Helps with vertical alignment if needed */
  align-items: center;
}

/* Style the title span if needed */
.tab-title {
  /* Add specific styles if needed, e.g., margin-right */
  margin-right: 8px; /* Space between title and close button */
  /* Стили для обрезки текста теперь здесь */
  white-space: nowrap; /* Запретить перенос */
  overflow: hidden; /* Скрыть лишнее */
  text-overflow: ellipsis; /* Добавить многоточие */
  /* Можно добавить max-width, если нужно ограничить только текст,
     но обычно родительский max-width на .editor-tab-item предпочтительнее */
}


/* Style the new close button */
.tab-close-button {
  background: none;
  border: none;
  color: inherit; /* Inherit color from parent tab */
  padding: 2px; /* Small padding around the icon */
  margin-left: auto; /* Pushes button to the right */
  flex-shrink: 0; /* Prevent button from shrinking */
  line-height: 0; /* Prevent extra space due to line height */
  cursor: pointer;
  border-radius: 3px; /* Slightly rounded corners */
  opacity: 0.6;
  transition: opacity 0.2s ease, background-color 0.2s ease;
  display: inline-flex; /* Align icon inside */
  align-items: center;
  justify-content: center;
}

.editor-tab-item:hover .tab-close-button,
.editor-tab-item.active-tab .tab-close-button {
  opacity: 1;
}

.tab-close-button:hover {
  background-color: var(--app-list-hoverBackground); /* Subtle background on hover */
  /* color: var(--app-foreground); */
}

/* Optional: Styles for the dirty indicator (circle) */
.tab-close-button .lucide-circle { /* Target the circle icon specifically */
  fill: currentColor; /* Fill the circle with the button's text color */
  /* Add other styles if needed, e.g., slight margin */
}


.editor-area {
  flex-grow: 1;
  overflow: auto; /* Restore scroll here */
  position: relative; /* Для позиционирования плейсхолдера редактора */
}
