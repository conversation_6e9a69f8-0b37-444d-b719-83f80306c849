import React, { useCallback, useMemo } from "react";
import cn from "classnames";
// Import the new store and related types
import { useEditorTabsStore, EditorTab } from "@renderer/state/editorTabsStore";
import { Icon } from "../Icon/Icon";
import { ActiveEditorArea } from "./ActiveEditorArea";
// Removed EditorRefHandle import
import "./EditorGroup.css";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  // UniqueIdentifier not used currently
} from "@dnd-kit/core";
import {
  SortableContext,
  horizontalListSortingStrategy, // Use horizontal strategy
  useSortable,
  // arrayMove is not directly needed here, store handles reordering
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

// --- DraggableTab Component ---
// Update prop type to use EditorTab
interface DraggableTabProps {
  tab: EditorTab;
  isActive: boolean;
  onClick: (id: string) => void;
  onClose: (id: string) => void;
}

// Wrap DraggableTab with React.memo
const DraggableTab: React.FC<DraggableTabProps> = React.memo(
  ({ tab, isActive, onClick, onClose }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging, // Use isDragging for styling
    } = useSortable({ id: tab.id }); // Use tab.id as the sortable ID

    const style: React.CSSProperties = {
      // Add type annotation
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : 1, // Example style for dragging state
      zIndex: isDragging ? 10 : 1, // Ensure dragged item is on top
      position: "relative", // Needed for zIndex
    };
    // No need for grabbing cursor style when dragging

    return (
      <span
        ref={setNodeRef}
        style={style}
        className={cn("editor-tab-item", { "active-tab": isActive })} // Add a common class
        onClick={() => onClick(tab.id)}
        title={tab.title}
        {...attributes} // Spread DnD attributes
        {...listeners} // Spread DnD listeners
      >
        {/* Wrap icon in a span */}
        {tab.icon && (
          <span className="tab-icon">
            <Icon
              name={tab.icon}
              size={14}
              style={{
                marginRight: "5px",
                verticalAlign: "middle",
                pointerEvents: "none",
              }}
            />
          </span>
        )}
        <span className="tab-title" style={{ pointerEvents: "none" }}>
          {tab.title}
        </span>{" "}
        {/* Add class for potential styling, Disable pointer events on title span */}
        {/* Replace text 'x' with Lucide icon */}
        <button
          className="tab-close-button" // Add class for styling
          onClick={(e) => {
            e.stopPropagation();
            onClose(tab.id);
          }}
          style={{ pointerEvents: "auto" }}
          title={tab.isDirty ? "Unsaved Changes" : "Close Tab"} // Dynamic title
        >
          {/* Conditional Icon based on dirty state */}
          <Icon
            name={tab.isDirty ? "Circle" : "X"}
            size={tab.isDirty ? 8 : 14}
          />{" "}
          {/* Use Circle icon (adjust size) or X */}
        </button>
      </span>
    );
  }
); // Close React.memo
DraggableTab.displayName = "DraggableTab";

// --- EditorGroup Component ---
interface EditorGroupProps {
  style?: React.CSSProperties;
}

// Removed EditorGroupRefHandle interface

// Remove forwardRef
export const EditorGroup: React.FC<EditorGroupProps> = React.memo(
  ({ style }) => {
    console.log("[EditorGroup] Rendering...");
    // Removed activeEditorRef

    // Get state and actions from the new editorTabsStore
    const tabs = useEditorTabsStore((state) => state.openTabs);
    const activeTabId = useEditorTabsStore((state) => state.activeTabId);
    const setActiveTabId = useEditorTabsStore((state) => state.setActiveTabId);
    const closeTab = useEditorTabsStore((state) => state.closeTab);
    const reorderTabs = useEditorTabsStore((state) => state.reorderTabs);

    // Configure sensors
    const sensors = useSensors(
      useSensor(PointerSensor, {
        activationConstraint: {
          // Require mouse to move before dragging starts
          distance: 5,
        },
      }),
      useSensor(KeyboardSensor, {
        // Add keyboard support if needed (e.g., coordinateGetter: sortableKeyboardCoordinates)
      })
    );

    // Handle drag end event
    const handleDragEnd = useCallback(
      (event: DragEndEvent) => {
        const { active, over } = event;
        console.log(
          `[EditorGroup] DragEnd: active=${active.id}, over=${over?.id}`
        );

        if (over && active.id !== over.id) {
          const oldIndex = tabs.findIndex((tab) => tab.id === active.id);
          const newIndex = tabs.findIndex((tab) => tab.id === over.id);
          console.log(
            `[EditorGroup] Reordering from index ${oldIndex} to ${newIndex}`
          );

          if (oldIndex !== -1 && newIndex !== -1) {
            reorderTabs(oldIndex, newIndex); // Call the store action
          } else {
            console.warn(
              "[EditorGroup] Could not find dragged or target tab index."
            );
          }
        }
      },
      [tabs, reorderTabs]
    );

    // Get tab IDs for SortableContext
    const tabIds = useMemo(() => tabs.map((tab) => tab.id), [tabs]);
    console.log("[EditorGroup] Tab IDs for SortableContext:", tabIds);

    // Removed useImperativeHandle

    return (
      <div className="editor-group" style={style}>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          {/* Ensure SortableContext receives the correct IDs */}
          <SortableContext
            items={tabIds}
            strategy={horizontalListSortingStrategy}
          >
            <div className="editor-tabs">
              {tabs.map((tab) => (
                <DraggableTab
                  key={tab.id}
                  tab={tab}
                  isActive={tab.id === activeTabId}
                  onClick={setActiveTabId}
                  onClose={closeTab}
                />
              ))}
              {tabs.length === 0 && (
                <span className="no-editor-placeholder">No Editor Open</span>
              )}
            </div>
          </SortableContext>
          {/* DragOverlay could be added here for visual feedback during drag */}
        </DndContext>
        <div className="editor-area">
          {/* ActiveEditorArea no longer needs ref */}
          <ActiveEditorArea />
        </div>
      </div>
    );
  }
);
EditorGroup.displayName = "EditorGroup";
