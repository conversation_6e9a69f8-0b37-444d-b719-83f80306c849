import React, {ComponentType, lazy, LazyExoticComponent, Suspense, useEffect, useMemo,} from "react";
import {EditorTab, useEditorTabsStore} from "@renderer/state/editorTabsStore"; // Keep this
import {IpcChannels} from "@shared/constants/ipc-channels";
import {CommandIds} from "@shared/constants/command-ids";
import {ipcRendererService} from "@renderer/core/services/ipcRendererService";
import {getEditorImporter} from "@renderer/core/editorRegistry";
import {EditorProps} from "@shared/types/ui";
import {CommandsExecuteArgs, IpcErrorData} from "@shared/types/ipc";

// --- EditorInstance ---
interface EditorInstanceProps {
  tab: EditorTab;
  isActive: boolean;
}

// Кэшируем логику ленивой загрузки для каждой комбинации типа/компонента редактора
const editorComponentCache = new Map<
  string,
  LazyExoticComponent<ComponentType<unknown>> | null
>();

function getLazyEditorComponent(
  // extensionId: string, // Больше не требуется
  componentName: string
): LazyExoticComponent<ComponentType<Record<string, unknown>>> | null {
  const cacheKey = componentName; // Используем componentName в качестве ключа
  if (editorComponentCache.has(cacheKey)) {
    return editorComponentCache.get(cacheKey) ?? null;
  }
  
  console.log(
    `[ActiveEditorArea] Creating LazyEditorComponent for: ${componentName}`
  );
  
  // Получаем функцию импорта из реестра
  const importer = getEditorImporter(componentName);
  
  try {
    if (!importer) {
      throw new Error(
        `No importer found for editor component: ${componentName}`
      );
    }
    
    const lazyComponent = lazy(() =>
      importer() // Вызываем функцию импорта, предоставленную реестром
        .then((module) => {
          if (module.default) {
            console.log(
              `[ActiveEditorArea] Successfully imported editor module for: ${componentName}`
            );
            return module;
          } else {
            console.error(
              `[ActiveEditorArea] Editor module loaded, but no default export found for: ${componentName}`
            );
            throw new Error(
              `Editor module for ${componentName} does not have a default export.`
            );
          }
        })
        .catch((error) => {
          console.error(
            `[ActiveEditorArea] Dynamic import failed for editor component: ${componentName}. Error:`,
            error
          );
          // Перебрасываем ошибку, чтобы она была перехвачена внешним блоком catch
          throw error;
        })
    );
    
    editorComponentCache.set(cacheKey, lazyComponent);
    return lazyComponent;
  } catch (error) {
    console.error(
      `[ActiveEditorArea] Failed to create lazy editor component for ${componentName}. Error:`,
      error
    );
    editorComponentCache.set(cacheKey, null); // Кэшируем состояние ошибки (null)
    return null;
  }
}

const EditorInstance = React.memo<EditorInstanceProps>(({tab, isActive}) => {
  // Удален forwardRef
  const {componentName, dataId} = tab;
  console.log(
    `[EditorInstance] Rendering instance for tab: ${tab.id}, isActive: ${isActive}`
  );
  
  // Получаем ленивый компонент (возможно из кэша)
  const LazyEditorComponent = useMemo(() => {
    if (!componentName) return null; // Проверяем только componentName
    return getLazyEditorComponent(componentName); // Передаем только componentName
  }, [componentName]); // Обновлен массив зависимостей
  
  // Подготавливаем пропсы
  const editorProps: EditorProps = useMemo(() => {
    const props: EditorProps = {
      editorId: componentName,
      dataId: dataId,
    };
    return props;
  }, [dataId, componentName]);
  
  const fallbackElement = useMemo(
    () => (
      <div className="editor-area-fallback">Loading {componentName}...</div>
    ),
    [componentName]
  );
  
  // Обрабатываем ошибки загрузки
  if (LazyEditorComponent === null) {
    return (
      <div
        style={{
          display: isActive ? "block" : "none",
          height: "100%",
          padding: "10px",
          color: "red",
        }}
      >
        Error loading editor component: {componentName}
      </div>
    );
  }
  
  // Отображаем фактический компонент редактора только если он активен
  // Это предотвращает первоначальный рендеринг потенциально тяжелого содержимого редактора для неактивных вкладок,
  // но сохраняет компонент смонтированным впоследствии для более быстрого переключения.
  return (
    <div style={{display: isActive ? "block" : "none", height: "100%"}}>
      {/* Всегда рендерим Suspense, когда LazyEditorComponent доступен,
          видимость контролируется стилем display родительского div. */}
      {LazyEditorComponent && (
        <Suspense fallback={fallbackElement}>
          <LazyEditorComponent {...editorProps} />
        </Suspense>
      )}
    </div>
  );
});
EditorInstance.displayName = "EditorInstance";

// --- ActiveEditorArea ---
// Теперь рендерит все открытые вкладки, но отображает только активную
export const ActiveEditorArea: React.FC = React.memo(() => {
  // Удалена обертка forwardRef, если она была добавлена
  console.log("[ActiveEditorArea] Rendering container...");
  
  const openTabs = useEditorTabsStore((state) => state.openTabs);
  const activeTabId = useEditorTabsStore((state) => state.activeTabId);
  
  // Обновляем контекст при изменении активной вкладки
  useEffect(() => {
    console.log(`[ActiveEditorArea] Active tab changed: ${activeTabId}`);
    const activeTab = openTabs.find((t) => t.id === activeTabId);
    const activeEditorType = activeTab?.editorType ?? null;
    const setContextValue = async (key: string, value: unknown) => {
      try {
        const commandArgs: CommandsExecuteArgs = {
          commandId: CommandIds.CORE_CONTEXT_SET, // Cast string constant to CommandId
          args: {key, value},
        };
        await ipcRendererService.invoke(
          IpcChannels.COMMANDS_EXECUTE,
          commandArgs
        );
      } catch (err) {
        const errorData = err as IpcErrorData;
        console.error(
          `Failed to set context '${key}':`,
          errorData.message,
          err
        );
      }
    };
    
    setContextValue("activeEditorType", activeEditorType);
    setContextValue("editorFocus", !!activeTab);
    
    const editorContextValue = activeTab
      ? {
        editorType: activeTab.editorType,
        dataId: activeTab.dataId,
        extensionId: activeTab.extensionId,
        // Добавьте другую релевантную общую информацию при необходимости
      }
      : null;
    setContextValue("activeEditorContext", editorContextValue);
  }, [activeTabId, openTabs]); // Зависимость от openTabs также необходима на случай изменения данных активной вкладки
  
  if (openTabs.length === 0) {
    return <div className="editor-area-placeholder">No editor open</div>; // Заполнитель, когда нет открытых вкладок
  }
  
  return (
    <div className="active-editor-area-wrapper" style={{height: "100%"}}>
      {openTabs.map((tab) => (
        <EditorInstance
          key={tab.id} // Используем стабильный ID вкладки в качестве ключа
          tab={tab}
          isActive={tab.id === activeTabId}
        />
      ))}
    </div>
  );
});
ActiveEditorArea.displayName = "ActiveEditorArea";
