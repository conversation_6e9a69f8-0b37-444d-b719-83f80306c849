/* Стили для Sidebar */

.sidebar {
    /* grid-column и grid-row задаются инлайн */
    display: flex;
    /* Необходимо для работы flex-grow в .sidebar-content */
    flex-direction: column;
    /* Необходимо для работы flex-grow в .sidebar-content */
    overflow: hidden;
    /* !!! Важно: Предотвращает растягивание грид-ячейки сайдбаром !!! */
    height: 100%;
    /* Занимает всю высоту своей ячейки */
    background-color: var(--app-sideBar-background);
    /* Фон на всякий случай */
    border-right: 1px solid var(--app-border);
    /* Граница справа */
    min-height: 0; /* !!! Позволяет сжиматься ниже высоты контента в гриде !!! */
}

.sidebar-header {
  padding: 5px 10px;
  font-weight: bold;
  border-bottom: 1px solid var(--app-border);
  flex-shrink: 0;
  text-transform: uppercase;
  font-size: 0.9em;
  color: var(--app-tab-inactiveForeground);
  /* Убедимся, что фон совпадает с фоном Sidebar */
  background-color: var(--app-sideBar-background);
}

.sidebar-content {
  padding: 10px;
  overflow-y: auto;
  flex-grow: 1;
  background-color: var(--app-sideBar-background); /* Убедимся, что фон совпадает */
  min-height: 0; /* !!! Добавляем, чтобы разрешить сжатие внутри flex-контейнера !!! */
}

/* Стили для общих элементов внутри видов Sidebar */
.sidebar-content ul {
  list-style: none;
  padding-left: 10px;
  margin: 5px 0;
}

.sidebar-content li {
  padding: 3px 8px; /* Немного увеличим padding */
  cursor: pointer;
  border-radius: 3px;
  margin-bottom: 2px; /* Небольшой отступ между элементами */
  white-space: nowrap; /* Предотвращаем перенос текста */
  overflow: hidden;
  text-overflow: ellipsis; /* Добавляем многоточие, если текст не влезает */
}

.sidebar-content li:hover {
  background-color: var(--app-list-hoverBackground);
}

/* Стили для details/summary (например, для глав) */
.sidebar-content details {
  margin-bottom: 5px;
}

.sidebar-content details > summary {
  padding: 3px 5px;
  border-radius: 3px;
  font-weight: bold;
}

.sidebar-content details > summary:hover {
   background-color: var(--app-list-hoverBackground);
}

/* Стили для кнопок внутри Sidebar */
.sidebar-content button {
  /* Можно сделать их менее яркими, чем основные кнопки */
  background-color: var(--app-input-background);
  color: var(--app-foreground);
  padding: 3px 8px;
  font-size: 0.9em;
  margin-top: 5px; /* Отступ сверху */
}

.sidebar-content button:hover {
  background-color: var(--app-list-hoverBackground);
}

/* Стили для select внутри Sidebar */
.sidebar-content select {
  width: 100%;
  margin-bottom: 10px;
}

.sidebar-content hr {
  border: none;
  border-top: 1px solid var(--app-border);
  margin: 10px 0;
}
