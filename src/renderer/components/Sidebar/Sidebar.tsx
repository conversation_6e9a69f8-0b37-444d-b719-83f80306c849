import type { ViewDescriptor } from "@main/services/view.service";
import React, {
  Suspense,
  lazy,
  useMemo,
  ComponentType,
  LazyExoticComponent,
} from "react";
import { getViewImporter } from "@renderer/core/viewRegistry"; // Import the new registry function
import { ViewProps } from "@shared/types/ui";
import "./Sidebar.css";

interface SidebarProps {
  sidebarViews: ViewDescriptor[]; // Принимаем все виды для сайдбара
  activeViewId: string | null; // Принимаем ID активного *вида*
  style?: React.CSSProperties;
  // openOrFocusTab проп больше не нужен здесь, он будет передаваться каждому ViewInstance
}

// --- ViewInstance ---
// Компонент для рендеринга одного экземпляра вида
interface ViewInstanceProps {
  view: ViewDescriptor;
  isActive: boolean;
}

// Кэш для лениво загружаемых компонентов видов
const viewComponentCache = new Map<
  string,
  React.LazyExoticComponent<React.ComponentType<ViewProps>> | null
>();

// Функция для получения ленивого компонента (с кэшированием)
function getLazyViewComponent(
  // extensionId: string, // No longer needed here
  componentName: string
): React.LazyExoticComponent<React.ComponentType<ViewProps>> | null {
  const cacheKey = componentName; // Use componentName as the key
  if (viewComponentCache.has(cacheKey)) {
    return viewComponentCache.get(cacheKey) ?? null;
  }

  console.log(`[Sidebar] Creating LazyViewComponent for: ${componentName}`);

  // Get the importer function from the registry
  const importer = getViewImporter(componentName);

  try {
    if (!importer) {
      throw new Error(`No importer found for component: ${componentName}`);
    }

    const lazyComponent = lazy(() =>
      importer() // Call the importer function provided by the registry
        .then((module) => {
          if (module.default) {
            console.log(
              `[Sidebar] Successfully imported view module for: ${componentName}`
            );
            return module;
          } else {
            console.error(
              `[Sidebar] View module loaded, but no default export found for: ${componentName}`
            );
            throw new Error(
              `View module for ${componentName} does not have a default export.`
            );
          }
        })
        .catch((error) => {
          console.error(
            `[Sidebar] Dynamic import failed for view component: ${componentName}. Error:`,
            error
          );
          // Re-throw the error to be caught by the outer catch block
          throw error;
        })
    ) as LazyExoticComponent<ComponentType<ViewProps>>;

    viewComponentCache.set(cacheKey, lazyComponent);
    return lazyComponent;
  } catch (error) {
    console.error(
      `[Sidebar] Failed to create lazy view component for ${componentName}. Error:`,
      error
    );
    viewComponentCache.set(cacheKey, null); // Кэшируем ошибку
    return null;
  }
}

const ViewInstance = React.memo<ViewInstanceProps>(({ view, isActive }) => {
  const { componentName, name } = view;
  console.log(
    `[ViewInstance] Rendering instance for view: ${view.id}, isActive: ${isActive}`
  );

  // Получаем ленивый компонент
  const LazyViewComponent = useMemo(() => {
    if (!componentName) return null; // Check only componentName
    return getLazyViewComponent(componentName); // Pass only componentName
  }, [componentName]); // Update dependency array

  const fallbackElement = useMemo(() => <div>Loading {name}...</div>, [name]);

  // Обработка ошибки загрузки компонента
  if (LazyViewComponent === null) {
    return (
      <div
        style={{
          display: isActive ? "block" : "none",
          height: "100%",
          padding: "10px",
          color: "red",
        }}
      >
        Error loading view: {name}
      </div>
    );
  }

  // Рендерим компонент, скрывая неактивные
  return (
    <div style={{ display: isActive ? "block" : "none", height: "100%" }}>
      <Suspense fallback={fallbackElement}>
        <LazyViewComponent viewId={view.id} />
      </Suspense>
    </div>
  );
});
ViewInstance.displayName = "ViewInstance";

// --- Sidebar Component ---
// Теперь рендерит все виды и показывает только активный
export const Sidebar: React.FC<SidebarProps> = React.memo(
  ({ sidebarViews, activeViewId, style }) => {
    console.log(
      `[Sidebar] Rendering container with activeViewId: ${activeViewId}`
    );

    if (!sidebarViews || sidebarViews.length === 0) {
      return (
        <div className="sidebar" style={style}>
          <div className="sidebar-content">No views available</div>
        </div>
      );
    }

    return (
      <div className="sidebar" style={style}>
        <div className="sidebar-content">
          {sidebarViews.map((view) => (
            <ViewInstance
              key={view.id} // Стабильный ключ
              view={view}
              isActive={view.id === activeViewId}
            />
          ))}
        </div>
      </div>
    );
  }
);
Sidebar.displayName = "Sidebar";
