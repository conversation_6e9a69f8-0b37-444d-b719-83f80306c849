import { contextBridge, ipc<PERSON>enderer, IpcRendererEvent } from 'electron';
import { IpcEventArgs, IpcInvokeResult } from '@shared/types/ipc';

// Определение структуры API, доступного для Renderer процесса
export interface IElectronAPI {
  getElectronVersion: () => string;
  invoke: <TResult = unknown, TArgs = unknown>(
    channel: string,
    invokeArgs: TArgs
  ) => Promise<IpcInvokeResult<TResult>>;
  onMainEvent: <TData = unknown>(
    channel: string,
    listener: (event: IpcRendererEvent, args: IpcEventArgs<TData>) => void
  ) => () => void;
}

const electronAPI: IElectronAPI = {
  /**
   * Возвращает текущую версию Electron.
   */
  getElectronVersion: () => process.versions.electron,

  /**
   * Вызывает обработчик IPC-канала в Main процессе.
   * ipcRendererService будет обрабатывать распаковку IpcInvokeResult.
   *
   * @param channel Имя IPC-канала.
   * @param invokeArgs Аргументы для обработчика.
   * @returns Промис с результатом от ipcRenderer.invoke.
   */
  invoke: <TResult = unknown, TArgs = unknown>(
    channel: string,
    invokeArgs: TArgs
  ): Promise<IpcInvokeResult<TResult>> => {
    return ipcRenderer.invoke(channel, invokeArgs);
  },

  /**
   * Регистрирует слушателя для события от Main процесса.
   *
   * @param channel Имя IPC-канала.
   * @param listener Функция обратного вызова. Получит IpcRendererEvent и IpcEventArgs.
   * @returns Функция для удаления слушателя.
   */
   onMainEvent: <TData = unknown>(
    channel: string,
    listener: (event: IpcRendererEvent, args: IpcEventArgs<TData>) => void
  ): () => void => {
    ipcRenderer.on(channel, listener);

    return () => {
      ipcRenderer.removeListener(channel, listener);
    };
  },
};

// Предоставляем API для Renderer процесса
try {
  contextBridge.exposeInMainWorld('electronAPI', electronAPI);
} catch (error) {
  console.error('Не удалось предоставить electronAPI через contextBridge:', error);
}
