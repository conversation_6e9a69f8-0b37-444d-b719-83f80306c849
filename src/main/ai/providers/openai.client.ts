import OpenAI from 'openai';
import type {JSONSchema7} from 'json-schema';
import type {LoggingServiceAPI} from '@services/logging.service';
import type {IpcErrorData} from '@shared/types/ipc';
import {AICompletionResponse, AIProviderClient, AIProviderConfig} from "@shared/types/ai";

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class OpenAIClient implements AIProviderClient {
  private readonly openai: OpenAI;
  private readonly logger: LoggingServiceAPI;
  private readonly maxRetries = 3;
  private readonly initialDelay = 1000; // 1 second
  
  constructor(apiKey: string, logger: LoggingServiceAPI) {
    this.logger = logger.createScopedLogger('OpenAIClient');
    if (!apiKey) {
      this.logger.error('API Key is missing!');
      throw new Error('OpenAI API Key is required.');
    }
    this.openai = new OpenAI({apiKey});
    this.logger.info('Initialized.');
  }
  
  async generateStructuredOutput(prompt: string, outputSchema: JSONSchema7, options?: AIProviderConfig): Promise<AICompletionResponse> {
    const model = options?.model || 'gpt-3.5-turbo'; // Default model for MVP
    const temperature = options?.temperature ?? 0.7;
    const maxTokens = options?.maxTokens ?? 1024;
    
    this.logger.info(`Generating structured output with model ${model}`, {temperature, maxTokens});
    
    // Prepare the request for structured output using response_format
    // Note: This requires the prompt to explicitly ask for JSON matching the schema.
    // Function calling (`tools`) might be more robust but adds complexity.
    // TODO: use real structured output from docs
    const requestPayload: OpenAI.Chat.ChatCompletionCreateParamsNonStreaming = {
      model: model,
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant designed to output JSON conforming to the provided schema.'
        },
        {
          role: 'user',
          content: `${prompt}\n\nOutput JSON conforming to the following schema:\n${JSON.stringify(outputSchema)}`
        }
      ],
      temperature: temperature,
      max_tokens: maxTokens,
      response_format: {type: "json_object"}
    };
    
    let retries = 0;
    while (retries < this.maxRetries) {
      try {
        const completion = await this.openai.chat.completions.create(requestPayload);
        
        const responseContent = completion.choices[0]?.message?.content;
        if (!responseContent) {
          throw new Error('OpenAI response content is empty.');
        }
        
        // Corrected logger call: Wrap string in an object
        this.logger.info('Received raw response from OpenAI:', {responseContent});
        
        try {
          const parsedJson = JSON.parse(responseContent);
          this.logger.info('Successfully generated and parsed structured output.');
          return parsedJson; // Return the parsed JSON object
        } catch (parseError) {
          this.logger.error('Failed to parse OpenAI response as JSON', {responseContent, parseError});
          throw new Error(`Failed to parse AI response as JSON: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
        }
        
      } catch (error: unknown) {
        const isRetryable = error instanceof OpenAI.APIConnectionError ||
          error instanceof OpenAI.RateLimitError ||
          error instanceof OpenAI.InternalServerError;
        
        this.logger.error(`OpenAI API call failed (Attempt ${retries + 1}/${this.maxRetries})`, {error});
        
        if (isRetryable && retries < this.maxRetries - 1) {
          const waitTime = this.initialDelay * Math.pow(2, retries);
          this.logger.warn(`Retryable error encountered. Retrying in ${waitTime}ms...`);
          await delay(waitTime);
          retries++;
        } else {
          // Non-retryable error or max retries reached
          const message = error instanceof Error ? error.message : 'Unknown OpenAI API error';
          throw new Error(`OpenAI API Error: ${message}`); // Rethrow after logging
        }
      }
    }
    // Should not be reached if maxRetries > 0, but satisfies TypeScript
    throw new Error('OpenAI API call failed after maximum retries.');
  }
  
  // Implement actual streaming
  async* streamOutput(
    prompt: string,
    options?: AIProviderConfig
  ): AsyncGenerator<string | IpcErrorData, void, unknown> {
    const model = options?.model || 'gpt-3.5-turbo';
    const temperature = options?.temperature ?? 0.7;
    const maxTokens = options?.maxTokens ?? 1024;
    
    this.logger.info(`Streaming output with model ${model}`, {temperature, maxTokens});
    
    const requestPayload: OpenAI.Chat.ChatCompletionCreateParamsStreaming = {
      model: model,
      messages: [
        // Simple prompt structure for autocomplete
        {role: 'system', content: 'You are a helpful writing assistant. Continue the text provided by the user.'},
        {role: 'user', content: prompt}
      ],
      temperature: temperature,
      max_tokens: maxTokens,
      stream: true,
    };
    
    try {
      const stream = await this.openai.chat.completions.create(requestPayload);
      this.logger.info('Streaming started.');
      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          yield content; // Yield the token string
        }
      }
      this.logger.info('Streaming finished.');
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Unknown OpenAI API error during streaming';
      this.logger.error('OpenAI API streaming call failed', {error});
      // Yield an error object that can be identified by the caller
      yield {code: 'ai_streaming_error', message: `OpenAI Streaming Error: ${message}`};
    }
  }
}
