import { Disposable } from '@shared/types/common';

export class DisposableCollection {
  private disposables: Disposable[] = [];
  private _isDisposed = false;

  async push(disposable: Disposable): Promise<Disposable> {
    if (this._isDisposed) {
      await disposable.dispose();
    } else {
      this.disposables.push(disposable);
    }
    return {
      dispose: async () => {
        if (!this._isDisposed) {
          const index = this.disposables.indexOf(disposable);
          if (index !== -1) {
            this.disposables.splice(index, 1);
          }
        }
      }
    };
  }

  async dispose() {
    if (this._isDisposed) {
      return;
    }
    this._isDisposed = true;
    while (this.disposables.length > 0) {
      try {
        this.disposables.pop()?.dispose();
      } catch (e) {
        console.error('Error disposing item in DisposableCollection:', e);
      }
    }
  }

  indexOf(currentService1: Disposable) {
      return this.disposables.indexOf(currentService1);
  }

  splice(index: number, number: number) {
    return this.disposables.splice(index, number);
  }
}
