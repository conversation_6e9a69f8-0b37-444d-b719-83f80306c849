import { app } from 'electron';
import started from 'electron-squirrel-startup';
import { MainApplication } from '@main/app';
import { logger } from '@services/logging.service';
// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (started) {
  app.quit();
}

// --- Глобальная обработка неперехваченных исключений ---
// Важно добавить это для отладки неожиданных ошибок в основном процессе
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Main process exception:', error.message);
  app.exit(1);
});

process.on('unhandledRejection', (reason) => {
  logger.error('Unhandled Main process rejection:', reason);
});


try {
  logger.info('Creating MainApplication instance...');
  new MainApplication(logger);
  logger.info('MainApplication instance created. Waiting for app events...');
} catch {
  console.error('Failed to instantiate MainApplication');
  app.exit(1);
}
