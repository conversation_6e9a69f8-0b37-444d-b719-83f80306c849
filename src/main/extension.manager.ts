import {LoggingServiceAPI} from '@services/logging.service';
import {ExtensionRegistry} from './extensions/extension.registry';
import {ServiceManager} from './service.manager';
import {Disposable} from '@shared/types/common';

/**
 * API ExtensionService для внутреннего использования или для других сервисов ядра.
 */
export interface ExtensionManagerAPI extends Disposable {
  discoverExtensions(): Promise<void>;
  activateAll(): Promise<void>;
  deactivateAll(): Promise<void>;
  getApi<T>(extensionId: string): Promise<T | undefined>;
}

/**
 * Сервис управления жизненным циклом расширений (поиск, активация, деактивация).
 * Инкапсулирует ExtensionRegistry.
 */
export class ExtensionManager implements ExtensionManagerAPI {
  private extensionRegistry: ExtensionRegistry;
  private isDisposed = false;
  private readonly logger: LoggingServiceAPI;
  private serviceManager: ServiceManager;

  constructor(logger: LoggingServiceAPI, registry: ServiceManager) {
    this.serviceManager = registry;
    this.logger = logger.createScopedLogger('ExtensionManager');
    const coreApisFromRegistry = registry.getCoreServicesAPI();
    this.extensionRegistry = new ExtensionRegistry(coreApisFromRegistry);
  }

  async discoverExtensions(): Promise<void> {
    if (this.isDisposed) {
      this.logger.warn('Попытка поиска расширений после уничтожения сервиса.');
      return;
    }
    this.logger.info('Поиск расширений...');
    try {
      await this.extensionRegistry.discoverExtensions();
    } catch (err) {
      this.logger.error('Ошибка при поиске расширений:', err);
      throw err;
    }
  }

  async activateAll(): Promise<void> {
    if (this.isDisposed) {
      this.logger.warn('Попытка активации расширений после уничтожения сервиса.');
      return;
    }
    this.logger.info('Активация расширений...');
    try {
      await this.extensionRegistry.activateAll();
      this.logger.info('Активация расширений завершена.');
    } catch (err) {
      this.logger.error('Ошибка при активации расширений:', err);
    }
  }

  async deactivateAll(): Promise<void> {
    if (this.isDisposed) {
      return;
    }
    this.logger.info('Деактивация расширений...');
    await this.extensionRegistry.deactivateAll();
  }

  async getApi<T>(extensionId: string): Promise<T | undefined> {
    if (this.isDisposed) {
      this.logger.warn(`Попытка получить API для '${extensionId}' после уничтожения сервиса.`);
      return undefined;
    }
    // Делегируем напрямую в реестр
    return this.extensionRegistry.getApi<T>(extensionId);
  }

  public async dispose() {
    if (this.isDisposed) {
      return;
    }
    this.logger.info('Уничтожение ExtensionManager...');
    // Деактивируем все расширения при уничтожении
    this.deactivateAll().finally(() => {
      this.isDisposed = true;
      this.logger.info('ExtensionManager уничтожен.');
      // ExtensionRegistry не требует отдельного уничтожения
    });
  }
}
