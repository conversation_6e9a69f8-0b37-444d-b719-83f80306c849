import {LoggingServiceAPI} from '@services/logging.service';
import {MenuServiceAPI} from '@services/menu.service';
import {IContributionHandler} from '../contributionHandler.interface';
import {ExtensionManifest} from '../extension.types';
import {Disposable} from "@shared/types/common";
import {ExtensionMenuContribution} from "@shared/types/extensions";

/**
 * Обработчик точек вклада типа 'menus'.
 * Регистрирует пункты меню из манифеста расширения в MenuService.
 */
export class MenuContributionHandler implements IContributionHandler {
  readonly contributionType = 'menus';
  
  private readonly logger: LoggingServiceAPI;
  private readonly menuService: MenuServiceAPI;
  
  constructor(logger: LoggingServiceAPI, menuService: MenuServiceAPI) {
    this.logger = logger.createScopedLogger('MenuContributionHandler');
    this.menuService = menuService;
  }
  
  processContributions(extensionId: string, manifest: ExtensionManifest): Disposable[] {
    const subscriptions: Disposable[] = [];
    const contributions = manifest.contributes;
    
    if (!contributions || !contributions.menus) {
      return subscriptions;
    }
    
    try {
      Object.entries(contributions.menus).forEach(([contextKey, items]) => {
        const menuItemsWithExtensionId = items.map(item => {
          return {
            ...item,
            extensionId: extensionId, // Гарантируем, что extensionId установлен
          } as ExtensionMenuContribution;
        });
        
        // Регистрируем все пункты меню для данного контекста
        const disposable = this.menuService.registerMenuItems(contextKey, menuItemsWithExtensionId);
        
        subscriptions.push(disposable);
      });
    } catch (error) {
      this.logger.error(`[${extensionId}] Ошибка обработки вкладов меню:`, error);
    }
    
    return subscriptions;
  }
}
