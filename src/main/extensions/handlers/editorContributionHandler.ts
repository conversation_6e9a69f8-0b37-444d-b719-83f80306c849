import {LoggingServiceAPI} from '@services/logging.service';
import {EditorServiceAPI} from '@services/editor.service';
import {IContributionHandler} from '../contributionHandler.interface';
import {ExtensionManifest} from '../extension.types';
import {Disposable} from '@shared/types/common';

/**
 * Обработчик точек вклада типа 'editors'.
 * Регистрирует редакторы из манифеста расширения в EditorService.
 */
export class EditorContributionHandler implements IContributionHandler {
  readonly contributionType = 'editors';
  
  private readonly logger: LoggingServiceAPI;
  private readonly editorService: EditorServiceAPI;
  
  constructor(logger: LoggingServiceAPI, editorService: EditorServiceAPI) {
    this.logger = logger.createScopedLogger('EditorContributionHandler');
    this.editorService = editorService;
  }
  
  processContributions(extensionId: string, manifest: ExtensionManifest): Disposable[] {
    const subscriptions: Disposable[] = [];
    const contributions = manifest.contributes;
    
    if (!contributions || !contributions.editors) {
      return subscriptions;
    }
    
    try {
      contributions.editors.forEach(editor => {
        this.logger.debug(`[${extensionId}] Регистрация редактора: ${editor.editorType}`);
        subscriptions.push(this.editorService.registerEditorProvider({
          ...editor,
          extensionId: extensionId, // Гарантируем, что extensionId установлен
        }));
      });
    } catch (error) {
      this.logger.error(`[${extensionId}] Ошибка обработки вкладов редакторов:`, error);
    }
    
    return subscriptions;
  }
}
