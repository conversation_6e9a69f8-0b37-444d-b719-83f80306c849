import {LoggingServiceAPI} from '@services/logging.service';
import {ViewServiceAPI} from '@services/view.service';
import {IContributionHandler} from '../contributionHandler.interface';
import {ExtensionManifest} from '../extension.types';
import {Disposable} from "@shared/types/common";

/**
 * Обработчик точек вклада типа 'views'.
 * Регистрирует представления из манифеста расширения в ViewService.
 */
export class ViewContributionHandler implements IContributionHandler {
  readonly contributionType = 'views';
  
  private readonly logger: LoggingServiceAPI;
  private readonly viewService: ViewServiceAPI;
  
  constructor(logger: LoggingServiceAPI, viewService: ViewServiceAPI) {
    this.logger = logger.createScopedLogger('ViewContributionHandler');
    this.viewService = viewService;
  }
  
  processContributions(extensionId: string, manifest: ExtensionManifest): Disposable[] {
    const subscriptions: Disposable[] = [];
    const contributions = manifest.contributes;
    
    if (!contributions || !contributions.views) {
      return subscriptions;
    }
    
    try {
      Object.entries(contributions.views).forEach(([containerId, views]) => {
        views.forEach(view => {
          this.logger.debug(`[${extensionId}] Регистрация представления: ${view.id} в контейнере ${containerId}`);
          subscriptions.push(this.viewService.registerView({
            ...view,
            extensionId: extensionId, // Гарантируем, что extensionId установлен
            location: view.location ?? 'sidebar', // Местоположение по умолчанию, если не указано
          }));
        });
      });
    } catch (error) {
      this.logger.error(`[${extensionId}] Ошибка обработки вкладов представлений:`, error);
    }
    
    return subscriptions;
  }
}
