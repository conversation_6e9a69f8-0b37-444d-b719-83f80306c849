import {LoggingServiceAPI} from '@services/logging.service';
import {CommandServiceAPI} from '@services/command.service';
import {IContributionHandler} from '../contributionHandler.interface';
import {ExtensionManifest} from '../extension.types';
import {Disposable} from '@shared/types/common';

/**
 * Обработчик точек вклада типа 'commands'.
 * Регистрирует команды из манифеста расширения в CommandService.
 *
 * Примечание: Команды в манифесте только декларативные, без обработчиков.
 * Обработчики команд регистрируются в методе activate() расширения через context.commands.registerCommand.
 */
export class CommandContributionHandler implements IContributionHandler {
  readonly contributionType = 'commands';
  
  private readonly logger: LoggingServiceAPI;
  private readonly commandService: CommandServiceAPI;
  
  constructor(logger: LoggingServiceAPI, commandService: CommandServiceAPI) {
    this.logger = logger.createScopedLogger('CommandContributionHandler');
    this.commandService = commandService;
  }
  
  processContributions(extensionId: string, manifest: ExtensionManifest): Disposable[] {
    // Команды в манифесте только декларативные, без обработчиков.
    // Фактическая регистрация с обработчиками происходит в методе activate() расширения через context.commands.registerCommand.
    // Однако, мы должны зарегистрировать команды в CommandService, чтобы они были доступны в меню и через горячие клавиши.
    
    const subscriptions: Disposable[] = [];
    const contributions = manifest.contributes;
    
    if (!contributions || !contributions.commands) {
      return subscriptions;
    }
    
    try {
      this.logger.info(`[${extensionId}] Processing ${contributions.commands.length} command declarations from manifest.`);
      
      // Регистрируем команды в CommandService без обработчиков
      // Обработчики будут добавлены позже в методе activate() расширения
      for (const command of contributions.commands) {
        this.logger.debug(`[${extensionId}] Registering command: ${command.command}`);
        
        // Регистрируем команду с пустым обработчиком
        // Реальный обработчик будет добавлен в методе activate() расширения
        const disposable = this.commandService.registerCommand({
          id: command.command,
          title: command.title,
          category: command.category,
          handler: async () => {
            this.logger.warn(`Command ${command.command} was called but has no handler. This command should be registered with a handler in the extension's activate() method.`);
            return undefined;
          }
        });
        
        subscriptions.push(disposable);
      }
      
    } catch (error) {
      this.logger.error(`[${extensionId}] Error processing command contributions:`, error);
    }
    
    return subscriptions;
  }
}
