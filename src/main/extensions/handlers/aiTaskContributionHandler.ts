import {LoggingServiceAPI} from '@services/logging.service';
import {AIServiceAPI} from '@services/ai.service';
import {IContributionHandler} from '../contributionHandler.interface';
import {ExtensionManifest} from '../extension.types';
import {Disposable} from '@shared/types/common';

/**
 * Обработчик точек вклада типа 'aiTasks'.
 * Регистрирует задачи ИИ из манифеста расширения в AIService.
 */
export class AITaskContributionHandler implements IContributionHandler {
  readonly contributionType = 'aiTasks';
  
  private readonly logger: LoggingServiceAPI;
  private readonly aiService: AIServiceAPI;
  
  constructor(logger: LoggingServiceAPI, aiService: AIServiceAPI) {
    this.logger = logger.createScopedLogger('AITaskContributionHandler');
    this.aiService = aiService;
  }
  
  processContributions(extensionId: string, manifest: ExtensionManifest): Disposable[] {
    const subscriptions: Disposable[] = [];
    const contributions = manifest.contributes;
    
    if (!contributions || !contributions.aiTasks || !this.aiService) {
      return subscriptions;
    }
    
    try {
      contributions.aiTasks.forEach(task => {
        this.logger.debug(`[${extensionId}] Регистрация ИИ-задачи: ${task.taskId}`);
        subscriptions.push(this.aiService.registerTask(task));
      });
    } catch (error) {
      this.logger.error(`[${extensionId}] Ошибка обработки вкладов ИИ-задач:`, error);
    }
    
    return subscriptions;
  }
}
