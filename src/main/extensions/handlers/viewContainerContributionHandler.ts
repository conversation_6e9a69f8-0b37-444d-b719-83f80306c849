import {LoggingServiceAPI} from '@services/logging.service';
import {ViewServiceAPI} from '@services/view.service';
import {IContributionHandler} from '../contributionHandler.interface';
import {ExtensionManifest} from '../extension.types';
import {Disposable} from "@shared/types/common";

/**
 * Обработчик точек вклада типа 'viewContainers'.
 * Регистрирует контейнеры представлений из манифеста расширения в ViewService.
 */
export class ViewContainerContributionHandler implements IContributionHandler {
  readonly contributionType = 'viewContainers';
  
  private readonly logger: LoggingServiceAPI;
  private readonly viewService: ViewServiceAPI;
  
  constructor(logger: LoggingServiceAPI, viewService: ViewServiceAPI) {
    this.logger = logger.createScopedLogger('ViewContainerContributionHandler');
    this.viewService = viewService;
  }
  
  processContributions(extensionId: string, manifest: ExtensionManifest): Disposable[] {
    const subscriptions: Disposable[] = [];
    const contributions = manifest.contributes;
    
    if (!contributions || !contributions.viewContainers) {
      return subscriptions;
    }
    
    try {
      contributions.viewContainers.forEach(container => {
        this.logger.debug(`[${extensionId}] Регистрация контейнера представлений: ${container.id}`);
        subscriptions.push(this.viewService.registerViewContainer({
          ...container
        }));
      });
    } catch (error) {
      this.logger.error(`[${extensionId}] Ошибка обработки вкладов контейнеров представлений:`, error);
    }
    
    return subscriptions;
  }
}
