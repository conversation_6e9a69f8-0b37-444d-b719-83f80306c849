import {CommandServiceAP<PERSON>} from '@services/command.service';
import {ViewContainerDescriptor, ViewDescriptor, ViewServiceAPI} from '@services/view.service';
import {Editor<PERSON><PERSON>ider, EditorServiceAPI} from '@services/editor.service';
import {Disposable} from '@shared/types/common';
import {IpcServiceAPI} from "@services/ipc.service";
import {ConfigurationServiceAPI} from "@services/configuration.service";
import {StorageServiceAPI} from "@services/storage.service";
import {DialogServiceAPI} from "@services/dialog.service";
import {NotificationServiceAPI} from "@services/notification.service";
import {LoggingServiceAPI} from "@services/logging.service";
import {GitServiceAPI} from "@services/git.service";
import {ContextServiceAPI} from "@services/context.service";
import {AIServiceAPI} from "@services/ai.service";
import {AppLifecycle} from "@shared/types/lifecycle-events";
import {Command} from "@shared/types/commands";
import {MenuService<PERSON>I} from "@services/menu.service";
import {AITaskDefinition} from "@shared/types/ai";

// --- Contribution Point Interfaces ---
export type CommandContribution = { command: string } & Omit<Command, 'handler'>;
export type MenuItemsContribution = Record<string, MenuItemsContribution[]>;

// --- Manifest Structure ---

export interface ExtensionContributions {
  commands?: CommandContribution[]; // Declarative only
  views?: Record<string, ViewDescriptor[]>; // Use ViewDescriptor
  viewContainers?: ViewContainerDescriptor[]; // Use ViewContainerDescriptor
  menus?: MenuItemsContribution;
  editors?: EditorProvider[]; // Use EditorProvider
  aiTasks?: AITaskDefinition[];
}

// Basic structure for the extension's package.json
export interface ExtensionManifest {
  name: string;
  publisher: string;
  version: string;
  main: string; // Relative path to the compiled main entry point
  activationEvents?: string[]; // TODO: Implement activation events later
  contributes?: ExtensionContributions;
}

export interface ExtensionContext {
  subscriptions: Disposable[]; // Renamed IDisposable
  commands: CommandServiceAPI;
  views: ViewServiceAPI;
  storage: StorageServiceAPI;
  settings: ConfigurationServiceAPI;
  ipc: IpcServiceAPI;
  notifications: NotificationServiceAPI;
  logger: LoggingServiceAPI;
  dialogs: DialogServiceAPI;
  context: ContextServiceAPI;
  menus: MenuServiceAPI;
  editors: EditorServiceAPI;
  git: GitServiceAPI;
  ai: AIServiceAPI;
  lifecycle: AppLifecycle; // Renamed IAppLifecycle
}

/**
 * Extension API interface
 * This is the interface that extensions can expose to other extensions
 */
export type ExtensionApi = Record<string, unknown>;

export interface ExtensionModule {
  activate(context: ExtensionContext): Promise<ExtensionApi | undefined>;
  
  deactivate?(): Promise<void>;
}

export interface LoadedExtension {
  manifest: ExtensionManifest;
  id: string;
  module: ExtensionModule;
  api?: ExtensionApi;
  isActive: boolean;
  subscriptions: Disposable[]; // Renamed IDisposable
  compiledPath: string;
}
