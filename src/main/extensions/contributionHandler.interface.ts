import {ExtensionManifest} from './extension.types';
import {Disposable} from '@shared/types/common';

/**
 * Интерфейс для обработчиков точек вклада (contribution points) из манифеста расширения.
 * Каждый сервис, который обрабатывает точки вклада, должен реализовать этот интерфейс.
 */
export interface IContributionHandler {
  /**
   * Уникальный идентификатор типа точки вклада, который обрабатывает этот обработчик.
   * Например, 'commands', 'views', 'editors', 'menus', 'aiTasks', и т.д.
   */
  readonly contributionType: string;
  
  /**
   * Обрабатывает точки вклада из манифеста расширения.
   *
   * @param extensionId Идентификатор расширения
   * @param manifest Манифест расширения
   * @returns Массив объектов Disposable, которые будут добавлены в subscriptions расширения
   */
  processContributions(extensionId: string, manifest: ExtensionManifest): Disposable[];
}
