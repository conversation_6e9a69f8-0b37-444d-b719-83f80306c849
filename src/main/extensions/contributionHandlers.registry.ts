import {LoggingServiceAPI} from '@services/logging.service';
import {IContributionHandler} from './contributionHandler.interface';
import {ExtensionContributions, ExtensionManifest} from './extension.types';
import {Disposable} from '@shared/types/common';

/**
 * Реестр обработчиков точек вклада (contribution points).
 * Хранит все зарегистрированные обработчики и вызывает их при обработке манифеста расширения.
 */
export class ContributionHandlersRegistry {
  private readonly handlers = new Map<string, IContributionHandler>();
  private readonly logger: LoggingServiceAPI;
  
  constructor(logger: LoggingServiceAPI) {
    this.logger = logger.createScopedLogger('ContributionHandlersRegistry');
  }
  
  /**
   * Регистрирует обработчик точек вклада.
   *
   * @param handler Обр<PERSON>ботчик точек вклада
   */
  registerHandler(handler: IContributionHandler): void {
    const {contributionType} = handler;
    
    if (this.handlers.has(contributionType)) {
      this.logger.warn(`Обработчик для типа вклада '${contributionType}' уже зарегистрирован. Перезаписываем.`);
    }
    
    this.handlers.set(contributionType, handler);
    this.logger.info(`Зарегистрирован обработчик для типа вклада: ${contributionType}`);
  }
  
  /**
   * Обрабатывает все точки вклада из манифеста расширения.
   *
   * @param extensionId Идентификатор расширения
   * @param manifest Манифест расширения
   * @returns Массив объектов Disposable, которые будут добавлены в subscriptions расширения
   */
  processContributions(extensionId: string, manifest: ExtensionManifest): Disposable[] {
    const subscriptions: Disposable[] = [];
    
    if (!manifest.contributes) {
      return subscriptions;
    }
    
    this.logger.info(`Обработка вкладов для расширения: ${extensionId}`);
    
    // Определяем порядок обработки точек вклада
    // Сначала обрабатываем команды, затем контейнеры представлений, затем представления, затем редакторы, затем меню, затем задачи ИИ
    const contributionOrder = [
      'commands',
      'viewContainers',
      'views',
      'editors',
      'menus',
      'aiTasks'
    ];
    
    // Обрабатываем точки вклада в определенном порядке
    for (const contributionType of contributionOrder) {
      // Используем type guard для проверки наличия свойства в объекте contributes
      if (manifest.contributes && this.hasContribution(manifest.contributes, contributionType)) {
        const handler = this.handlers.get(contributionType);
        
        if (!handler) {
          this.logger.warn(`Не зарегистрирован обработчик для типа вклада: ${contributionType}`);
          continue;
        }
        
        try {
          // Вызываем обработчик для данного типа точек вклада
          const disposables = handler.processContributions(extensionId, manifest);
          subscriptions.push(...disposables);
        } catch (error) {
          this.logger.error(`Ошибка обработки вкладов типа '${contributionType}' для расширения '${extensionId}':`, error);
        }
      }
    }
    
    // Обрабатываем остальные типы точек вклада, которые не входят в определенный порядок
    if (manifest.contributes) {
      // Безопасно приводим к типу Record<string, unknown> для итерации
      const contributes = manifest.contributes as Record<string, unknown>;
      for (const contributionType of Object.keys(contributes)) {
        if (!contributionOrder.includes(contributionType) && this.hasContribution(manifest.contributes, contributionType)) {
          const handler = this.handlers.get(contributionType);
          
          if (!handler) {
            this.logger.warn(`Не зарегистрирован обработчик для типа вклада: ${contributionType}`);
            continue;
          }
          
          try {
            // Вызываем обработчик для данного типа точек вклада
            const disposables = handler.processContributions(extensionId, manifest);
            subscriptions.push(...disposables);
          } catch (error) {
            this.logger.error(`Ошибка обработки вкладов типа '${contributionType}' для расширения '${extensionId}':`, error);
          }
        }
      }
    }
    
    return subscriptions;
  }
  
  /**
   * Type guard для проверки наличия свойства в объекте ExtensionContributions
   *
   * @param contributes Объект с точками вклада
   * @param key Ключ, который нужно проверить
   * @returns true, если свойство существует
   */
  private hasContribution(contributes: ExtensionContributions, key: string): boolean {
    return Object.prototype.hasOwnProperty.call(contributes, key) && contributes[key as keyof ExtensionContributions] !== undefined;
  }
}
