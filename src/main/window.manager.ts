import { BrowserWindow } from 'electron';
import path from 'node:path';
import { LoggingServiceAPI } from '@services/logging.service'; // Import API type
import { ConfigurationServiceAPI } from '@services/configuration.service'; // Import ConfigurationService API
import type { AppSettings } from '@shared/types/settings';
import { EventEmitter } from 'events';

// --- Constants ---
const DEFAULT_WINDOW_WIDTH = 1200;
const DEFAULT_WINDOW_HEIGHT = 800;
// ---

// События WindowManager
export enum WindowManagerEvents {
  WINDOW_CREATED = 'window-created',
  WINDOW_CLOSED = 'window-closed',
  WINDOW_READY = 'window-ready'
}

export class WindowManager extends EventEmitter {
  private mainWindow: BrowserWindow | null = null;
  private readonly configurationService: ConfigurationServiceAPI; // Use ConfigurationService
  private readonly logger: LoggingServiceAPI; // Dependency

  constructor(logger: LoggingServiceAPI, configurationService: ConfigurationServiceAPI) { // Inject ConfigurationService directly
    super(); // Инициализируем EventEmitter
    this.logger = logger.createScopedLogger('WindowManager'); // Create scoped logger
    this.configurationService = configurationService; // Assign injected service
  }

  // Removed updateSettingsService method

  /**
   * Creates and configures the main application window.
   * @returns The created BrowserWindow instance or null if creation failed.
   */
  public async createMainWindow(): Promise<BrowserWindow | null> {
    this.logger.info('Creating main window...'); // Use this.logger

    // Use configurationService.getValue (async)
    const workbenchSettings = await this.configurationService.getValue<AppSettings['workbench']>('workbench');
    const savedBounds = workbenchSettings?.windowBounds;

    const windowOptions: Electron.BrowserWindowConstructorOptions = {
      width: savedBounds?.width ?? DEFAULT_WINDOW_WIDTH,
      height: savedBounds?.height ?? DEFAULT_WINDOW_HEIGHT,
      frame: false,
      titleBarStyle: 'hidden',
      titleBarOverlay: true,
      x: savedBounds?.x,
      y: savedBounds?.y,
      webPreferences: {
        // Assuming preload script is located relative to the main process entry point after build
        preload: path.join(__dirname, 'index.js'),
        contextIsolation: true,
        nodeIntegration: false,
      },
      show: true, // Show immediately, don't wait for 'ready-to-show'
    };

    try {
      const window = new BrowserWindow(windowOptions);
      this.mainWindow = window; // Store reference

      if (process.platform !== 'darwin') {
        window.setMenu(null);
      }

      // Генерируем событие создания окна
      this.emit(WindowManagerEvents.WINDOW_CREATED, window);

      // Load the content
      try {
        if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
          this.logger.info(`Loading URL: ${MAIN_WINDOW_VITE_DEV_SERVER_URL}`); // Use this.logger
          await window.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
          this.logger.info(`URL loaded successfully: ${MAIN_WINDOW_VITE_DEV_SERVER_URL}`);
        } else {
          const indexPath = path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`);
          this.logger.info(`Loading file: ${indexPath}`); // Use this.logger
          await window.loadFile(indexPath);
          this.logger.info(`File loaded successfully: ${indexPath}`);
        }
      } catch (error) {
        this.logger.error(`Error loading content into window:`, error);
        // Если не удалось загрузить контент, все равно показываем окно
        window.show();
      }

      // Setup lifecycle handlers for this window
      await this.setupWindowLifecycleHandlers(window);

      // Добавляем логирование для отладки
      this.logger.info(`Setting up ready-to-show event handler for window (ID: ${window.id}).`);

      // Генерируем событие готовности окна сразу, так как окно уже показано
      this.logger.info(`Window is already visible (ID: ${window.id}). Emitting WINDOW_READY event.`);
      this.emit(WindowManagerEvents.WINDOW_READY, window);

      // Также подписываемся на событие ready-to-show для логирования
      window.once('ready-to-show', () => {
        this.logger.info(`Window ready-to-show event fired (ID: ${window.id}). Window is already visible.`);
      });

      this.logger.info(`Main window created (ID: ${window.id}).`); // Use this.logger
      return window;

    } catch (error) {
      this.logger.error('Failed to create main window', error); // Use this.logger
      this.mainWindow = null;
      return null;
    }
  }

  /**
   * Returns the current main browser window instance if it exists and is not destroyed.
   */
  public getCurrentWindow(): BrowserWindow | null {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      return this.mainWindow;
    }
    this.logger.warn('Attempted to get a destroyed or non-existent main window.'); // Use this.logger

    return null;
  }

  /**
   * Sets up lifecycle handlers for a given window (bounds saving, closed event).
   */
  private async setupWindowLifecycleHandlers(window: BrowserWindow) {
    let saveBoundsTimeout: NodeJS.Timeout | null = null;
    const scheduleSaveBounds = () => {
      if (!window || window.isDestroyed()) return;
      if (saveBoundsTimeout) {
        clearTimeout(saveBoundsTimeout);
      }
      saveBoundsTimeout = setTimeout(async () => {
        if (!window.isDestroyed()) {
          await this.saveWindowBounds(window);
        }
        saveBoundsTimeout = null;
      }, 500);
    };

    window.on('resize', scheduleSaveBounds);
    window.on('move', scheduleSaveBounds);
    window.on('close', async () => {
      if (!window || window.isDestroyed()) return;
      if (saveBoundsTimeout) {
        clearTimeout(saveBoundsTimeout);
      }
      await this.saveWindowBounds(window); // Save immediately on close attempt
    });

    window.on('closed', () => {
      this.logger.info(`Window closed (ID: ${window.id}).`); // Use this.logger
      // Clear the reference if this was the main window
      if (this.mainWindow === window) {
        this.mainWindow = null;
      }
      // Note: We don't automatically quit here, MainApplication handles that.
    });
  }

  /**
   * Saves the bounds of the given window to settings using ConfigurationService.
   */
  private async saveWindowBounds(window: BrowserWindow) {
    // No need to check if window is null here, called from handlers
    if (window.isDestroyed()) {
      this.logger.warn('Attempted to save bounds for a destroyed window.'); // Use this.logger
      return;
    }
    try {
      const bounds = window.getBounds();
      this.logger.info('Saving window bounds:', bounds); // Use this.logger
      // Use configurationService.getValue and updateValue (async)
      const currentWorkbenchSettings = await this.configurationService.getValue<AppSettings['workbench']>('workbench');
      await this.configurationService.updateValue('workbench', {
        ...(currentWorkbenchSettings ?? {}),
        windowBounds: {
          x: bounds.x,
          y: bounds.y,
          width: bounds.width,
          height: bounds.height
        }
      });
    } catch (error) {
      // Check specifically for the destroyed object error
      if (error instanceof Error && error.message?.includes('Object has been destroyed')) {
        this.logger.warn('Error saving bounds because window was destroyed during async operation.'); // Use this.logger
      } else {
        this.logger.error('Error saving window bounds:', error); // Use this.logger
      }
    }
  }

  // Optional: Add methods for managing multiple windows if needed later
  // public getWindowById(id: number): BrowserWindow | null { ... }
  // public getAllWindows(): BrowserWindow[] { ... }
}
