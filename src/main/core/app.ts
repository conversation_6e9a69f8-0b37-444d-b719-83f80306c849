import { app, BrowserWindow, Event } from 'electron';
import { logger } from './services/logging.service'; // Keep logger singleton for now, refactor later if needed
import { ServiceManager } from './service.manager';
import { WindowManager } from './window.manager';
import { ExtensionManager } from './extension.manager';
import { registerCoreCommands } from './defaults/commands'; // Import function
// import { registerIpcHandlers } from '../ipc/handlers'; // No longer needed here? Handlers registered via extensions/core
// import { ipcMainService } from './ipcMainService'; // No longer needed, use serviceManager.ipcMain


export class MainApplication {
    private serviceManager: ServiceManager| null = null;
    private windowManager: WindowManager | null = null;
    private extensionManager: ExtensionManager| null = null;

    constructor() {
        logger.info('MainApplication constructor called.');
        this.registerAppListeners();
    }

    /**
     * Initializes the application, services, window, and extensions.
     * Should be called once the app is ready.
     */
    async initialize(): Promise<void> {
        logger.info('MainApplication initializing...');

        // init all built-in services
        this.serviceManager = new ServiceManager();
        // Pass logger and settings service to WindowManager constructor
        this.windowManager = new WindowManager(this.serviceManager.logger, this.serviceManager.settings);

        // Instantiate ExtensionManager - needs registry and sender getter
        this.extensionManager = new ExtensionManager(this.serviceManager, () => this.getSendRendererEvent());

        // --- Phase 2: Bootstrap Core and Discover Extensions ---
        await this._bootstrapCoreAndDiscoverExtensions();

        // --- Phase 3: Create Main Window and Initialize Window-Dependent Services ---
        const mainWindow = await this._createAndInitializeMainWindow();

        // --- Phase 4: Activate Extensions ---
        if (mainWindow) {
            await this._activateExtensions();
        } else {
            logger.error("Skipping extension activation as main window creation failed.");
            app.quit(); // Exit if window creation failed
        }

        logger.info('MainApplication initialization complete.');
    }


    /**
     * Registers listeners for Electron app events.
     */
    private registerAppListeners(): void {
        // Use initialize method on 'ready'
        app.on('ready', () => this.initialize().catch(err => {
            logger.error("Fatal error during MainApplication initialization:", err);
            app.quit();
        }));
        app.on('activate', () => this.onActivate());
        app.on('window-all-closed', () => this.onWindowAllClosed());
        // Use a wrapper for async onWillQuit handler
        app.on('will-quit', (event) => {
            event.preventDefault(); // Prevent immediate exit
            this.onWillQuit().catch(err => {
                logger.error("Error during onWillQuit handling:", err);
            }).finally(() => {
                // Ensure app exits even if onWillQuit fails internally,
                // but only after the async operation completes or errors.
                // The actual app.exit() is now inside onWillQuit's finally block.
            });
        });
    }

    /**
     * Initializes core settings, registers global handlers, and discovers extensions.
     * Relies on services being instantiated in initialize().
     */
    private async _bootstrapCoreAndDiscoverExtensions(): Promise<void> {
        if (!this.serviceManager || !this.extensionManager) {
             logger.error("Cannot bootstrap core: serviceManager or extensionManager not initialized.");
             throw new Error("Core services not initialized before bootstrap.");
        }
        logger.info('Bootstrapping core infrastructure and discovering extensions...');

        // Initialize core settings by calling the method on the settings service instance
        if (this.serviceManager?.settings) { // Use serviceManager
             this.serviceManager.settings.registerCoreSettings(); // Call method on the service
        } else {
            logger.error("SettingsService not available in serviceManager for core settings registration.");
        }

        // Register Global Handlers (Commands & IPC)
        this._registerGlobalHandlers(); // Uses internal services

        // Discover extensions using the service
        await this._discoverExtensions();
        logger.info('Core bootstrap and extension discovery complete.');
    }

     /**
     * Creates the main window, initializes window-dependent services, and sets up the window UI/Menu.
     * Relies on services being instantiated in initialize().
     * @returns The created BrowserWindow instance or null if failed.
     */
    private async _createAndInitializeMainWindow(): Promise<BrowserWindow | null> {
        if (!this.windowManager || !this.serviceManager) {
            logger.error("Cannot create main window: WindowManager or serviceManager not initialized.");
            return null;
        }
        logger.info('Creating and initializing main window...');

        const window = this.windowManager.createMainWindow();
        if (window) {
            // Initialize window-dependent services via the registry
            this.serviceManager.initializeServices(window, () => this.getSendRendererEvent());

            // Setup window-specific UI and Menu
            await this._setupWindowUIAndMenu(window);
            logger.info('Main window created and initialized.');
        } else {
            logger.error("Main window could not be created.");
        }
        return window;
    }

    /**
     * Discovers extensions defined in the application using extensionManager.
     */
    private async _discoverExtensions(): Promise<void> {
        logger.info('Discovering extensions...');
        if (!this.extensionManager) {
            logger.error('extensionManager not initialized before discovering extensions.');
            return;
        }
        try {
            await this.extensionManager.discoverExtensions();
        } catch (err) {
            logger.error('Failed during extension discovery:', err);
        }
        logger.info('Extension discovery complete.');
    }

    /**
     * Activates all discovered extensions using extensionManager.
     */
    private async _activateExtensions(): Promise<void> {
        logger.info('Activating extensions...');
         if (!this.extensionManager) {
            logger.error('extensionManager not initialized before activating extensions.');
            return;
        }
        try {
            await this.extensionManager.activateAll();
        } catch (err) {
            logger.error('Failed during extension activation:', err);
            // Decide if activation error is critical
        }
        logger.info('Extension activation complete.');
    }


    /**
     * Called on macOS when the dock icon is clicked and there are no other windows open.
     */
    private async onActivate(): Promise<void> {
        // Ensure initialization has happened
        if (!this.windowManager) {
            logger.warn("onActivate called before initialization is complete.");
            return;
        }
        if (!this.windowManager.getCurrentWindow()) {
            logger.info('App activated, creating new window.');
            // Re-run the window creation and initialization part
            // This assumes _bootstrapCoreAndDiscoverExtensions doesn't need re-running
            await this._createAndInitializeMainWindow();
            // Extensions are typically not reactivated on macOS activate unless explicitly designed to
        } else {
             this.windowManager.getCurrentWindow()?.focus();
        }
    }

    /**
     * Registers global command and IPC handlers. Called once during initialization.
     * Assumes services are available via this.serviceManager.
     */
    private _registerGlobalHandlers(): void {
        if (!this.serviceManager || !this.windowManager) {
             logger.error("Cannot register global handlers: serviceManager or WindowManager not initialized.");
             return;
        }
        logger.info('Registering global core commands...'); // Updated log message

        // Get required services from the registry
        const commandSvc = this.serviceManager.commands;
        const notificationSvc = this.serviceManager.notifications;
        const editorSvc = this.serviceManager.editors;
        const contextSvc = this.serviceManager.context; // Get context service
        // Ensure services exist before proceeding
        if (!commandSvc || !notificationSvc || !editorSvc || !contextSvc) { // Add contextSvc check
            logger.error("Cannot register global handlers: One or more required services (commands, notifications, editors, context) not found in registry.");
            return;
        }

        // Pass only necessary services/functions to registration functions
        registerCoreCommands(
            commandSvc,
            notificationSvc,
            editorSvc,
            contextSvc, // Pass context service
            () => this.getCurrentWindow(), // Pass getter function for window
            () => this.getSendRendererEvent() // Pass getter function for sender
        );

        // IPC Handlers should be registered by the services/extensions themselves
        // using serviceManager.ipcMain.handle(...)
        logger.info('Global core commands registered.'); // Updated log message
    }

    /**
     * Sets up the UI components and menu for a given window.
     * @param window The BrowserWindow instance to set up.
     */
    private async _setupWindowUIAndMenu(window: BrowserWindow): Promise<void> {
        if (!this.serviceManager) {
            logger.error("Cannot setup window UI: serviceManager not initialized.");
            return;
        }
        logger.info(`Setting up UI environment for window ID: ${window.id}`);

        // Get necessary APIs (menus) from the registry
        const menuSvc = this.serviceManager.menus;

          // Build native menu for this window
        if (process.platform === 'darwin' && menuSvc) {
            // Assuming getCoreServicesAPI is still useful on registry, or get menuSvc directly
            await menuSvc.buildAndSetNativeMainMenu();
        }
        logger.info(`UI Environment setup complete for window ID: ${window.id}`);
    }


    /**
     * Called when all windows are closed. Quit the app, except on macOS.
     */
    private onWindowAllClosed(): void {
        if (process.platform !== 'darwin') {
            logger.info('All windows closed, quitting app.');
            app.quit();
        }
    }

    /**
     * Called before the application starts closing its windows.
     * Handles saving dirty editors, deactivation of extensions, and disposal of services.
     */
    private async onWillQuit(): Promise<void> {
        logger.info('Application is about to quit. Saving dirty editors...');

        let allSavesCompleted = false;
        const saveTimeout = 5000; // 5 seconds timeout for saves
        const confirmationChannel = 'core:workbench.allSaveRequestsCompleted';
        let saveConfirmationHandler: NodeJS.Timeout | null = null;

        // Setup confirmation handler using ipcMain service from ServiceManager
        const saveConfirmationPromise = new Promise<void>((resolve) => {
            const handler = () => {
                logger.info('Received confirmation: all save requests completed by renderer.');
                allSavesCompleted = true;
                if (saveConfirmationHandler) clearTimeout(saveConfirmationHandler);
                this.serviceManager?.ipcMain.removeHandler(confirmationChannel); // Clean up handler
                resolve();
            };
            // Ensure ipcMain is available before handling
            if (this.serviceManager?.ipcMain) {
                this.serviceManager.ipcMain.handle(confirmationChannel, handler);
            } else {
                 logger.error("ipcMain service not available to handle save confirmation.");
                 resolve(); // Resolve immediately if IPC cannot be handled
            }


            // Timeout for the confirmation
            saveConfirmationHandler = setTimeout(() => {
                if (!allSavesCompleted) {
                    logger.warn(`Save confirmation timed out after ${saveTimeout}ms.`);
                    this.serviceManager?.ipcMain.removeHandler(confirmationChannel); // Clean up handler on timeout
                    resolve(); // Resolve anyway to proceed with shutdown
                }
            }, saveTimeout);
        });


        try {
            // Get dirty editors and send requests
            const editorSvc = this.serviceManager?.editors;
            const sendEvent = this.getSendRendererEvent();
            // Define structure for dirty editor info (adjust as needed based on EditorService implementation)
            let dirtyEditors: { editorId: string; sceneId: string; /* uri?: string; other needed info */ }[] = [];

            if (editorSvc && typeof editorSvc.getDirtyEditors === 'function') {
                dirtyEditors = editorSvc.getDirtyEditors(); // Assuming this method exists and returns the needed info
                if (dirtyEditors.length > 0) {
                    logger.info(`Found ${dirtyEditors.length} dirty editors. Sending save requests...`);
                    dirtyEditors.forEach(editor => {
                        // Send necessary info for renderer to identify and save the editor
                        sendEvent('core:editor.requestSave', { editorId: editor.editorId, sceneId: editor.sceneId });
                    });
                } else {
                    logger.info('No dirty editors found.');
                    allSavesCompleted = true; // No saves needed
                }
            } else {
                logger.warn('EditorService or getDirtyEditors method not available. Skipping save attempt.');
                allSavesCompleted = true; // Cannot attempt save
            }

            // Wait for confirmation (or timeout) only if saves were requested
            if (!allSavesCompleted) {
                 logger.info('Waiting for save confirmation from renderer...');
                 await saveConfirmationPromise;
            } else {
                 // If no saves were needed or possible, ensure handler is removed if it was set up
                 if (this.serviceManager?.ipcMain) {
                    this.serviceManager.ipcMain.removeHandler(confirmationChannel);
                 }
                 if (saveConfirmationHandler) clearTimeout(saveConfirmationHandler); // Clear timeout too
            }


            // Proceed with shutdown
            logger.info('Proceeding with extension deactivation and service disposal...');
            // Deactivate extensions first
            if (this.extensionManager) {
                await this.extensionManager.dispose();
            }
            // Dispose services via the registry
            if (this.serviceManager) {
                await this.serviceManager.dispose(); // Await the async dispose
            }
            logger.info('Extensions and services handled successfully.');

        } catch (err) {
            logger.error('Error during shutdown sequence:', err);
        } finally {
            logger.info('Exiting application.');
            // Ensure handler is removed in all exit paths
            this.serviceManager?.ipcMain.removeHandler(confirmationChannel);
            if (saveConfirmationHandler) clearTimeout(saveConfirmationHandler);
            app.exit(); // Exit after cleanup attempt
        }
    }

    /**
     * Registers core UI components like editors and views.
     * Moved here to be callable after service initialization.
     */
    private registerCoreUIComponents(): void {
        if (!this.serviceManager) {
            logger.error("Cannot register core UI components: serviceManager not initialized.");
            return;
        }
        const editorSvc = this.serviceManager.editors;
        const viewSvc = this.serviceManager.views;

        if (!editorSvc || !viewSvc) {
             logger.error("EditorService or ViewService not available in registry.");
             return;
        }

        // Register core editors/views (with checks)
        if (!editorSvc.getEditorProvider('workbench:settings-editor')) {
            editorSvc.registerEditorProvider({
                editorType: 'workbench:settings-editor',
                extensionId: 'ai-books.workbench',
                componentName: 'SettingsEditor',
                title: 'Settings',
                icon: 'Settings'
            });
        }
        // ViewService handles overwrites, no check needed
        viewSvc.registerView({
            id: 'workbench.views.notifications',
            name: 'Notifications',
            componentName: 'NotificationsView',
            extensionId: 'ai-books.workbench',
            location: 'panel',
            icon: 'Bell',
        });
         // TODO: Register other core components if needed
    }


    /**
     * Returns a function to send events to the *current* main window's renderer process.
     * This ensures we don't send to a destroyed window.
     */
    public getSendRendererEvent(): (channel: string, payload: any) => void { // Updated signature
        // Capture 'this' correctly
        return (channel: string, payload: any) => { // Expect single payload object now
            const currentWindow = this.getCurrentWindow(); // Use internal method
            if (currentWindow && currentWindow.webContents && !currentWindow.webContents.isDestroyed()) {
                // Use ipcMain service from ServiceManager to send the event
                this.serviceManager?.ipcMain.send(currentWindow.webContents, channel, payload);
            } else {
                // Use logger singleton or console as fallback before registry is ready
                const log = this.serviceManager?.logger ?? logger;
                log.warn(`Attempted to send event '${channel}' but main window is not available.`);
            }
        };
    }

    /**
     * Returns the current main browser window instance via WindowManager.
     */
    public getCurrentWindow(): BrowserWindow | null {
        // Delegate to internal WindowManager instance
        return this.windowManager?.getCurrentWindow() ?? null;
    }
}
