import { AppSettings } from "@shared/types/settings";
import path from 'node:path'; // Import path for default projectsRoot
import { app } from 'electron'; // Import app to get default user data path
import { Schema } from "electron-store";

export const defaultProjectsRoot = path.join(app.getPath('userData'), 'projects');

export const defaults: AppSettings = {
  theme: 'system',
  editorFontSize: 16,
  editorLineHeight: 1.6,
  autosaveInterval: 5000,
  ai: { 
    apiKey: '',
    model: 'gpt-4o-mini',
    provider: 'openai' // Default to 'none' to avoid unnecessary API calls
  },
  workbench: {
    tabs: { openTabs: [], activeTabId: null },
    layout: {
      isSidebarVisible: true,
      isPanelVisible: true,
      sidebarWidth: 250,
      panelHeight: 300
    },
    projectsRoot: defaultProjectsRoot,
    windowBounds: {
        x: 0,
        y: 0,
        width: 800,
        height: 600
    }
  }
};

export const schema: Schema<AppSettings> = {
    theme: { type: 'string', enum: ['light', 'dark', 'system'], default: defaults.theme },
    editorFontSize: { type: 'number', minimum: 10, maximum: 30, default: defaults.editorFontSize },
    editorLineHeight: { type: 'number', minimum: 1.0, maximum: 3.0, default: defaults.editorLineHeight },
    autosaveInterval: { type: 'number', minimum: 1000, default: defaults.autosaveInterval },
    ai: {
        type: 'object',
        properties: {
            provider: { type: 'string', enum: ['openai', 'anthropic', 'none'], default: 'none' },
            apiKey: { type: 'string', default: '' },
            model: { type: 'string', default: '' },
        },
    },
    workbench: {
        type: 'object',
        properties: {
            tabs: {
                type: 'object',
                properties: {
                    openTabs: { type: 'array', items: { type: 'object' }, default: [] },
                    activeTabId: { type: ['string', 'null'], default: null }
                },
                default: { openTabs: [], activeTabId: null },
            },
            layout: {
                type: 'object',
                properties: {
                    isSidebarVisible: { type: 'boolean', default: true },
                    isPanelVisible: { type: 'boolean', default: true },
                    sidebarWidth: { type: 'number', minimum: 100, maximum: 500, default: 250 },
                    panelHeight: { type: 'number', minimum: 100, maximum: 500, default: 300 }
                },
                default: { isSidebarVisible: true, isPanelVisible: true, sidebarWidth: 250, panelHeight: 300 }
            },
            windowBounds: {
                type: 'object',
                properties: {
                    x: { type: 'number' }, y: { type: 'number' },
                    width: { type: 'number' }, height: { type: 'number' }
                },
                // No default for windowBounds, let the system handle it
            },
            projectsRoot: { // Add schema definition
                type: 'string',
                default: defaultProjectsRoot
            }
        },
        default: { // Update default for workbench
             tabs: { openTabs: [], activeTabId: null },
             projectsRoot: defaultProjectsRoot
        },
    }
};
