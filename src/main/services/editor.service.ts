import {LoggingServiceAPI} from './logging.service';
import {BaseService, IBaseService} from "@services/base.service";
import {IpcChannels} from '@shared/constants/ipc-channels';
import {IpcServiceAPI} from './ipc.service';
import {EditorGetProviderDetailsArgs, EditorGetProviderDetailsResult} from '@shared/types/ipc';
import {BrowserWindow} from "electron";
import {Disposable} from "@shared/types/common";

/**
 * Describes an editor provider contributed by an extension.
 */
export interface EditorProvider {
  /** Unique identifier for the editor type (e.g., 'ai-books.books:scene-editor') */
  editorType: string;
  /** ID of the extension providing the editor (e.g., 'ai-books.books') */
  extensionId: string; // Добавляем ID расширения
  /** Name of the React component (must match filename {ComponentName}.editor.tsx) */
  componentName: string;
  /** Optional title pattern for the tab (e.g., "Scene: {title}") */
  title?: string;
  /** Optional icon identifier for the tab */
  icon?: string;
  /** Optional context when this editor type is applicable */
  when?: string;
}

/**
 * API for the EditorService provided to extensions via ExtensionContext.
 */
export interface EditorServiceAPI extends IBaseService {
  /**
   * Registers a provider for a custom editor type.
   * @param provider The editor provider description.
   * @returns An Disposable to unregister the provider.
   */
  registerEditorProvider(provider: EditorProvider): Disposable;
  
  /**
   * Retrieves the registered editor provider for a given type.
   * @param editorType The unique identifier of the editor type.
   * @returns The EditorProvider or undefined if not found.
   */
  getEditorProvider(editorType: string): EditorProvider | undefined;
}

// Add Disposable implementation
export class EditorService extends BaseService implements EditorServiceAPI {
  private providers = new Map<string, EditorProvider>();
  private providerDisposables = new Map<string, Disposable>();
  private readonly ipcService: IpcServiceAPI;
  
  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, 'EditorService');
    this.ipcService = ipcService;
  }
  
  public async initialize(windowGetter: () => (BrowserWindow | null)) {
    await super.initialize(windowGetter);
    this.registerDefaultEditors();
    this.registerIpcHandlers();
    this.isInitialized = true;
  }
  
  registerEditorProvider(provider: EditorProvider): Disposable {
    if (!provider || !provider.editorType) {
      this.logger.error('Attempted to register editor provider with invalid editorType.', provider); // Use this.logger
      throw new Error('Editor provider must have a valid editorType string.');
    }
    if (!provider.extensionId) { // Проверка extensionId
      this.logger.error(`Editor provider for type '${provider.editorType}' is missing extensionId.`); // Use this.logger
      throw new Error(`Editor provider '${provider.editorType}' must have an extensionId.`);
    }
    if (this.providers.has(provider.editorType)) {
      this.logger.warn(`Editor provider for type '${provider.editorType}' is already registered. Overwriting.`); // Use this.logger
      // Dispose previous registration if exists
      this.providerDisposables.get(provider.editorType)?.dispose();
    }
    if (!provider.componentName) {
      this.logger.error(`Editor provider for type '${provider.editorType}' is missing componentName.`); // Use this.logger
      throw new Error(`Editor provider '${provider.editorType}' must have a componentName.`);
    }
    
    this.logger.info(`Registering editor provider for type '${provider.editorType}' with component '${provider.componentName}'.`); // Use this.logger
    this.providers.set(provider.editorType, provider);
    
    const disposable = {
      dispose: async () => {
        this.logger.info(`Disposing editor provider registration for type '${provider.editorType}'.`); // Use this.logger
        this.providers.delete(provider.editorType);
        this.providerDisposables.delete(provider.editorType);
      }
    };
    this.providerDisposables.set(provider.editorType, disposable);
    return disposable;
  }
  
  /**
   * Retrieves the registered editor provider for a given type.
   * (Internal method, not part of the API for extensions)
   * @param editorType The unique identifier of the editor type.
   * @returns The EditorProvider or undefined if not found.
   */
  getEditorProvider(editorType: string): EditorProvider | undefined {
    return this.providers.get(editorType);
  }
  
  // Add dispose method
  async dispose() {
    this.logger.info('Disposing all editor providers...');
    await super.dispose();
    this.providerDisposables.forEach(d => d.dispose());
    this.providers.clear();
    this.providerDisposables.clear();
  }
  
  private registerDefaultEditors() {
    this.registerEditorProvider({
      editorType: 'workbench:settings-editor',
      extensionId: 'workbench',
      componentName: 'SettingsEditor',
      title: 'Settings',
      icon: 'Settings'
    });
  }
  
  private registerIpcHandlers(): void {
    this.logger.info('Registering EditorService IPC handlers...');
    
    this.ipcService.handle<EditorGetProviderDetailsArgs, EditorGetProviderDetailsResult>(
      IpcChannels.EDITOR_GET_PROVIDER_DETAILS,
      (args) => {
        const {editorType} = args;
        if (!editorType) throw new Error('Invalid editor type.');
        const provider = this.getEditorProvider(editorType);
        return provider ?? null; // Return provider or null
      }
    );
    
    this.logger.info('EditorService IPC handlers registered.');
  }
  
  // TODO: Add method to get all providers (maybe filtered by context 'when'?) if needed.
}
