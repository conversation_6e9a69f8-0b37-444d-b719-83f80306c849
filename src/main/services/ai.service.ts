import type {Dictionary, Disposable} from '@shared/types/common';
import type {LoggingServiceAPI} from './logging.service';
import type {ConfigurationServiceAPI} from './configuration.service';
import type {IpcServiceAPI} from './ipc.service';
import type {IpcErrorData} from '@shared/types/ipc';
import type {AICompletionResponse, AIProviderClient, AIProviderConfig, AITaskDefinition} from '@shared/types/ai';
import {OpenAIClient} from '../ai/providers/openai.client';
import {BaseService, IBaseService} from "@services/base.service";
import Ajv from 'ajv';
import {BrowserWindow} from "electron";

export interface AIServiceAPI extends IBaseService {
  registerTask(definition: AITaskDefinition): Disposable;
  
  runTask(taskId: string, context: Dictionary<unknown>): Promise<AICompletionResponse | IpcErrorData>;
  
  runStreamingTask(taskId: string, context: Dictionary<unknown>): AsyncGenerator<string | IpcErrorData, void, unknown>;
}

export class AIService extends BaseService implements AIServiceAPI {
  private readonly configuration: ConfigurationServiceAPI;
  private readonly taskRegistry = new Map<string, AITaskDefinition>();
  private readonly ajv = new Ajv();
  private readonly ipc: IpcServiceAPI;
  private providerClient: AIProviderClient | null = null; // Initialize later

  constructor(logger: LoggingServiceAPI, ipc: IpcServiceAPI, configuration: ConfigurationServiceAPI) {
    super(logger, "AIService");
    this.configuration = configuration;
    this.ipc = ipc;
  }
  
  public async initialize(windowGetter: () => (BrowserWindow | null)): Promise<void> {
    await super.initialize(windowGetter);
    await this.initializeProvider();
    this.isInitialized = true;
  }
  
  public registerTask(definition: AITaskDefinition): Disposable {
    if (this.taskRegistry.has(definition.taskId)) {
      this.logger.warn(`AI Task already registered, overwriting: ${definition.taskId}`);
    }
    this.taskRegistry.set(definition.taskId, definition);
    this.logger.info(`Registered AI Task: ${definition.taskId}`);
    return {
      dispose: async () => {
        this.taskRegistry.delete(definition.taskId);
        this.logger.info(`Unregistered AI Task: ${definition.taskId}`);
      }
    };
  }
  
  public async runTask(taskId: string, context: Dictionary<unknown>): Promise<AICompletionResponse | IpcErrorData> {
    this.logger.info(`Running AI Task: ${taskId}`, {contextKeys: Object.keys(context)});
    
    // Re-check provider initialization in case settings changed or failed initially
    if (!this.providerClient) {
      this.initializeProvider();
      if (!this.providerClient) {
        return {code: 'ai_config_error', message: 'AI provider is not configured or API key is missing.'};
      }
    }
    
    const taskDefinition = this.taskRegistry.get(taskId);
    if (!taskDefinition) {
      return {code: 'ai_task_not_found', message: `AI Task '${taskId}' not found.`}; // Implicitly IpcErrorData
    }
    
    try {
      // 1. Gather Full Context - Responsibility moved to the caller (IPC Handler)
      const fullContext = context;
      
      // Basic validation if requiredContext is defined
      if (taskDefinition.requiredContext) {
        for (const key of taskDefinition.requiredContext) {
          if (!(key in fullContext)) {
            throw new Error(`Missing required context key for task ${taskId}: ${key}`);
          }
        }
      }
      
      // 2. Format Prompt
      let prompt = taskDefinition.promptTemplate;
      for (const key in fullContext) {
        const placeholder = `{{${key}}}`;
        while (prompt.includes(placeholder)) {
          prompt = prompt.replace(placeholder, String(fullContext[key]));
        }
      }
      this.logger.info(`Formatted Prompt for ${taskId}: ${prompt.substring(0, 200)}...`);
      
      
      // 3. Call AI Provider
      // TODO: Read model/options from settings ('ai.model', 'ai.temperature' etc.)
      const aiOptions: AIProviderConfig = {
        model: await this.configuration.getValue<string>('ai.model') ?? undefined, // Example
        // temperature: await this.configuration.getValue<number>('ai.temperature') ?? undefined
      };
      const result = await this.providerClient.generateStructuredOutput(
        prompt,
        taskDefinition.outputSchema,
        aiOptions
      );
      
      // 4. Validate Response
      const validate = this.ajv.compile(taskDefinition.outputSchema);
      if (!validate(result)) {
        const errorDetails = this.ajv.errorsText(validate.errors);
        this.logger.error(`AI response schema validation failed for task ${taskId}`, {
          errors: errorDetails,
          response: result
        });
        throw new Error(`AI response schema validation failed: ${errorDetails}`);
      }
      
      this.logger.info(`AI Task ${taskId} completed successfully.`);
      return result;
      
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error(`AI Task ${taskId} failed`, {error: message});
      return {code: 'ai_execution_error', message: `AI Task failed: ${message}`}; // Implicitly IpcErrorData
    }
  }
  
  // Implement streaming task execution
  public async* runStreamingTask(taskId: string, context: Dictionary<unknown>): AsyncGenerator<string | IpcErrorData, void, unknown> {
    this.logger.info(`Running Streaming AI Task: ${taskId}`, {contextKeys: Object.keys(context)});
    
    if (!this.providerClient || typeof this.providerClient.streamOutput !== 'function') {
      this.initializeProvider(); // Attempt re-initialization
      if (!this.providerClient || typeof this.providerClient.streamOutput !== 'function') {
        yield {code: 'ai_config_error', message: 'AI provider is not configured or does not support streaming.'};
        return;
      }
    }
    
    const taskDefinition = this.taskRegistry.get(taskId);
    if (!taskDefinition) {
      yield {code: 'ai_task_not_found', message: `AI Task '${taskId}' not found.`};
      return;
    }
    
    try {
      // 1. Gather Full Context (Responsibility of the caller for MVP)
      const fullContext = context;
      if (taskDefinition.requiredContext) {
        for (const key of taskDefinition.requiredContext) {
          if (!(key in fullContext)) {
            throw new Error(`Missing required context key for task ${taskId}: ${key}`);
          }
        }
      }
      
      // 2. Format Prompt
      let prompt = taskDefinition.promptTemplate;
      for (const key in fullContext) {
        const placeholder = `{{${key}}}`;
        while (prompt.includes(placeholder)) {
          prompt = prompt.replace(placeholder, String(fullContext[key]));
        }
      }
      this.logger.info(`Formatted Streaming Prompt for ${taskId}: ${prompt.substring(0, 200)}...`);
      
      // 3. Call AI Provider Stream
      // TODO: Read model/options from settings ('ai.model', 'ai.temperature' etc.)
      const aiOptions: AIProviderConfig = {
        model: await this.configuration.getValue<string>('ai.model') ?? undefined, // Example
        // temperature: await this.configuration.getValue<number>('ai.temperature') ?? undefined
      };
      const stream = this.providerClient.streamOutput(
        prompt,
        aiOptions
      );
      
      // 4. Yield tokens or error
      for await (const chunk of stream) {
        yield chunk; // Forward the string token or IpcErrorData
      }
      
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error(`AI Streaming Task ${taskId} failed`, {error: message});
      yield {code: 'ai_streaming_error', message: `AI Streaming Task failed: ${message}`};
    }
  }
  
  public async dispose() {
    await super.dispose();
    this.taskRegistry.clear();
    this.providerClient = null;
  }
  
  private async initializeProvider() {
    const apiKey = await this.configuration.getValue<string>('ai.apiKey') ?? '';
    const providerType = await this.configuration.getValue<string>('ai.provider') ?? 'openai';
    
    if (!apiKey || apiKey === "" || !providerType || providerType === "") {
      this.logger.warn('AI Provider  not configured (Setting: ai.apiKey, ai.provider).');
      this.providerClient = null;
      return;
    } else {
      this.logger.info('AI Provider API key is configured.');
    }
    
    
    if (providerType === 'openai') {
      this.providerClient = new OpenAIClient(apiKey, this.logger);
    } else {
      this.logger.warn(`Unsupported AI provider type: ${providerType}`);
      this.providerClient = null; // Set to null if unsupported
    }
    
    // TODO: Listen for settings changes ('ai.provider', 'ai.apiKey') and re-initialize
    // This requires ConfigurationService to emit specific change events or a general one
    // Example:
    // this.configuration.onDidChangeConfiguration(({key}) => {
    //   if (key === 'ai.provider' || key === 'ai.apiKey') {
    //     this.initializeProvider();
    //   }
    // });
  }
}
