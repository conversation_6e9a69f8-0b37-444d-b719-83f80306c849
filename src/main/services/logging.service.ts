import {BrowserWindow, WebContents} from 'electron';
import type {IpcEventArgs} from '@shared/types/ipc';
import {IpcChannels} from '@shared/constants/ipc-channels';
import {IBaseService} from "@services/base.service";
import {LogEntry, LogLevel} from '@shared/types/logging';

export interface LoggingServiceAPI extends IBaseService {
  createScopedLogger(contextName: string): LoggingServiceAPI
  
  info(message: string, ...args: unknown[]): void; // Use variadic args
  warn(message: string, ...args: unknown[]): void; // Use variadic args
  error(message: string, ...args: unknown[]): void; // Use variadic args, error should be passed as one of the args
  debug(message: string, ...args: unknown[]): void; // Use variadic args
}

// Type guard for error-like objects
function isErrorLike(value: unknown): value is { message: string; stack?: string; name?: string } {
  return typeof value === 'object' && value !== null && 'message' in value;
}

class LoggingService implements LoggingServiceAPI {
  private logQueue: LogEntry[] = []; // Очередь на случай, если окно еще не готово
  private getCurrentWindow?: () => (BrowserWindow | null); // Store window getter
  
  // Метод для инициализации с функцией получения окна
  public async initialize() {
    this.flushLogQueue();
  }
  
  // Add optional source parameter
  log(level: LogLevel, message: string, source?: string, ...args: unknown[]): void {
    const timestamp = this.getTimestamp();
    // Extract error if it's the last argument and an Error instance or error-like
    let errorInfo: { name?: string; message: string; stack?: string } | undefined;
    let details = args;
    const lastArg = args.length > 0 ? args[args.length - 1] : undefined;
    
    if (lastArg instanceof Error) {
      errorInfo = {name: lastArg.name, message: lastArg.message, stack: lastArg.stack};
      details = args.slice(0, -1); // Remove error from details
    } else if (isErrorLike(lastArg)) { // Use type guard
      // Now TypeScript knows lastArg has message, stack?, name?
      errorInfo = {name: lastArg.name, message: lastArg.message, stack: lastArg.stack};
      details = args.slice(0, -1);
    }
    
    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      source: source, // Use the source parameter
      details: details.length > 0 ? details : undefined // Store remaining args as details
    };
    
    // Вывод в консоль Main процесса
    let consoleMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    // Append details to console message
    if (details.length > 0) {
      consoleMessage += details.map(arg => ` ${typeof arg === 'object' ? JSON.stringify(arg) : String(arg)}`).join('');
    }
    if (errorInfo) consoleMessage += `\nError: ${errorInfo.message}${errorInfo.stack ? `\nStack: ${errorInfo.stack}` : ''}`;
    
    switch (level) {
      case 'debug':
        if (process.env.NODE_ENV === 'development') console.debug(consoleMessage);
        break;
      case 'info':
        console.info(consoleMessage);
        break;
      case 'warn':
        console.warn(consoleMessage);
        break;
      case 'error':
        console.error(consoleMessage);
        break;
    }
    
    // Отправка в Renderer
    this.sendToRenderer(entry);
  }
  
  // Public API methods call log without source (source is undefined for non-scoped logs)
  info(message: string, ...args: unknown[]): void {
    this.log('info', message, undefined, ...args);
  }
  
  warn(message: string, ...args: unknown[]): void {
    this.log('warn', message, undefined, ...args);
  }
  
  error(message: string, ...args: unknown[]): void {
    this.log('error', message, undefined, ...args);
  }
  
  debug(message: string, ...args: unknown[]): void {
    if (process.env.NODE_ENV === 'development') {
      this.log('debug', message, undefined, ...args);
    }
  }
  
  // Основные методы логирования
  
  // Добавляем пустой метод dispose
  public async dispose() {
    this.info('LoggingService disposed.'); // Можно добавить лог о вызове
    this.getCurrentWindow = undefined; // Clear window getter reference
    this.logQueue = []; // Очищаем очередь
  }
  
  /**
   * Creates a scoped logger instance that automatically prefixes messages.
   * @param contextName The name of the context (e.g., service name).
   * @returns A LoggingServiceAPI instance that logs with the context prefix.
   */
  public createScopedLogger(contextName: string): LoggingService {
    const prefix = `[${contextName}] `;
    
    // Return an object matching LoggingServiceAPI, but calls the main log method
    // with the contextName as the source.
    return {
      info: (message: string, ...args: unknown[]) => {
        this.log('info', prefix + message, contextName, ...args); // Pass contextName as source
      },
      warn: (message: string, ...args: unknown[]) => {
        this.log('warn', prefix + message, contextName, ...args); // Pass contextName as source
      },
      error: (message: string, ...args: unknown[]) => {
        this.log('error', prefix + message, contextName, ...args); // Pass contextName as source
      },
      debug: (message: string, ...args: unknown[]) => {
        this.log('debug', prefix + message, contextName, ...args); // Pass contextName as source
      },
      createScopedLogger: this.createScopedLogger.bind(this), // Allow creating sub-scopes
      // Pass the initialize method, bound to the original instance to keep `this` context
      initialize: this.initialize.bind(this),
      dispose: async () => {
        // Scoped logger dispose does nothing extra
      }
    } as LoggingService;
  }
  
  private getTimestamp(): string {
    return new Date().toISOString();
  }
  
  // Try to send queued logs to the current window
  private flushLogQueue(): void {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    const webContents = currentWindow?.webContents;
    
    if (webContents && !webContents.isDestroyed() && this.logQueue.length > 0) {
      // Log directly to console to avoid recursion via this.info -> this.log -> sendToRenderer -> flushLogQueue
      console.info(`[LoggingService] Flushing ${this.logQueue.length} queued log entries.`);
      // Use a temporary copy and clear original queue first to avoid race conditions
      const queueToSend = [...this.logQueue];
      this.logQueue = [];
      queueToSend.forEach(entry => this.sendToRendererInternal(webContents, entry));
    } else if (this.logQueue.length > 0) {
      this.debug(`Window not ready, ${this.logQueue.length} logs remain queued.`);
    }
  }
  
  // Internal method to actually send to specific webContents
  private sendToRendererInternal(webContents: WebContents, entry: LogEntry): void {
    // Use the correct channel from IpcChannels
    const channel = IpcChannels.LOG_MESSAGE;
    try {
      // Wrap the entry in the standard IpcEventArgs structure
      webContents.send(channel, {payload: entry} as IpcEventArgs<LogEntry>);
    } catch (error) {
      // This might happen if the window is destroyed between getting webContents and sending
      console.error("Failed to send log to renderer:", error);
      // Re-queue the message if sending failed
      this.queueLogEntry(entry);
    }
  }
  
  // Queues a log entry if sending is not possible
  private queueLogEntry(entry: LogEntry): void {
    this.logQueue.push(entry);
    // Limit queue size
    if (this.logQueue.length > 100) { // Keep limit reasonable
      this.logQueue.shift();
    }
  }
  
  // Отправка лога в Renderer (public facing logic)
  private sendToRenderer(entry: LogEntry): void {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    const webContents = currentWindow?.webContents;
    
    if (webContents && !webContents.isDestroyed()) {
      // If window is ready, try flushing queue first, then send current entry
      this.flushLogQueue(); // Attempt to send older messages first
      this.sendToRendererInternal(webContents, entry);
    } else {
      // If window not ready, queue the message
      this.queueLogEntry(entry);
    }
  }
}

// Экспортируем класс и синглтон
export {LoggingService};
export const logger = new LoggingService();
