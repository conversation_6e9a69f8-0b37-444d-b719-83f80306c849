import {BrowserWindow, dialog} from 'electron';
import {LoggingServiceAPI} from './logging.service';
import {IpcServiceAPI} from './ipc.service';
import {IpcChannels} from '@shared/constants/ipc-channels';
import {Disposable} from '@shared/types/common';
import type {
  DialogInputResponseData,
  DialogShowSaveConfirmationArgs,
  DialogShowSaveConfirmationResult,
  InputDialogOptions as IpcInputDialogOptions,
  IpcInvokeResult
} from '@shared/types/ipc';
import {BaseService, IBaseService} from "@services/base.service";

// Опции для диалога ввода (локальный тип, может отличаться от IPC)
export interface InputDialogOptions {
  title: string;
  prompt: string;
  defaultValue?: string;
  placeholder?: string;
  validateInput?: (value: string) => string | null | undefined | Promise<string | null | undefined>;
}

// API, предоставляемое DialogService для расширений
export interface DialogServiceAPI extends IBaseService {
  showInputDialog(options: InputDialogOptions): Promise<string | null>;
  
  showInformationMessage(message: string, detail?: string): Promise<Electron.MessageBoxReturnValue>;
  
  showWarningMessage(message: string, detail?: string): Promise<Electron.MessageBoxReturnValue>;
  
  showErrorMessage(message: string, detail?: string): Promise<Electron.MessageBoxReturnValue>;
  
  showConfirmationDialog(options: Electron.MessageBoxOptions): Promise<Electron.MessageBoxReturnValue>;
  
  showOpenDialog(options: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue>;
  
  showSaveConfirmationDialog(args: DialogShowSaveConfirmationArgs): Promise<DialogShowSaveConfirmationResult>;
}

// Класс DialogService
export class DialogService extends BaseService implements DialogServiceAPI {
  private getCurrentWindow?: () => (BrowserWindow | null);
  private requestCounter = 0;
  private readonly ipcService: IpcServiceAPI; // Use API type
  private pendingRequests = new Map<number, {
    resolve: (value: string | null) => void;
    reject: (reason?: unknown) => void;
    timeoutId: NodeJS.Timeout;
  }>();
  
  private disposables: Disposable[] = [];
  
  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, 'DialogService');
    this.ipcService = ipcService;
    this.registerIpcHandlers();
  }
  
  async initialize(getCurrentWindowFunc: () => (BrowserWindow | null)) {
    this.getCurrentWindow = getCurrentWindowFunc;
    await super.initialize(getCurrentWindowFunc);
    this.isInitialized = true;
  }
  
  async showInputDialog(options: InputDialogOptions): Promise<string | null> {
    const requestId = this.requestCounter++;
    // Cast string to DialogId type to fix the type error
    const dialogId = String(requestId);
    
    this.logger.info(`Sending request ${requestId} to ${IpcChannels.WORKBENCH_SHOW_INPUT_DIALOG}`, options);
    
    let timeoutId: NodeJS.Timeout;
    
    return new Promise<string | null>((resolve, reject) => {
      timeoutId = setTimeout(() => {
        const controls = this.pendingRequests.get(requestId);
        if (controls) {
          this.logger.warn(`Input dialog request ${requestId} timed out.`);
          this.pendingRequests.delete(requestId);
          controls.resolve(null);
        }
      }, 30000); // 30 seconds timeout
      
      this.pendingRequests.set(requestId, {resolve, reject, timeoutId});
      const data: IpcInputDialogOptions = {dialogId, ...options};
      this.ipcService.send(IpcChannels.WORKBENCH_SHOW_INPUT_DIALOG, data);
    });
  }
  
  // --- Standard Message Boxes (Direct Electron API calls) ---
  async showInformationMessage(message: string, detail?: string): Promise<Electron.MessageBoxReturnValue> {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    if (!currentWindow || currentWindow.isDestroyed()) {
      this.logger.error('Cannot show info message, main window not available.');
      throw new Error('Main window is not available.');
    }
    return await dialog.showMessageBox(currentWindow, {type: 'info', message, detail, buttons: ['OK']});
  }
  
  async showWarningMessage(message: string, detail?: string): Promise<Electron.MessageBoxReturnValue> {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    if (!currentWindow || currentWindow.isDestroyed()) {
      this.logger.error('Cannot show warning message, main window not available.');
      throw new Error('Main window is not available.');
    }
    return await dialog.showMessageBox(currentWindow, {type: 'warning', message, detail, buttons: ['OK']});
  }
  
  async showErrorMessage(message: string, detail?: string): Promise<Electron.MessageBoxReturnValue> {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    if (!currentWindow || currentWindow.isDestroyed()) {
      this.logger.error('Cannot show error message, main window not available.');
      throw new Error('Main window is not available.');
    }
    return await dialog.showMessageBox(currentWindow, {type: 'error', message, detail, buttons: ['OK']});
  }
  
  // ---
  
  async showConfirmationDialog(options: Electron.MessageBoxOptions): Promise<Electron.MessageBoxReturnValue> {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    if (!currentWindow || currentWindow.isDestroyed()) {
      this.logger.error('Cannot show confirmation dialog, main window not available.');
      throw new Error('Main window is not available.');
    }
    const defaultOptions: Electron.MessageBoxOptions = {
      type: 'question',
      buttons: ['Cancel', 'OK'],
      defaultId: 1,
      cancelId: 0, ...options
    };
    return await dialog.showMessageBox(currentWindow, defaultOptions);
  }
  
  // --- showOpenDialog (Direct Electron API call) ---
  async showOpenDialog(options: Electron.OpenDialogOptions): Promise<Electron.OpenDialogReturnValue> {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    if (!currentWindow || currentWindow.isDestroyed()) {
      this.logger.error('Cannot show open dialog, main window not available.');
      throw new Error('Main window is not available.');
    }
    this.logger.info('Showing open dialog directly with options:', options);
    const result = await dialog.showOpenDialog(currentWindow, options);
    this.logger.info('Open dialog result:', result);
    return result;
  }
  
  // --- showSaveConfirmationDialog (Public API method) ---
  async showSaveConfirmationDialog(args: DialogShowSaveConfirmationArgs): Promise<DialogShowSaveConfirmationResult> {
    // This method now directly calls the internal handler logic
    // We could also make this call an IPC invoke to itself, but direct call is simpler here
    const result = await this.handleShowSaveConfirmationDialog(args);
    if (result.success) {
      return result.data;
    } else {
      // Log the error and return 'cancel' as a safe default
      this.logger.error('Error showing save confirmation dialog via public API:', result.error);
      return 'cancel';
    }
  }
  
  async dispose() {
    // Dispose all registered handlers
    this.disposables.forEach(d => d.dispose());
    this.disposables = [];
    
    this.pendingRequests.forEach((controls, id) => {
      clearTimeout(controls.timeoutId);
      this.logger.warn(`Rejecting pending request ${id} due to disposal.`);
      controls.reject(new Error('DialogService is being disposed.'));
    });
    this.pendingRequests.clear();
    this.getCurrentWindow = undefined;
    this.logger.info('Disposed.');
  }
  
  // Register all IPC handlers for this service
  private registerIpcHandlers(): void {
    this.disposables.push(
      this.ipcService.handle(
        IpcChannels.DIALOG_INPUT_RESPONSE,
        (payload) => this.handleDialogInputResponse(payload as DialogInputResponseData)
      )
    );
    this.disposables.push(
      this.ipcService.handle(
        IpcChannels.DIALOG_SHOW_OPEN_DIALOG,
        (payload) => this.handleShowOpenDialog(payload as Electron.OpenDialogOptions)
      )
    );
    // Register the new handler for save confirmation
    this.disposables.push(
      this.ipcService.handle(
        IpcChannels.DIALOG_SHOW_SAVE_CONFIRMATION,
        (payload) => this.handleShowSaveConfirmationDialog(payload as DialogShowSaveConfirmationArgs)
      )
    );
    this.logger.info(`Registered all dialog IPC handlers.`);
  }
  
  private handleDialogInputResponse(responsePayload: DialogInputResponseData): IpcInvokeResult<void> {
    const dialogId = responsePayload.dialogId;
    const value = responsePayload.value;
    const requestId = Number(dialogId);
    
    if (isNaN(requestId)) {
      this.logger.error(`Received invalid non-numeric dialogId/requestId: ${dialogId}`);
      return {success: false, error: {code: 'INVALID_REQUEST_ID', message: 'Invalid dialogId received.'}};
    }
    
    this.logger.info(`Received response invoke for request ${requestId}:`, {value});
    const promiseControls = this.pendingRequests.get(requestId);
    
    if (promiseControls) {
      clearTimeout(promiseControls.timeoutId);
      promiseControls.resolve(value);
      this.pendingRequests.delete(requestId);
      return {success: true, data: undefined};
    } else {
      this.logger.warn(`Received response for unknown or timed-out request ID: ${requestId}`);
      return {
        success: false,
        error: {code: 'UNKNOWN_REQUEST_ID', message: `Unknown or timed-out request ID: ${requestId}`}
      };
    }
  }
  
  // Handler for DIALOG_SHOW_OPEN_DIALOG
  private async handleShowOpenDialog(options: Electron.OpenDialogOptions): Promise<IpcInvokeResult<Electron.OpenDialogReturnValue>> {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    if (!currentWindow || currentWindow.isDestroyed()) {
      this.logger.error('Cannot show open dialog, main window not available.');
      return {success: false, error: {code: 'WINDOW_UNAVAILABLE', message: 'Main window is not available.'}};
    }
    try {
      this.logger.info('Showing open dialog with options:', options);
      const result = await dialog.showOpenDialog(currentWindow, options);
      this.logger.info('Open dialog result:', result);
      return {success: true, data: result};
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error showing open dialog: ${message}`, error);
      return {success: false, error: {code: 'DIALOG_ERROR', message: `Failed to show open dialog: ${message}`}};
    }
  }
  
  // ---
  
  // --- Handler for DIALOG_SHOW_SAVE_CONFIRMATION ---
  private async handleShowSaveConfirmationDialog(args: DialogShowSaveConfirmationArgs): Promise<IpcInvokeResult<DialogShowSaveConfirmationResult>> {
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    if (!currentWindow || currentWindow.isDestroyed()) {
      this.logger.error('Cannot show save confirmation dialog, main window not available.');
      // Default to 'cancel' if window is gone? Or throw error? Let's return 'cancel'.
      return {success: true, data: 'cancel'};
    }
    try {
      this.logger.info('Showing save confirmation dialog:', args);
      const result = await dialog.showMessageBox(currentWindow, {
        type: 'warning',
        buttons: ['Save', "Don't Save", 'Cancel'], // Standard order
        defaultId: 0, // Save
        cancelId: 2, // Cancel
        title: args.title || 'Save Changes?',
        message: args.message,
        detail: args.detail,
        noLink: true, // Recommended for security
      });
      
      let response: DialogShowSaveConfirmationResult;
      switch (result.response) {
        case 0:
          response = 'save';
          break;
        case 1:
          response = 'dontsave';
          break;
        default:
          response = 'cancel';
          break; // Includes cancelId (2) and closing dialog
      }
      this.logger.info('Save confirmation dialog result:', {responseIndex: result.response, mappedResult: response});
      return {success: true, data: response};
      
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error showing save confirmation dialog: ${message}`, error);
      // Return 'cancel' on error to prevent accidental data loss or closure
      return {success: true, data: 'cancel'};
    }
  }
}
