import {Disposable} from '@shared/types/common';
import {LoggingServiceAPI} from './logging.service';
import {ContextServiceAPI} from './context.service';
import {CommandServiceAPI} from './command.service';
import {IpcChannels} from '@shared/constants/ipc-channels';
import {IpcServiceAPI} from './ipc.service';
import {MenuGetAppMenuResult} from '@shared/types/ipc';
import {BrowserWindow, Menu, MenuItemConstructorOptions} from 'electron';
import {BaseService, IBaseService} from "@services/base.service";
import {WindowManager, WindowManagerEvents} from '@main/window.manager';
import {CommandArgs} from "@shared/types/commands";
import {AppMenuItem, MenuItemContribution, RegisteredMenuItem} from '@shared/types/menu';

export interface MenuServiceAPI extends IBaseService {
  /** Registers menu items for a specific context identifier. */
  registerMenuItems(contextIdentifier: string, items: MenuItemContribution[]): Disposable;
  
  /** Gets the structure of the main application menu. */
  getApplicationMenuStructure(): Promise<AppMenuItem[]>;
  
  /** Builds and sets the native application menu (mainly for macOS). */
  buildAndSetNativeMainMenu(): Promise<void>;
  
  /** Shows a context menu for a given identifier and optional arguments. */
  showContextMenu(contextIdentifier: string, menuArgs?: Record<string, unknown>): void;
}


export class MenuService extends BaseService implements MenuServiceAPI {
  private menuRegistry = new Map<string, RegisteredMenuItem[]>();
  private registrationDisposables = new Map<string, Disposable[]>();
  private readonly commandService: CommandServiceAPI;
  private readonly contextService: ContextServiceAPI;
  private readonly ipcService: IpcServiceAPI;
  private getCurrentWindow?: () => (BrowserWindow | null);
  
  // Queue for context menu requests made before the window is ready
  private pendingContextMenuRequests: { contextIdentifier: string, menuArgs?: Record<string, unknown> }[] = [];
  
  // Store a getter function for WindowManager, as it might not be available at construction time
  private readonly getWindowManager?: () => WindowManager | null;
  
  constructor(
    logger: LoggingServiceAPI,
    commandService: CommandServiceAPI,
    contextService: ContextServiceAPI,
    ipcService: IpcServiceAPI,
    windowManagerGetter?: () => WindowManager | null // Accept a getter function
  ) {
    super(logger, 'MenuService');
    this.commandService = commandService;
    this.contextService = contextService;
    this.ipcService = ipcService;
    this.getWindowManager = windowManagerGetter; // Store the getter
    
    // Attempt to get the WindowManager instance and subscribe to events if available
    const wmInstance = this.getWindowManager ? this.getWindowManager() : null;
    if (wmInstance) {
      this.logger.info('WindowManager instance available at construction time. Subscribing to events.');
      wmInstance.on(WindowManagerEvents.WINDOW_CREATED, this.handleWindowCreated.bind(this));
      wmInstance.on(WindowManagerEvents.WINDOW_READY, this.handleWindowReady.bind(this));
    } else {
      this.logger.info('WindowManager instance not available at construction time. Will rely on initialize.');
    }
  }
  
  public async initialize(getCurrentWindowFunc: () => (BrowserWindow | null)) {
    await super.initialize(getCurrentWindowFunc);
    
    this.getCurrentWindow = getCurrentWindowFunc;
    
    // Check if the window getter function works
    const window = this.getCurrentWindow ? this.getCurrentWindow() : null;
    this.logger.info(`MenuService initialized. Window available: ${!!window}`);
    
    // Process any pending context menu requests if the window is ready
    if (window) {
      this.processPendingContextMenuRequests();
    }
    
    this.registerIpcHandlers();
    await this.buildAndSetNativeMainMenu(); // Build menu on initialization
    this.isInitialized = true;
  }
  
  /**
   * Registers menu items for the specified context.
   * @param contextIdentifier The context identifier (e.g., 'explorer/context/file').
   * @param items An array of menu items to register.
   * @returns A Disposable to unregister these items.
   */
  registerMenuItems(contextIdentifier: string, items: MenuItemContribution[]): Disposable {
    this.logger.info(`Registering ${items.length} menu items for context '${contextIdentifier}'`);
    if (!this.menuRegistry.has(contextIdentifier)) {
      this.menuRegistry.set(contextIdentifier, []);
      this.registrationDisposables.set(contextIdentifier, []);
    }
    // TODO: Get actual extensionId when registering
    const registeredItems: RegisteredMenuItem[] = items.map(item => ({...item, extensionId: 'unknown'}));
    this.menuRegistry.get(contextIdentifier)?.push(...registeredItems);
    
    const disposable: Disposable = {
      dispose: async () => {
        this.logger.info(`Unregistering menu items for context '${contextIdentifier}'`);
        const currentItems = this.menuRegistry.get(contextIdentifier);
        if (currentItems) {
          // Filter out the items being unregistered based on the original 'items' array
          const commandIdsToRemove = new Set(items.map(i => i.command));
          const updatedItems = currentItems.filter(regItem => !commandIdsToRemove.has(regItem.command)); // Assumes command ID is unique enough for removal
          
          if (updatedItems.length > 0) {
            this.menuRegistry.set(contextIdentifier, updatedItems);
          } else {
            // Remove context if no items left
            this.menuRegistry.delete(contextIdentifier);
            this.registrationDisposables.delete(contextIdentifier);
          }
        }
        // Remove this specific disposable from the context's list
        const contextDisposables = this.registrationDisposables.get(contextIdentifier);
        if (contextDisposables) {
          const index = contextDisposables.indexOf(disposable);
          if (index > -1) {
            contextDisposables.splice(index, 1);
          }
        }
      }
    };
    
    // Store the disposable associated with this registration
    const disposablesForContext = this.registrationDisposables.get(contextIdentifier);
    if (disposablesForContext) {
      disposablesForContext.push(disposable);
    } else {
      // This case should ideally not happen if the context was added above
      this.logger.error(`Could not find disposables array for context '${contextIdentifier}' during registration.`);
    }
    return disposable;
  }
  
  /**
   * Creates and shows a context menu for the given identifier and arguments.
   * @param contextIdentifier The menu context identifier (e.g., 'explorer/context/file').
   * @param menuArgs Context-specific arguments (e.g., { itemId: '...' }).
   */
  async showContextMenu(contextIdentifier: string, menuArgs?: Record<string, unknown>) {
    this.logger.info(`Showing context menu for '${contextIdentifier}' with args:`, menuArgs);
    const registeredItems = this.menuRegistry.get(contextIdentifier) || [];
    this.logger.info(`Found ${registeredItems.length} registered items for context '${contextIdentifier}'`);
    
    const activeItems: RegisteredMenuItem[] = [];
    
    // Filter items based on 'when' condition
    for (const item of registeredItems) {
      const isActive = this.contextService.evaluate(item.when);
      this.logger.info(`Item '${item.command}' (when='${item.when}') active: ${isActive}`);
      if (isActive) {
        activeItems.push(item);
      }
    }
    
    if (activeItems.length === 0) {
      this.logger.info(`No active menu items found for context '${contextIdentifier}'.`);
      return;
    }
    
    // Sort items by group
    activeItems.sort((a, b) => {
      const groupA = a.group || 'zzz'; // Default to end
      const groupB = b.group || 'zzz';
      return groupA.localeCompare(groupB);
    });
    
    // Build Electron menu template
    const template: MenuItemConstructorOptions[] = [];
    let lastGroup: string | undefined = undefined;
    
    for (const item of activeItems) {
      // Add separator if group changes
      if (item.group && item.group !== lastGroup && template.length > 0) {
        template.push({type: 'separator'});
      }
      lastGroup = item.group;
      
      // Get command details for the title
      const commandDetails = this.commandService.getCommand(item.command); // Cast to CommandId here
      const commandTitle = commandDetails?.title ?? item.command; // Fallback to command ID if title not found
      
      template.push({
        label: commandTitle,
        click: () => {
          this.logger.info(`Executing command '${item.command}' from context menu with args:`, menuArgs);
          this.commandService.executeCommand(
            {commandId: item.command, args: menuArgs as CommandArgs} // Cast string to CommandId
          ).catch(err => {
            this.logger.error(`Error executing command '${item.command}':`, err);
          });
        }
      });
    }
    
    // Build and show the menu
    const menu = Menu.buildFromTemplate(template);
    
    if (!this.getCurrentWindow) {
      this.logger.error(`Cannot show context menu for '${contextIdentifier}', getCurrentWindow is undefined. Ensure MenuService is initialized correctly.`);
      return;
    }
    
    const currentWindow = this.getCurrentWindow ? this.getCurrentWindow() : null;
    this.logger.info(`Current window from getCurrentWindow: ${currentWindow ? 'available' : 'null'}`);
    
    // If window is not ready yet, queue the request
    if (!currentWindow) {
      this.logger.info(`Window not available yet. Queuing context menu request: ${contextIdentifier}`);
      this.pendingContextMenuRequests.push({contextIdentifier, menuArgs});
      return;
    }
    
    // Show the menu if the window is available and not destroyed
    if (currentWindow && !currentWindow.isDestroyed()) {
      this.logger.info(`Popping up menu with ${template.length} items at coords: x=${menuArgs?.x}, y=${menuArgs?.y}`);
      try {
        // Provide coordinates if available in menuArgs
        const x = typeof menuArgs?.x === 'number' ? menuArgs.x : undefined;
        const y = typeof menuArgs?.y === 'number' ? menuArgs.y : undefined;
        menu.popup({window: currentWindow, x, y});
        this.logger.info(`Popup menu shown successfully.`);
      } catch (error) {
        this.logger.error(`Error showing popup menu:`, error);
      }
    } else {
      this.logger.error(`Cannot show context menu for '${contextIdentifier}', window is unavailable or destroyed.`);
      
      // Fallback: Try getting the focused window
      try {
        this.logger.info(`Attempting to get focused window as fallback.`);
        const focusedWindow = BrowserWindow.getFocusedWindow();
        this.logger.info(`Focused window: ${focusedWindow ? 'available' : 'null'}`);
        
        if (focusedWindow && !focusedWindow.isDestroyed()) {
          this.logger.info(`Using focused window as fallback for context menu.`);
          const x = typeof menuArgs?.x === 'number' ? menuArgs.x : undefined;
          const y = typeof menuArgs?.y === 'number' ? menuArgs.y : undefined;
          menu.popup({window: focusedWindow, x, y});
          this.logger.info(`Popup menu shown successfully via fallback window.`);
        } else {
          this.logger.error(`No focused window available for context menu fallback.`);
          
          // Fallback 2: Try getting the first available window
          const allWindows = BrowserWindow.getAllWindows();
          this.logger.info(`Total windows available: ${allWindows.length}`);
          
          if (allWindows.length > 0) {
            const firstWindow = allWindows[0];
            if (firstWindow && !firstWindow.isDestroyed()) {
              this.logger.info(`Using first available window for context menu.`);
              const x = typeof menuArgs?.x === 'number' ? menuArgs.x : undefined;
              const y = typeof menuArgs?.y === 'number' ? menuArgs.y : undefined;
              menu.popup({window: firstWindow, x, y});
              this.logger.info(`Popup menu shown successfully via first available window.`);
            } else {
              this.logger.error(`First available window is destroyed.`);
            }
          } else {
            this.logger.error(`No windows available for context menu.`);
          }
        }
      } catch (error) {
        this.logger.error(`Error during fallback window retrieval:`, error);
      }
    }
  }
  
  /**
   * Returns the structure of the main application menu.
   * TODO: Dynamically build this based on contributions.
   */
  async getApplicationMenuStructure(): Promise<AppMenuItem[]> {
    // Basic static structure for now
    this.logger.warn('[MenuService] getApplicationMenuStructure currently returns a static structure.');
    return [
      {
        label: 'File', // TODO: Localize
        submenu: [
          {label: 'New Book...', commandId: 'books.action.createNew'},
          {type: 'separator'},
          {label: 'Save Scene', commandId: 'editor.action.saveScene'}, // Example, might need context
          {type: 'separator'},
          {label: 'Settings...', commandId: 'workbench.action.openSettings'},
          {type: 'separator'},
          {label: 'Exit', role: 'quit'} // Use role for standard actions
        ]
      },
      {
        label: 'Edit',
        submenu: [
          {label: 'Undo', role: 'undo'},
          {label: 'Redo', role: 'redo'},
          {type: 'separator'},
          {label: 'Cut', role: 'cut'},
          {label: 'Copy', role: 'copy'},
          {label: 'Paste', role: 'paste'},
          {label: 'Select All', role: 'selectAll'}
        ]
      },
      {
        label: 'View',
        submenu: [
          {label: 'Toggle Sidebar', commandId: 'workbench.action.toggleSidebarVisibility'},
          {label: 'Toggle Panel', commandId: 'workbench.action.togglePanelVisibility'},
          {type: 'separator'},
          {label: 'Toggle Developer Tools', role: 'toggleDevTools'},
          {label: 'Toggle Full Screen', role: 'togglefullscreen'}
        ]
      },
      {
        label: 'Help',
        submenu: [
          {label: 'About', commandId: 'app.about'} // Example
        ]
      }
    ];
  }
  
  /**
   * Builds and sets the native application menu (primarily for macOS).
   */
  async buildAndSetNativeMainMenu(): Promise<void> {
    this.logger.info('[MenuService] Building and setting native application menu...');
    const appMenuStructure = await this.getApplicationMenuStructure();
    
    // Convert our structure to Electron's MenuItemConstructorOptions
    const template = this.convertAppMenuToElectronTemplate(appMenuStructure);
    
    // Add standard macOS application menu if on darwin
    if (process.platform === 'darwin') {
      template.unshift({role: 'appMenu'}); // Standard App menu (App Name, About, Services, Hide, Quit)
      // Consider adding standard Window and Help menus for macOS consistency if needed
      // template.push({ role: 'windowMenu' });
      // template.push({ role: 'help' }); // Basic help menu
    }
    
    try {
      const menu = Menu.buildFromTemplate(template);
      Menu.setApplicationMenu(menu);
      this.logger.info('[MenuService] Native application menu set.');
    } catch (error) {
      this.logger.error('[MenuService] Failed to build or set native application menu:', error);
    }
  }
  
  async dispose() {
    await super.dispose();
    this.logger.info('[MenuService] Disposing menu registrations...');
    // Dispose all registration disposables
    this.registrationDisposables.forEach(contextDisposables => {
      contextDisposables.forEach(async (d) => {
        try {
          await d.dispose();
        } catch (e) {
          this.logger.error('Error disposing menu item registration:', e);
        }
      });
    });
    
    this.menuRegistry.clear();
    this.registrationDisposables.clear();
    Menu.setApplicationMenu(null); // Clear native menu on dispose
    this.getCurrentWindow = undefined; // Clear window getter
    this.logger.info('[MenuService] Disposed.');
  }
  
  /**
   * Handles the window creation event.
   */
  private handleWindowCreated(): void {
    this.logger.info(`Window created event received in MenuService.`);
    // Potentially rebuild menu if needed when a new window is created?
  }
  
  /**
   * Handles the window ready event.
   */
  private handleWindowReady(): void {
    this.logger.info(`Window ready event received in MenuService. Processing pending context menu requests...`);
    this.processPendingContextMenuRequests();
  }
  
  /**
   * Processes any context menu requests that were queued before the window was ready.
   */
  private processPendingContextMenuRequests(): void {
    if (this.pendingContextMenuRequests.length === 0) {
      return;
    }
    
    this.logger.info(`Processing ${this.pendingContextMenuRequests.length} pending context menu requests.`);
    
    // Copy queue and clear original
    const requests = [...this.pendingContextMenuRequests];
    this.pendingContextMenuRequests = [];
    
    // Process each request now that the window should be ready
    requests.forEach(request => {
      this.logger.info(`Processing queued context menu request for ${request.contextIdentifier}`);
      // Use a microtask to avoid potential issues if showContextMenu triggers sync operations
      queueMicrotask(() => {
        this.showContextMenu(request.contextIdentifier, request.menuArgs);
      });
    });
  }
  
  /**
   * Converts the application menu structure to Electron's format.
   */
  private convertAppMenuToElectronTemplate(items: readonly AppMenuItem[]): MenuItemConstructorOptions[] { // Accept ReadonlyArray
    return items.map(item => {
      const electronItem: MenuItemConstructorOptions = {};
      if (item.label) electronItem.label = item.label;
      
      if (item.role) {
        // If role is specified, Electron handles label, click, etc.
        electronItem.role = item.role;
      } else if (item.commandId) {
        // If commandId is specified, set up click handler
        const commandId = item.commandId; // Assign to new variable to satisfy TS without '!'
        electronItem.click = () => {
          this.commandService.executeCommand(
            {
              commandId: commandId, // Use the non-null variable
              args: {} // Pass empty args by default for menu items
            }
          ).catch(err => {
            this.logger.error(`[MenuService Native] Error executing command '${commandId}':`, err); // Use the non-null variable here too
          });
        };
      }
      
      // Handle other properties
      if (item.type) electronItem.type = item.type;
      if (item.checked !== undefined) electronItem.checked = item.checked;
      if (item.submenu) {
        // Recursively convert submenu
        electronItem.submenu = this.convertAppMenuToElectronTemplate(item.submenu);
      }
      
      return electronItem;
    });
  }
  
  /**
   * Registers IPC handlers for the MenuService.
   */
  private registerIpcHandlers(): void {
    this.logger.info('Registering IPC handlers for MenuService...');
    
    // Handler for showing context menu requested from renderer
    this.ipcService.handle<{ contextIdentifier: string, menuArgs?: Record<string, unknown> }>(
      IpcChannels.MENU_SHOW_CONTEXT_MENU,
      async (args) => {
        this.logger.info(`Received ${IpcChannels.MENU_SHOW_CONTEXT_MENU} request with args:`, args);
        const {contextIdentifier, menuArgs} = args;
        if (!contextIdentifier) {
          this.logger.error('Invalid menu context identifier provided.');
          throw new Error('Invalid menu context identifier provided.');
        }
        // Await showContextMenu to handle potential async operations within it
        await this.showContextMenu(contextIdentifier, menuArgs);
        return {success: true}; // Indicate success
      }
    );
    
    // Handler for getting the application menu structure for the renderer
    // Input type is undefined | null as no arguments are expected
    this.ipcService.handle<undefined | null, MenuGetAppMenuResult>(
      IpcChannels.MENU_GET_APP_MENU,
      async () => { // No args expected
        this.logger.info(`Received ${IpcChannels.MENU_GET_APP_MENU} request.`);
        return await this.getApplicationMenuStructure();
      }
    );
    
    this.logger.info('IPC handlers for MenuService registered.');
  }
}
