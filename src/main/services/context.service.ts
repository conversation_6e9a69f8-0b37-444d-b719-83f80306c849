import {LoggingServiceAPI} from './logging.service';
import {IpcChannels} from '@shared/constants/ipc-channels';
import {ContextGetValueArgs, ContextGetValueResult} from '@shared/types/ipc';
import {IpcServiceAPI} from './ipc.service';
import {BaseService, IBaseService} from "@services/base.service";
import {JsonValue} from '@shared/types/common';

// API, предоставляемое ContextService для расширений
export interface ContextServiceAPI extends IBaseService {
  setContext(key: string, value: JsonValue): void;
  getContext(key: string): JsonValue | undefined;
  removeContext(key: string): void;
  getAllContexts(): Record<string, JsonValue>;
  evaluate(whenClause: string | undefined): boolean;
}

// Класс ContextService
export class ContextService extends BaseService implements ContextServiceAPI {
  private context = new Map<string, JsonValue>();
  private readonly ipcService: IpcServiceAPI;
  
  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, 'ContextService');
    this.ipcService = ipcService;
    this.context.set('isDevelopment', !process.defaultApp);
    this.registerIpcHandlers();
  }
  
  /**
   * Устанавливает или обновляет значение ключа контекста.
   * @param key Ключ контекста (строка).
   * @param value Значение контекста.
   */
  setContext(key: string, value: JsonValue): void {
    if (key.trim() === '') {
      this.logger.error('Неверный ключ контекста.');
      return;
    }
    const oldValue = this.context.get(key);
    // Простая проверка на изменение для примитивов, для объектов может потребоваться глубокое сравнение
    if (oldValue !== value) {
      this.logger.info(`Установка контекста: ${key} = ${JSON.stringify(value)}`);
      this.context.set(key, value);
      this.notifyRenderer(key, value);
    }
  }
  
  /**
   * Получает значение ключа контекста.
   * @param key Ключ контекста.
   * @returns Значение или undefined, если ключ не найден.
   */
  getContext(key: string): JsonValue | undefined {
    return this.context.get(key);
  }
  
  /**
   * Удаляет ключ контекста.
   * @param key Ключ для удаления.
   */
  removeContext(key: string): void {
    if (this.context.has(key)) {
      this.logger.info(`Удаление контекста: ${key}`);
      this.context.delete(key);
      this.notifyRenderer(key, null);
    }
  }
  
  /**
   * Возвращает все текущие пары ключ-значение контекста.
   * @returns Объект, представляющий текущий контекст.
   */
  getAllContexts(): Record<string, JsonValue> {
    return Object.fromEntries(this.context);
  }
  
  /**
   * Оценивает простое выражение 'when'.
   * Поддерживает: 'key', '!key', 'key == value', 'key != value'.
   * Не поддерживает '&&', '||' и более сложные выражения на данном этапе.
   * @param whenClause Строка условия или undefined.
   * @returns true, если условие выполняется или не задано, иначе false.
   */
  evaluate(whenClause: string | undefined): boolean {
    if (!whenClause) {
      return true; // Нет условия - команда активна
    }
    
    // Убираем лишние пробелы
    const trimmedClause = whenClause.trim();
    
    // Проверка на 'key == value'
    let match = trimmedClause.match(/^([a-zA-Z0-9_.-]+)\s*==\s*(.+)$/);
    if (match) {
      const key = match[1];
      const expectedValueStr = match[2].trim();
      let parsedExpectedValue: JsonValue = expectedValueStr;
      // Пытаемся распарсить значение (true, false, null, числа, строки в кавычках)
      try {
        if (expectedValueStr === 'true') parsedExpectedValue = true;
        else if (expectedValueStr === 'false') parsedExpectedValue = false;
        else if (expectedValueStr === 'null') parsedExpectedValue = null;
        else if (/^['"].*['"]$/.test(expectedValueStr)) parsedExpectedValue = expectedValueStr.slice(1, -1);
        else if (!isNaN(Number(expectedValueStr))) parsedExpectedValue = Number(expectedValueStr);
      } catch { /* Оставляем как строку, если парсинг не удался */
      }
      
      const actualValue = this.context.get(key);
      return actualValue === parsedExpectedValue;
    }
    
    // Проверка на 'key != value'
    match = trimmedClause.match(/^([a-zA-Z0-9_.-]+)\s*!=\s*(.+)$/);
    if (match) {
      const key = match[1];
      const expectedValueStr = match[2].trim();
      let parsedExpectedValue: JsonValue = expectedValueStr;
      try {
        if (expectedValueStr === 'true') parsedExpectedValue = true;
        else if (expectedValueStr === 'false') parsedExpectedValue = false;
        else if (expectedValueStr === 'null') parsedExpectedValue = null;
        else if (/^['"].*['"]$/.test(expectedValueStr)) parsedExpectedValue = expectedValueStr.slice(1, -1);
        else if (!isNaN(Number(expectedValueStr))) parsedExpectedValue = Number(expectedValueStr);
      } catch { /* Оставляем как строку */
      }
      
      const actualValue = this.context.get(key);
      return actualValue !== parsedExpectedValue;
    }
    
    // Проверка на '!key' (ключ должен быть false или undefined)
    match = trimmedClause.match(/^!([a-zA-Z0-9_.-]+)$/);
    if (match) {
      const key = match[1];
      const actualValue = this.context.get(key);
      return !actualValue;
    }
    
    // Проверка на 'key' (ключ должен быть true)
    match = trimmedClause.match(/^([a-zA-Z0-9_.-]+)$/);
    if (match) {
      const key = match[1];
      const actualValue = this.context.get(key);
      return !!actualValue; // Приводим к boolean
    }
    
    this.logger.warn(`Неподдерживаемый формат условия 'when': ${whenClause}`);
    return false; // Неподдерживаемый формат считаем невыполненным
  }
  
  async dispose() {
    await super.dispose();
    this.context.clear();
  }
  
  private registerIpcHandlers(): void {
    this.logger.info('Регистрация обработчиков IPC для ContextService...');
    
    this.ipcService.handle<ContextGetValueArgs, ContextGetValueResult>(
      IpcChannels.CONTEXT_GET,
      (args) => {
        const {key} = args;
        if (!key) {
          this.logger.error(`IPC: Получен неверный ключ контекста в ${IpcChannels.CONTEXT_GET}`);
          return undefined;
        }
        return this.getContext(key);
      }
    );
    
    this.logger.info('Обработчики IPC для ContextService зарегистрированы.');
  }
  
  private notifyRenderer(key: string, value: JsonValue): void {
    try {
      this.logger.info(`Уведомление renderer об обновлении контекста для ключа ${key} через ${IpcChannels.CONTEXT_CHANGED}`);
      this.ipcService.send(IpcChannels.CONTEXT_CHANGED, {key, value});
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.warn(`Не удалось отправить уведомление об обновлении контекста через IpcService: ${errorMessage}.`);
      if (error instanceof Error) {
        this.logger.info('Детали ошибки отправки IpcService:', error);
      }
    }
  }
}
