import {LoggingServiceAPI} from './logging.service';
import {BrowserWindow} from "electron";
import {Disposable} from "@shared/types/common";

/**
 * Базовый интерфейс для всех сервисов приложения.
 * Определяет общие методы для управления жизненным циклом сервиса.
 */
export interface IBaseService extends Disposable {
  /**
   * Инициализирует сервис с необходимыми зависимостями.
   * Вызывается после создания основного окна приложения.
   *
   * @param windowGetter Функция для получения текущего главного окна.
   */
  initialize(windowGetter: () => BrowserWindow | null): Promise<void>;
}


/**
 * Базовый класс для всех сервисов, который предоставляет стандартизированную
 * реализацию логирования и базовых методов жизненного цикла.
 */
export abstract class BaseService implements IBaseService {
  protected readonly logger: LoggingServiceAPI;
  protected readonly serviceName: string;
  protected isDisposed = false;
  protected isInitialized = false;
  protected windowGetter: (() => BrowserWindow | null) | undefined;
  
  /**
   * @param rootLogger Корневой логгер приложения
   * @param serviceName Имя сервиса для контекстного логирования (автоматически определяется из имени класса, если не указано)
   */
  protected constructor(rootLogger: LoggingServiceAPI, serviceName: string) {
    this.serviceName = serviceName || this.constructor.name || 'BaseService';
    this.logger = rootLogger.createScopedLogger(serviceName);
  }
  
  async initialize(windowGetter: () => (BrowserWindow | null)): Promise<void> {
    if (this.isInitialized) return Promise.resolve();
    this.throwIfDisposed();
    
    this.windowGetter = windowGetter;
    this.logger.info('Initializing service...');
    return Promise.resolve();
  }
  
  /**
   * Освобождает ресурсы, используемые сервисом.
   * Подклассы должны переопределять этот метод для освобождения специфичных ресурсов,
   * не забывая вызвать super.dispose().
   */
  public async dispose(): Promise<void> {
    if (this.isDisposed) {
      this.logger.warn('Attempt to dispose an already disposed service');
      return;
    }
    
    this.isDisposed = true;
    this.logger.info('Disposed.');
  }
  
  /**
   * Проверяет, что сервис не был уничтожен, и выбрасывает исключение в противном случае.
   * Рекомендуется вызывать в начале каждого публичного метода сервиса.
   *
   * @throws Error если сервис был уничтожен
   */
  protected throwIfDisposed(): void {
    if (this.isDisposed) {
      const message = `Service ${this.constructor.name} has been disposed`;
      this.logger.error(message);
      throw new Error(message);
    }
  }
}
