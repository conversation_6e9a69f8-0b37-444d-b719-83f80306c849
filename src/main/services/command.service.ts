import {ContextServiceAP<PERSON>} from './context.service';
import {LoggingServiceAPI} from './logging.service';
import {KeybindingServiceAPI} from './keybinding.service';
import {IpcServiceAPI} from './ipc.service';
import {DisposableCollection} from "@main/disposable-collection";
import {BaseService, IBaseService} from "@services/base.service";
import {app, BrowserWindow} from "electron";
import {CommandIds} from '@shared/constants/command-ids';
import {IpcChannels} from '@shared/constants/ipc-channels';
import {Command, CommandArgs, CommandResult, EditorOpenTabArgs} from '@shared/types/commands';
import {
  CommandsExecuteArgs,
  CommandsExecuteResult,
  CommandsGetAllResult,
  ContextSetValueArgs,
  SettingsSetValueArgs
} from '@shared/types/ipc';
import {ConfigurationServiceAPI} from "@services/configuration.service";
import {EditorServiceAPI} from "@services/editor.service";
import {Disposable, JsonValue} from "@shared/types/common";

export interface CommandServiceAPI extends IBaseService {
  registerCommand(command: Command): Disposable;
  
  executeCommand<T = CommandResult>(args: CommandsExecuteArgs): Promise<T | undefined>;
  
  getCommands(): Omit<Command, 'handler'>[];
  
  getCommand(commandId: string): Readonly<Command> | undefined;
}

export class CommandService extends BaseService implements CommandServiceAPI {
  private commands = new Map<string, { command: Command, registrationDisposables: DisposableCollection }>();
  
  private readonly keybindingService: KeybindingServiceAPI;
  private readonly ipcService: IpcServiceAPI;
  private readonly contextService: ContextServiceAPI;
  private readonly settingsService: ConfigurationServiceAPI;
  private readonly editorService: EditorServiceAPI;
  
  constructor(
    logger: LoggingServiceAPI,
    contextService: ContextServiceAPI,
    keybindingService: KeybindingServiceAPI,
    ipcService: IpcServiceAPI,
    settingsService: ConfigurationServiceAPI,
    editorService: EditorServiceAPI
  ) {
    super(logger, 'CommandService');
    this.contextService = contextService;
    this.keybindingService = keybindingService;
    this.ipcService = ipcService;
    this.contextService = contextService;
    this.settingsService = settingsService;
    this.editorService = editorService;
  }
  
  public async initialize(windowGetter: () => (BrowserWindow | null)): Promise<void> {
    this.registerCoreCommands();
    this.registerIpcHandlers();
    await super.initialize(windowGetter);
    this.isInitialized = true;
  }
  
  /**
   * Registers a command and its keybindings.
   * @param command The command object to register.
   * @returns A Disposable to unregister the command.
   */
  registerCommand(command: Command): Disposable {
    if (this.commands.has(command.id)) {
      this.logger.warn(`Command '${command.id}' is already registered. Overwriting previous registration.`);
      this.commands.get(command.id)?.registrationDisposables.dispose();
    }
    this.logger.info(`Registering command '${command.id}'`);
    
    const disposables = new DisposableCollection();
    this.commands.set(command.id, {command, registrationDisposables: disposables});
    
    // Register associated keybindings if they exist
    const keybindings = typeof command.keybinding === 'string'
      ? [command.keybinding]
      : Array.isArray(command.keybinding)
        ? command.keybinding
        : [];
    
    for (const key of keybindings) {
      if (key) {
        const keybindingDisposable = this.keybindingService.registerKeybinding({
          key: key,
          command: command.id,
          when: command.when
        });
        disposables.push(keybindingDisposable);
      }
    }
    
    // Add a disposable to remove the command itself
    const commandRemovalDisposable: Disposable = {
      dispose: async () => {
        this.logger.info(`Unregistering command '${command.id}'`);
        this.commands.delete(command.id);
      }
    };
    disposables.push(commandRemovalDisposable);
    
    return disposables;
  }
  
  /**
   * Executes a registered command.
   * @param args Arguments containing the command ID and parameters.
   * @returns The result of the command execution.
   */
  async executeCommand<T = CommandResult>(args: CommandsExecuteArgs): Promise<T | undefined> {
    const {commandId, args: commandArgs} = args;
    // Cast incoming string commandId to branded type for map lookup
    const registration = this.commands.get(commandId);
    if (!registration) {
      this.logger.error(`Command '${commandId}' not found.`);
      throw new Error(`Command '${commandId}' not found.`);
    }
    
    const command = registration.command;
    
    // Check 'when' context before execution
    if (!this.contextService.evaluate(command.when)) {
      this.logger.warn(`Command '${commandId}' is not active in the current context ('${command.when}').`);
      throw new Error(`Command '${commandId}' is not active in the current context.`);
    }
    
    this.logger.info(`Executing command '${commandId}' with args:`, commandArgs);
    
    try {
      // Pass args? or {} to handler, as args can be undefined in handler signature now
      const result = await command.handler(commandArgs ?? {});
      return result as T;
    } catch (error) {
      this.logger.error(`Error executing command '${commandId}':`, error);
      throw error;
    }
  }
  
  /**
   * Returns a list of all commands suitable for the Command Palette / Menus.
   * @returns An array of commands without handlers.
   */
  getCommands(): (Omit<Command, 'handler' | 'when' | 'keybinding'> & { keybindings?: string[] })[] {
    const commandList: (Omit<Command, 'handler' | 'when' | 'keybinding'> & { keybindings?: string[] })[] = [];
    for (const registration of this.commands.values()) {
      const command = registration.command;
      // Check if command is active in context and should be shown
      if (this.contextService.evaluate(command.when) && command.showInPalette !== false) {
        const activeBindings = this.keybindingService.getActiveKeybindingsForCommand(command.id);
        
        commandList.push({
          id: command.id,
          title: command.title,
          category: command.category,
          icon: command.icon,
          keybindings: activeBindings.length > 0 ? activeBindings : undefined,
        });
      }
    }
    
    // Sort the list: first by category (undefined/empty at the end), then by title
    commandList.sort((a, b) => {
      const categoryA = a.category ?? 'zzz'; // Use 'zzz' to sort undefined/empty last
      const categoryB = b.category ?? 'zzz';
      if (categoryA < categoryB) return -1;
      if (categoryA > categoryB) return 1;
      return a.title.localeCompare(b.title);
    });
    
    this.logger.info(`Returning ${commandList.length} active, sorted commands for UI.`);
    return commandList;
  }
  
  /**
   * Returns a command by its identifier.
   * @param commandId The ID of the command.
   * @returns The command object (readonly) or undefined if not found.
   */
  getCommand(commandId: string): Readonly<Command> | undefined {
    const registration = this.commands.get(commandId);
    // Return a frozen copy to prevent external modification
    return registration ? Object.freeze({...registration.command}) : undefined;
  }
  
  public async dispose(): Promise<void> {
    this.logger.info('Disposing all registered commands...');
    for (const registration of this.commands.values()) {
      try {
        await registration.registrationDisposables.dispose();
      } catch (e) {
        this.logger.error(`Error disposing resources for command '${registration.command.id}':`, e);
      }
    }
    this.commands.clear();
    this.logger.info('All commands disposed.');
  }
  
  /**
   * Registers IPC handlers for the CommandService.
   */
  private registerIpcHandlers(): void {
    this.logger.info('Registering IPC handlers for CommandService...');
    
    this.ipcService.handle<CommandsExecuteArgs, CommandsExecuteResult>(
      IpcChannels.COMMANDS_EXECUTE,
      async (args) => {
        const {commandId, args: commandArgs} = args;
        if (!commandId) throw new Error('Invalid command ID provided.');
        // Ensure commandArgs is an object if undefined/null
        return this.executeCommand({commandId, args: commandArgs ?? {}});
      }
    );
    
    this.ipcService.handle<never, CommandsGetAllResult>(
      IpcChannels.COMMANDS_GET_ALL,
      (): CommandsGetAllResult => {
        const registeredCommands = this.getCommands();
        // Basic check, although getCommands should always return an array
        if (!Array.isArray(registeredCommands)) {
          this.logger.error(`[IPC Handler ${IpcChannels.COMMANDS_GET_ALL}] this.getCommands() did not return an array.`);
          return [];
        }
        return registeredCommands;
      }
    );
    
    this.logger.info('IPC handlers for CommandService registered.');
  }
  
  /**
   * Registers core application commands.
   */
  private registerCoreCommands() {
    this.logger.info('[CoreCommands] Registering core commands...');
    
    // Command: Open Settings
    this.registerCommand({
      id: CommandIds.OPEN_SETTINGS,
      title: 'Open Settings',
      category: 'Preferences',
      keybinding: ['Cmd+,', 'Ctrl+,'],
      handler: async () => {
        this.logger.info('[Command] Executing workbench.action.openSettings');
        await this.executeCommand({
          commandId: CommandIds.OPEN_EDITOR,
          args: {
            editorType: 'workbench:settings-editor', // Assuming this is the correct EditorTypeId
            dataId: 'user-settings', // Unique ID for user settings data
            title: 'Settings',
          } as CommandArgs
        });
      }
    });
    
    // Command: Toggle Developer Tools
    this.registerCommand({
      id: CommandIds.TOGGLE_DEV_TOOLS,
      title: 'Toggle Developer Tools',
      category: 'Developer',
      keybinding: ['Cmd+Alt+I', 'Ctrl+Shift+I'],
      handler: () => {
        this.logger.info('[Command] Executing workbench.action.toggleDevTools');
        this.ipcService.send(IpcChannels.WORKBENCH_TOGGLE_DEVTOOLS, {});
      }
    });
    
    // Command: Toggle Full Screen
    this.registerCommand({
      id: CommandIds.TOGGLE_FULL_SCREEN,
      title: 'Toggle Full Screen',
      category: 'View',
      keybinding: ['F11', 'Ctrl+Cmd+F'], // macOS uses Ctrl+Cmd+F
      handler: () => {
        this.logger.info('[Command] Executing workbench.action.toggleFullScreen');
        this.ipcService.send(IpcChannels.WINDOW_TOGGLE_FULLSCREEN, {});
      }
    });
    
    // Command: Quit Application
    this.registerCommand({
      id: CommandIds.APP_QUIT,
      title: 'Quit',
      category: 'Application', // Or 'File'
      keybinding: ['Cmd+Q', 'Ctrl+Q'],
      handler: () => {
        this.logger.info('[Command] Executing app.quit');
        app.quit();
      }
    });
    
    // Command: Toggle Command Palette
    this.registerCommand({
      id: CommandIds.TOGGLE_COMMAND_PALETTE,
      title: 'Command Palette...',
      category: 'View',
      keybinding: ['Cmd+Shift+P', 'Ctrl+Shift+P'],
      showInPalette: false, // Usually not shown in the palette itself
      handler: () => {
        this.logger.info('[Command] Executing workbench.action.toggleCommandPalette');
        this.ipcService.send(IpcChannels.WORKBENCH_TOGGLE_PALETTE, {});
      }
    });
    
    // Command: Toggle Sidebar Visibility
    this.registerCommand({
      id: CommandIds.TOGGLE_SIDEBAR_VISIBILITY,
      title: 'Toggle Sidebar Visibility',
      category: 'View',
      handler: () => {
        this.logger.info('[Command] Executing workbench.action.toggleSidebarVisibility');
        this.ipcService.send(IpcChannels.WORKBENCH_TOGGLE_SIDEBAR, {});
      }
    });
    
    // Command: Toggle Panel Visibility
    this.registerCommand({
      id: CommandIds.TOGGLE_PANEL_VISIBILITY,
      title: 'Toggle Panel Visibility',
      category: 'View',
      handler: () => {
        this.logger.info('[Command] Executing workbench.action.togglePanelVisibility');
        this.ipcService.send(IpcChannels.WORKBENCH_TOGGLE_PANEL, {});
      }
    });
    
    // Command: Open Editor (Internal)
    this.registerCommand({
      id: CommandIds.OPEN_EDITOR,
      title: 'Open Editor', // Internal command, not usually user-facing
      showInPalette: false,
      handler: async (args?: CommandArgs) => {
        // Use double assertion now that CommandArgs is Record<string, unknown>
        const specificArgs = args as unknown as EditorOpenTabArgs;
        this.logger.info(`[Command] Executing workbench.action.openEditor with args:`, specificArgs);
        const {editorType, dataId, title, options} = specificArgs;
        if (!editorType || !dataId) {
          this.logger.error('[Command] workbench.action.openEditor: Missing editorType or dataId.');
          throw new Error('Command openEditor requires editorType and dataId.');
        }
        
        const provider = this.editorService.getEditorProvider(editorType);
        if (!provider) {
          this.logger.error(`[Command] workbench.action.openEditor: Provider not found for type: ${editorType}`);
          throw new Error(`Editor provider not found for type: ${editorType}`);
        }
        
        this.logger.info(`[CoreCommand] workbench.action.openEditor: Found provider:`, provider);
        
        // Construct tab data
        const tabData = {
          id: `${editorType}:${dataId}`, // Unique ID for the tab instance
          editorType: provider.editorType,
          extensionId: provider.extensionId,
          componentName: provider.componentName,
          // Construct title carefully, handling potential missing provider title
          title: title ?? provider.title?.replace('{title}', dataId) ?? provider.editorType,
          icon: provider.icon,
          dataId: dataId,
          options: options ?? {}
        };
        
        this.logger.info(`[CoreCommand] workbench.action.openEditor: Sending '${IpcChannels.WORKBENCH_OPEN_TAB}' event with data:`, tabData);
        this.ipcService.send(IpcChannels.WORKBENCH_OPEN_TAB, tabData);
        return {success: true};
      }
    });
    
    // Command: Trigger Save Active Editor (Internal)
    this.registerCommand({
      id: CommandIds.TRIGGER_SAVE_ACTIVE_EDITOR,
      title: 'Save Active Editor', // Internal command
      showInPalette: false,
      handler: () => {
        this.logger.info('[Command] Executing workbench:triggerSaveActiveEditor');
        // TODO: Implement logic to find active editor and trigger save via IPC?
      }
    });
    
    // Command: Save All
    this.registerCommand({
      id: CommandIds.SAVE_ALL,
      title: 'Save All',
      category: 'File',
      showInPalette: true,
      handler: async () => {
        this.logger.info('[Command] Executing workbench.action.saveAll');
        this.logger.info(`[Command] Sending ${IpcChannels.WORKBENCH_REQUEST_SAVE_ALL} event to renderer.`);
        this.ipcService.send(IpcChannels.WORKBENCH_REQUEST_SAVE_ALL, {});
        this.logger.info('[Command] Signal workbench.action.saveAll sent.');
        return {success: true};
      }
    });
    
    // Command: Set Setting Value (Internal)
    this.registerCommand({
      id: CommandIds.CORE_SETTINGS_SET,
      title: 'Set Setting Value', // Internal command
      showInPalette: false,
      handler: async (args?: CommandArgs) => {
        const specificArgs = args as unknown as SettingsSetValueArgs;
        this.logger.info(`[Command ${CommandIds.CORE_SETTINGS_SET}] Executing with key: ${specificArgs.key}, value: ${specificArgs.value}`);
        // TODO: Implement proper type checking/guard for setting value based on key
        // Using unknown for type safety instead of any
        try {
          await this.settingsService.updateValue(specificArgs.key, specificArgs.value as JsonValue);
        } catch (error) {
          const meta: Record<string, unknown> = {value: specificArgs.value, error};
          this.logger.error(`[Command ${CommandIds.CORE_SETTINGS_SET}] Failed to set setting "${specificArgs.key}". Attempted value:`, meta);
        }
      }
    });
    
    // Command: Set Context Key Value (Internal)
    this.registerCommand({
      id: CommandIds.CORE_CONTEXT_SET,
      title: 'Set Context Key Value', // Internal command
      showInPalette: false,
      handler: (args?: CommandArgs) => {
        const specificArgs = args as unknown as ContextSetValueArgs;
        this.logger.info(`[Command ${CommandIds.CORE_CONTEXT_SET}] Executing with key: ${specificArgs.key}, value: ${specificArgs.value}`);
        this.contextService.setContext(specificArgs.key, specificArgs.value);
      }
    });
    
    this.logger.info('[CoreCommands] Core commands registered.');
  }
}
