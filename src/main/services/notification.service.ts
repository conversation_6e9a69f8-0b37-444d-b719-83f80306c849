import { LoggingServiceAPI } from './logging.service';
import { IpcChannels } from '@shared/constants/ipc-channels';
import { IpcServiceAPI } from './ipc.service';
import {BaseService, IBaseService} from "@services/base.service";

// API, предоставляемое NotificationService для расширений
export interface NotificationServiceAPI extends IBaseService  {
    showInfo(message: string): void;
    showWarning(message: string): void;
    showError(message: string | Error): void;
    // Возможно, добавить методы с кнопками действий?
}

export type NotificationType = 'info' | 'warning' | 'error' | 'success';

// Класс NotificationService
export class NotificationService extends BaseService implements NotificationServiceAPI {
  private readonly ipcService: IpcServiceAPI; // Add IpcService instance

  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, "NotificationService");
    this.ipcService = ipcService;
  }

    private sendNotification(type: NotificationType, message: string) {
      this.logger.info(`Attempting to send ${type} notification: ${message}`);

      // Use IpcService to send the notification
      try {
        this.logger.info(`Sending notification via IPC service: ${IpcChannels.WORKBENCH_SHOW_NOTIFICATION}`);
        this.ipcService.send(IpcChannels.WORKBENCH_SHOW_NOTIFICATION, { type, message });
      } catch (error) {
         const errorMessage = error instanceof Error ? error.message : String(error);
         this.logger.warn(`Failed to send notification via IpcService: ${errorMessage}. Logging locally.`);
         if (error instanceof Error) {
           this.logger.info('IpcService send error details:', error);
         }
      }
    }

    // Реализация методов интерфейса
    showInfo(message: string): void {
        this.sendNotification('info', message);
    }

    showWarning(message: string): void {
        this.sendNotification('warning', message);
    }

    showError(message: string | Error): void {
        const errorMessage = message instanceof Error ? message.message : message;
        this.sendNotification('error', errorMessage);
        if (message instanceof Error) {
            this.logger.error("Full error details:", message); // Use this.logger
        }
    }

    // Add dispose method
    public async dispose() {
      await super.dispose();
    }
}
