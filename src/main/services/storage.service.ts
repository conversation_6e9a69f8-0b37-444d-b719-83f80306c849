import Database from 'better-sqlite3';
import path from 'node:path';
import fs from 'node:fs';
import {app, BrowserWindow} from 'electron';
import {LoggingServiceAPI} from './logging.service';
import {IpcServiceAPI} from './ipc.service';
import {BaseService, IBaseService} from "@services/base.service";
import {JsonValue} from '@shared/types/common';

// --- Generic API Interface ---
// Определяем общий низкоуровневый API для работы с БД
export interface StorageServiceAPI extends IBaseService {
  /**
   * Executes a SQL statement that does not return data (INSERT, UPDATE, DELETE, CREATE, etc.).
   * @param sql The SQL statement string. Can contain placeholders (?).
   * @param params Parameters to bind to the placeholders.
   * @returns A promise resolving to the RunResult object from better-sqlite3.
   */
  run(sql: string, ...params: unknown[]): Promise<Database.RunResult>;
  
  /**
   * Executes a SQL statement that returns a single row.
   * @param sql The SQL statement string. Can contain placeholders (?).
   * @param params Parameters to bind to the placeholders.
   * @returns A promise resolving to the row object, or undefined if no row was found.
   */
  get<T = JsonValue>(sql: string, ...params: unknown[]): Promise<T | undefined>;
  
  /**
   * Executes a SQL statement that returns multiple rows.
   * @param sql The SQL statement string. Can contain placeholders (?).
   * @param params Parameters to bind to the placeholders.
   * @returns A promise resolving to an array of row objects.
   */
  all<T = JsonValue>(sql: string, ...params: unknown[]): Promise<T[]>;
  
  /**
   * Executes multiple SQL statements within a transaction.
   * @param callback A function containing the statements to execute transactionally.
   * @returns A promise resolving to the result of the callback function.
   */
  transaction<T>(callback: () => T): Promise<T>; // Убираем аргумент tx из колбэка
}

// --------------------


const dbPath = path.join(app.getPath('userData'), 'ai_books_data.sqlite');
// Logging dbPath moved to constructor

// sqliteVerbose function moved inside initializeDatabase where logger is available

class StorageError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'StorageError';
  }
}

export class StorageService extends BaseService implements StorageServiceAPI { // Implement Disposable
  private db: Database.Database | null = null;
  private readonly ipcService: IpcServiceAPI;
  
  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, 'StorageService');
    this.ipcService = ipcService; // Assign ipcService
    this.logger.info(`Database path: ${dbPath}`); // Log dbPath here
  }
  
  private ensureDatabase(): Database.Database {
    if (!this.db) {
      throw new StorageError('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }
  
  public async initialize(windowGetter: () => (BrowserWindow | null)) {
    await super.initialize(windowGetter);
    try {
      const dbDir = path.dirname(dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, {recursive: true});
        this.logger.info(`Created database directory: ${dbDir}`); // Use this.logger
      }
      this.db = new Database(dbPath); // Use the new verbose function
      this.logger.info('Database connection opened successfully.');
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('foreign_keys = ON');
      this.logger.info('WAL mode and foreign keys enabled.'); // Use this.logger
      // Создание/миграция схемы теперь будет ответственностью расширений или MigrationService
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to initialize database', error); // Use this.logger
      throw new Error(`Failed to initialize database: ${message}`);
    }
  }
  
  // Оборачиваем синхронные вызовы better-sqlite3 в Promise
  // (В реальном приложении лучше использовать worker thread или асинхронный драйвер)
  async run(sql: string, ...params: unknown[]): Promise<Database.RunResult> {
    return new Promise((resolve, reject) => {
      try {
        const db = this.ensureDatabase();
        const stmt = db.prepare(sql);
        const info = stmt.run(...params);
        resolve(info);
      } catch (error: unknown) {
        const message = error instanceof Error ? error.message : String(error);
        this.logger.error(`RUN Error: ${sql}`, {params, error}); // Use this.logger
        reject(new StorageError(`Failed to run statement: ${message}`));
      }
    });
  }
  
  async get<T = JsonValue>(sql: string, ...params: unknown[]): Promise<T | undefined> {
    return new Promise((resolve, reject) => {
      try {
        const db = this.ensureDatabase();
        const stmt = db.prepare(sql);
        const row = stmt.get(...params) as T | undefined;
        resolve(row);
      } catch (error: unknown) {
        const message = error instanceof Error ? error.message : String(error);
        this.logger.error(`GET Error: ${sql}`, {params, error}); // Use this.logger
        reject(new StorageError(`Failed to get row: ${message}`));
      }
    });
  }
  
  async all<T = JsonValue>(sql: string, ...params: unknown[]): Promise<T[]> {
    return new Promise((resolve, reject) => {
      try {
        const db = this.ensureDatabase();
        const stmt = db.prepare(sql);
        const rows = stmt.all(...params) as T[];
        resolve(rows);
      } catch (error: unknown) {
        const message = error instanceof Error ? error.message : String(error);
        this.logger.error(`ALL Error: ${sql}`, {params, error}); // Use this.logger
        reject(new StorageError(`Failed to get all rows: ${message}`));
      }
    });
  }
  
  async transaction<T>(callback: () => T): Promise<T> {
    return new Promise((resolve, reject) => {
      // Создаем транзакционную функцию
      const db = this.ensureDatabase();
      const txFunction = db.transaction(callback);
      try {
        const result = txFunction();
        resolve(result);
      } catch (error: unknown) {
        // better-sqlite3 автоматически откатывает транзакцию при ошибке
        const message = error instanceof Error ? error.message : String(error);
        reject(new StorageError(`Transaction failed: ${message}`));
      }
    });
  }
  
  async dispose(): Promise<void> { // Make async
    if (this.db) {
      try {
        // Attempt to checkpoint the WAL file to ensure data is written to the main DB file
        this.logger.info('Attempting WAL checkpoint...');
        this.db.pragma('wal_checkpoint(RESTART)'); // Use RESTART for more thorough checkpointing
        this.logger.info('WAL checkpoint successful.');
      } catch (checkpointError) {
        this.logger.error('Error during WAL checkpoint:', checkpointError);
        // Continue with closing even if checkpoint fails
      }
      
      try {
        this.db.close();
        this.logger.info('Database connection closed.');
      } catch (closeError) {
        this.logger.error('Error closing database connection:', closeError);
        // Rethrow or handle as appropriate
      }
    }
  }
}
