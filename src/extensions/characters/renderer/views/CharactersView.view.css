/* Use common explorer styles */
.container { /* Renamed */
    font-size: 0.9em;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box; /* Add box-sizing */
}

.header { /* Renamed */
    margin-bottom: 8px; /* Consistent margin */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0; /* Consistent padding */
    flex-shrink: 0; /* Prevent header from shrinking */
}

.header span { /* Style title */
    font-weight: bold;
    margin-left: 4px; /* Add some left margin */
}

.list { /* Renamed */
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex-grow: 1;
}

.listItem { /* Renamed */
    padding: 5px 8px; /* Consistent padding */
    cursor: pointer;
    border-radius: 3px;
    margin-bottom: 1px; /* Consistent margin */
    background-color: transparent;
    transition: background-color 0.1s ease-in-out;
    white-space: nowrap; /* Prevent wrapping */
    overflow: hidden; /* Hide overflow */
    text-overflow: ellipsis; /* Add ellipsis */
}

.listItem:hover {
     background-color: var(--app-list-hoverBackground); /* Use --app- variable */
}

/* Add active state if needed later */
/* .listItem.active {
    background-color: var(--app-list-activeSelectionBackground);
    color: var(--app-list-activeSelectionForeground, var(--app-foreground));
} */

.error { /* Renamed */
    color: var(--app-errorForeground); /* Use --app- variable */
    padding: 10px;
}

.loading { /* Renamed */
    padding: 10px;
    text-align: center;
    color: var(--app-descriptionForeground); /* Use --app- variable */
}

.noCharacters { /* Renamed */
    padding: 10px;
    font-style: italic;
    color: var(--app-descriptionForeground); /* Use --app- variable */
}

/* Button styles should ideally use a shared IconButton component or class */
.addButton { /* Renamed */
    /* Use styles consistent with other header buttons (like in BooksExplorerHeader) */
    /* Assuming an IconButton component or similar shared style exists */
    /* Placeholder basic style: */
    padding: 2px 4px;
    cursor: pointer;
    border: none;
    background: none;
    color: var(--app-foreground);
    opacity: 0.7;
    border-radius: 3px;
}

.addButton:hover {
     background-color: var(--app-list-hoverBackground); /* Consistent hover */
     opacity: 1;
}
