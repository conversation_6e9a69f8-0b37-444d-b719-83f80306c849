import React, {useCallback, useEffect} from "react";
import {ipcRendererService} from "@renderer/core/services/ipcRendererService";
import {CommandIds as CoreCommandIds} from "@shared/constants/command-ids";
import type {CommandsExecuteArgs, IpcErrorData, MenuShowContextMenuArgs,} from "@shared/types/ipc";
import {
  useCharacterList,
  useCharacterListError,
  useCharacterStore,
  useIsLoadingCharacterList,
} from "../state/characterStore";
// import { EXTENSION_ID } from "../../shared/constants/extension"; // Not used currently
import * as commands from "../../shared/constants/commands";
import {PROFILE_EDITOR} from "../../shared/constants/editors";
import styles from "./CharactersView.view.module.css";
import {IpcChannels} from "@shared/constants/ipc-channels";
// import { Icon } from "@renderer/components/Icon/Icon"; // Not used currently
import {CharactersExplorerHeader} from "../components/CharactersExplorerHeader"; // Import the new header

export const CharactersView: React.FC = () => {
  const characters = useCharacterList();
  const isLoading = useIsLoadingCharacterList();
  const error = useCharacterListError();
  const {loadCharacters} = useCharacterStore.getState();
  
  useEffect(() => {
    if (loadCharacters) {
      loadCharacters();
    }
  }, [loadCharacters]);
  
  const handleCreateCharacter = useCallback(() => {
    ipcRendererService
      .invoke(IpcChannels.COMMANDS_EXECUTE, {
        commandId: commands.CHARACTER_CREATE,
        args: {}
      })
      .catch((err: Error) => {
        console.error(
          `Error executing ${commands.CHARACTER_CREATE} command:`,
          err
        );
        alert(`Error creating character: ${err.message}`);
      });
  }, []);
  
  const handleCharacterClick = useCallback(
    async (characterId: string) => {
      console.log(`[CharactersView] handleCharacterClick for ${characterId}`);
      try {
        const character = characters.find((c) => c.id === characterId);
        const title = character?.name
          ? `Character: ${character.name}`
          : `Character: ${characterId}`;
        const editorType = PROFILE_EDITOR;
        
        const commandArgs: CommandsExecuteArgs = {
          commandId: CoreCommandIds.OPEN_EDITOR,
          args: {
            editorType: editorType,
            dataId: characterId,
            title: title, // Use resolved title
          },
          
        };
        await ipcRendererService.invoke(
          IpcChannels.COMMANDS_EXECUTE,
          commandArgs
        );
      } catch (err: unknown) {
        const errorData = err as IpcErrorData;
        console.error(
          `Error opening profile editor for ${characterId}:`,
          errorData?.message ?? err
        );
        alert(
          `Error opening profile: ${errorData?.message ?? "Unknown error"}`
        );
      }
    },
    [characters]
  );
  
  const handleContextMenu = useCallback(
    async (event: React.MouseEvent, characterId: string) => {
      event.preventDefault();
      event.stopPropagation();
      const contextIdentifier = "view/character/item";
      const menuArgs = {itemId: characterId};
      console.debug(`Context menu requested for character ${characterId}`);
      try {
        const args: MenuShowContextMenuArgs = {contextIdentifier, menuArgs};
        await ipcRendererService.invoke(
          IpcChannels.MENU_SHOW_CONTEXT_MENU,
          args
        );
      } catch (err: unknown) {
        const errorData = err as IpcErrorData;
        console.error("Error showing context menu:", errorData?.message ?? err);
      }
    },
    []
  );
  
  let content: React.ReactNode;
  
  if (isLoading) {
    content = <div className={styles.loading}>Loading characters...</div>;
  } else if (error) {
    content = <div className={styles.error}>Error: {error.message}</div>;
  } else if (characters.length === 0) {
    content = <div className={styles.noCharacters}>No characters yet.</div>;
  } else {
    content = (
      <ul className={styles.list}>
        {characters.map((char) => (
          <li
            key={char.id}
            className={styles.listItem} // Use new class name
            onClick={() => handleCharacterClick(char.id)}
            onContextMenu={(e) => handleContextMenu(e, char.id)}
          >
            {/* Optional: Add character icon */}
            {/* <Icon name="User" size={14} style={{ marginRight: '5px', verticalAlign: 'middle' }} /> */}
            {char.name || "Untitled Character"}
          </li>
        ))}
      </ul>
    );
  }
  
  return (
    <div className={styles.container}>
      <CharactersExplorerHeader onAddCharacter={handleCreateCharacter}/>
      {content}
    </div>
  );
};

export default CharactersView;
