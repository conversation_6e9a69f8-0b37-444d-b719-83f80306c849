import React from "react";
import styles from "./CharacterProfileMetadata.module.css"; // Import CSS module

interface CharacterProfileMetadataProps {
  createdAt: number | undefined;
  updatedAt: number | undefined;
}

const formatDate = (timestamp: number | undefined): string => {
  if (!timestamp) return "N/A";
  try {
    return new Date(timestamp).toLocaleString();
  } catch {
    return "Invalid Date";
  }
};

export const CharacterProfileMetadata: React.FC<
  CharacterProfileMetadataProps
> = ({ createdAt, updatedAt }) => {
  return (
    <div className={styles.container}>
      {" "}
      {/* Use className */}
      <div className={styles.item}>Created: {formatDate(createdAt)}</div>{" "}
      {/* Use className */}
      <div className={styles.item}>
        Last Updated: {formatDate(updatedAt)}
      </div>{" "}
      {/* Use className */}
    </div>
  );
};
