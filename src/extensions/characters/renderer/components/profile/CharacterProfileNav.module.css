/* Styles adapted from SettingsEditor.css */
.sidebar { /* Renamed from .nav */
  width: 220px; /* Match settings sidebar width */
  flex-shrink: 0;
  background-color: var(--app-sideBar-background);
  padding: 15px 0px 15px 15px; /* Match settings padding (no right padding on container) */
  box-sizing: border-box;
  border-right: 1px solid var(--app-border);
  display: flex;
  flex-direction: column;
  height: 100%; /* Ensure sidebar takes full height */
}

/* Optional: Add search input styles later if needed */
.searchInput {
  width: calc(100% - 15px); /* Match settings search input width relative to padding */
  padding: 8px 10px;
  box-sizing: border-box;
  background-color: var(--app-input-background);
  color: var(--app-input-foreground);
  border: 1px solid var(--app-border);
  border-radius: 3px;
  outline: none;
  margin-bottom: 15px;
  margin-right: 15px; /* Match settings search input margin */
}
.searchInput:focus {
   border-color: var(--app-button-background);
}


.categories { /* Renamed from .navList */
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  padding-right: 15px; /* Add padding for scrollbar */
  flex-grow: 1; /* Allow list to take remaining space */
}

.categories li { /* Style list items directly */
  padding: 8px 10px;
  cursor: pointer;
  border-radius: 3px;
  margin-bottom: 2px;
  color: var(--app-tab-inactiveForeground);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.categories li:hover {
  background-color: var(--app-list-hoverBackground);
  color: var(--app-foreground);
}

.categories li button { /* Style the button inside li */
  display: block;
  width: 100%;
  padding: 0; /* Remove padding from button, apply to li */
  text-align: left;
  border: none;
  background: none;
  color: inherit; /* Inherit color from li */
  cursor: pointer;
  font-size: inherit; /* Inherit font size */
}

/* Style the active list item */
.categories li.active {
  background-color: var(--app-list-activeSelectionBackground);
  color: var(--app-button-foreground);
}

/* Ensure button inside active li also gets correct color */
.categories li.active button {
    color: inherit;
    font-weight: inherit;
}
