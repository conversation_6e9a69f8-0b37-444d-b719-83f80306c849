import React, { useState, useEffect, useCallback } from "react";
import type {
  CharacterEvent,
  CharacterEventType,
  CreateCharacterEventData,
  UpdateCharacterEventData,
} from "../../../shared/types";
import type { IpcErrorData } from "@shared/types/ipc";
import * as ipcChannels from "../../../shared/constants/ipc-channels";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import styles from "./CharacterProfileEvents.module.css";

interface CharacterProfileEventsProps {
  characterId: string;
  isSaving: boolean;
}

interface EventFormData {
  eventType: CharacterEventType;
  description: string;
  timelinePosition: number;
  impact?: number;
  relatedSceneId?: string;
}

const eventTypeOptions: { value: CharacterEventType; label: string }[] = [
  { value: "Personality", label: "Personality Change" },
  { value: "Goals", label: "Goals & Motivations" },
  { value: "Relationships", label: "Relationships" },
  { value: "Conflicts", label: "Conflicts" },
];

const impactOptions = [
  { value: 1, label: "Minor" },
  { value: 3, label: "Moderate" },
  { value: 7, label: "Major" },
  { value: 10, label: "Transformative" },
];

export const CharacterProfileEvents: React.FC<CharacterProfileEventsProps> = ({
  characterId,
  isSaving,
}) => {
  const [events, setEvents] = useState<CharacterEvent[]>([]);
  const [isLoadingEvents, setIsLoadingEvents] = useState(true);
  const [eventsError, setEventsError] = useState<IpcErrorData | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingEvent, setEditingEvent] = useState<CharacterEvent | null>(null);
  const [formData, setFormData] = useState<EventFormData>({
    eventType: "Personality",
    description: "",
    timelinePosition: 50,
    impact: 3,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load events
  const loadEvents = useCallback(async () => {
    try {
      setIsLoadingEvents(true);
      setEventsError(null);

      const result = await ipcRendererService.invoke<CharacterEvent[]>(
        ipcChannels.GET_CHARACTER_EVENTS,
        { characterId }
      );

      if (Array.isArray(result)) {
        setEvents(result);
      } else {
        setEventsError(result as IpcErrorData);
      }
    } catch (_error) {
      setEventsError({
        code: "ipc_call_failed",
        message: "Failed to load character events",
      });
    } finally {
      setIsLoadingEvents(false);
    }
  }, [characterId]);

  useEffect(() => {
    loadEvents();
  }, [loadEvents]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting || !formData.description.trim()) return;

    setIsSubmitting(true);
    try {
      if (editingEvent) {
        // Update existing event
        const updateData: UpdateCharacterEventData = {
          eventType: formData.eventType,
          description: formData.description.trim(),
          timelinePosition: formData.timelinePosition,
          impact: formData.impact,
          relatedSceneId: formData.relatedSceneId,
        };

        const result = await ipcRendererService.invoke<{ success: boolean }>(
          ipcChannels.UPDATE_CHARACTER_EVENT,
          { id: editingEvent.id, data: updateData }
        );

        if (result?.success) {
          await loadEvents();
          resetForm();
        } else {
          setEventsError({
            code: "update_failed",
            message: "Failed to update event",
          });
        }
      } else {
        // Create new event
        const createData: CreateCharacterEventData = {
          characterId,
          bookId: "current-book", // TODO: Get actual bookId from context
          eventType: formData.eventType,
          description: formData.description.trim(),
          timelinePosition: formData.timelinePosition,
          impact: formData.impact,
          relatedSceneId: formData.relatedSceneId,
        };

        const result = await ipcRendererService.invoke<CharacterEvent>(
          ipcChannels.CREATE_CHARACTER_EVENT,
          createData
        );

        if (result && "id" in result) {
          await loadEvents();
          resetForm();
        } else {
          setEventsError(result as IpcErrorData);
        }
      }
    } catch (_error) {
      setEventsError({
        code: "ipc_call_failed",
        message: "Failed to save event",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle event deletion
  const handleDelete = async (eventId: string) => {
    if (!confirm("Are you sure you want to delete this event?")) return;

    try {
      const result = await ipcRendererService.invoke<{ success: boolean }>(
        ipcChannels.DELETE_CHARACTER_EVENT,
        { id: eventId }
      );

      if (result?.success) {
        await loadEvents();
      } else {
        setEventsError({
          code: "delete_failed",
          message: "Failed to delete event",
        });
      }
    } catch (_error) {
      setEventsError({
        code: "ipc_call_failed",
        message: "Failed to delete event",
      });
    }
  };

  // Form helpers
  const resetForm = () => {
    setFormData({
      eventType: "Personality",
      description: "",
      timelinePosition: 50,
      impact: 3,
    });
    setShowAddForm(false);
    setEditingEvent(null);
  };

  const startEdit = (event: CharacterEvent) => {
    setEditingEvent(event);
    setFormData({
      eventType: event.eventType,
      description: event.description,
      timelinePosition: event.timelinePosition,
      impact: event.impact || 3,
      relatedSceneId: event.relatedSceneId,
    });
    setShowAddForm(true);
  };

  const startAdd = () => {
    setEditingEvent(null);
    setFormData({
      eventType: "Personality",
      description: "",
      timelinePosition: 50,
      impact: 3,
    });
    setShowAddForm(true);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getImpactLabel = (impact?: number) => {
    const option = impactOptions.find((opt) => opt.value === impact);
    return option?.label || "Unknown";
  };

  if (isLoadingEvents) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading events...</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3 className={styles.title}>Character Events</h3>
        <button
          type="button"
          onClick={startAdd}
          disabled={showAddForm || isSaving}
          className={styles.addButton}
        >
          Add Event
        </button>
      </div>

      {eventsError && (
        <div className={styles.error}>Error: {eventsError.message}</div>
      )}

      {showAddForm && (
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formHeader}>
            <h4 className={styles.formTitle}>
              {editingEvent ? "Edit Event" : "Add New Event"}
            </h4>
            <button
              type="button"
              onClick={resetForm}
              className={styles.cancelButton}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="eventType" className={styles.label}>
              Event Type
            </label>
            <select
              id="eventType"
              value={formData.eventType}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  eventType: e.target.value as CharacterEventType,
                }))
              }
              disabled={isSubmitting}
              className={styles.input}
              required
            >
              {eventTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description" className={styles.label}>
              Description
            </label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              disabled={isSubmitting}
              placeholder="Describe what happened to the character..."
              className={styles.textarea}
              rows={4}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="timelinePosition" className={styles.label}>
              Timeline Position: {formData.timelinePosition}%
            </label>
            <input
              id="timelinePosition"
              type="range"
              min="0"
              max="100"
              value={formData.timelinePosition}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  timelinePosition: parseInt(e.target.value),
                }))
              }
              disabled={isSubmitting}
              className={styles.input}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="impact" className={styles.label}>
              Impact Level
            </label>
            <select
              id="impact"
              value={formData.impact || 3}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  impact: parseInt(e.target.value),
                }))
              }
              disabled={isSubmitting}
              className={styles.input}
            >
              {impactOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.formActions}>
            <button
              type="submit"
              disabled={isSubmitting || !formData.description.trim()}
              className={styles.submitButton}
            >
              {isSubmitting
                ? "Saving..."
                : editingEvent
                ? "Update Event"
                : "Add Event"}
            </button>
          </div>
        </form>
      )}

      <div className={styles.eventsList}>
        {events.length === 0 ? (
          <div className={styles.emptyState}>
            <p>No events recorded yet.</p>
            <p>
              Add events to track key moments in this character's development
              journey.
            </p>
          </div>
        ) : (
          events
            .sort((a, b) => a.timelinePosition - b.timelinePosition)
            .map((event) => (
              <div key={event.id} className={styles.eventCard}>
                <div className={styles.eventHeader}>
                  <h4 className={styles.eventName}>{event.eventType}</h4>
                  <div className={styles.eventActions}>
                    <button
                      type="button"
                      onClick={() => startEdit(event)}
                      disabled={showAddForm || isSaving}
                      className={styles.editButton}
                      title="Edit event"
                    >
                      Edit
                    </button>
                    <button
                      type="button"
                      onClick={() => handleDelete(event.id)}
                      disabled={showAddForm || isSaving}
                      className={styles.deleteButton}
                      title="Delete event"
                    >
                      Delete
                    </button>
                  </div>
                </div>
                <div className={styles.eventValue}>{event.description}</div>
                <div className={styles.eventMeta}>
                  Timeline: {event.timelinePosition}% | Impact:{" "}
                  {getImpactLabel(event.impact)} | Created{" "}
                  {formatDate(event.createdAt)}
                </div>
              </div>
            ))
        )}
      </div>
    </div>
  );
};

export default CharacterProfileEvents;
