.label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: var(--app-foreground); /* Use theme variable */
}

.textarea {
  width: 100%;
  padding: 8px;
  margin-bottom: 15px;
  min-height: 150px;
  box-sizing: border-box;
  resize: vertical;
  background-color: var(--app-input-background); /* Use theme variable */
  color: var(--app-input-foreground); /* Use theme variable */
  border: 1px solid var(--app-border); /* Use theme variable */
  border-radius: 3px;
  outline: none;
}

.textarea:focus {
  border-color: var(--app-button-background);
}

.textarea:disabled {
  opacity: 0.6;
  background-color: var(--app-input-background); /* Keep background consistent */
}
