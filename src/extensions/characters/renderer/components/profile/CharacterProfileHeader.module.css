.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--app-border); /* Use theme variable */
  padding-bottom: 10px;
}

.input {
  flex-grow: 1;
  font-size: 1.5em;
  font-weight: bold;
  border: none;
  background: transparent;
  color: var(--app-foreground); /* Use theme variable */
  margin-right: 15px;
  padding: 5px;
  outline: none; /* Remove default outline */
}

.input:focus {
  box-shadow: 0 0 0 1px var(--app-button-background);
}

.button {
  padding: 8px 12px;
  cursor: pointer;
  background-color: var(--app-button-background); /* Use theme variable */
  color: var(--app-button-foreground); /* Use theme variable */
  border: none;
  border-radius: 3px;
}

.button:hover {
  background-color: var(--app-button-hoverBackground); /* Use theme variable */
}

.button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
