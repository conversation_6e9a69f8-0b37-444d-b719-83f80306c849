import React, { useState, useEffect, useCallback } from "react";
import type { CharacterAttribute } from "../../../shared/types";
import type { IpcErrorData } from "@shared/types/ipc";
import * as ipcChannels from "../../../shared/constants/ipc-channels";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import styles from "./CharacterProfileAttributes.module.css";

interface CharacterProfileAttributesProps {
  characterId: string;
  isSaving: boolean;
}

interface AttributeFormData {
  attributeKey: string;
  attributeValue: string;
}

export const CharacterProfileAttributes: React.FC<
  CharacterProfileAttributesProps
> = ({ characterId, isSaving }) => {
  const [attributes, setAttributes] = useState<CharacterAttribute[]>([]);
  const [isLoadingAttributes, setIsLoadingAttributes] = useState(true);
  const [attributesError, setAttributesError] = useState<IpcErrorData | null>(
    null
  );
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAttribute, setEditingAttribute] =
    useState<CharacterAttribute | null>(null);
  const [formData, setFormData] = useState<AttributeFormData>({
    attributeKey: "",
    attributeValue: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load attributes
  const loadAttributes = useCallback(async () => {
    try {
      setIsLoadingAttributes(true);
      setAttributesError(null);

      const result = await ipcRendererService.invoke<CharacterAttribute[]>(
        ipcChannels.GET_CHARACTER_ATTRIBUTES,
        { characterId }
      );

      if (Array.isArray(result)) {
        setAttributes(result);
      } else {
        setAttributesError(result as IpcErrorData);
      }
    } catch (_error) {
      setAttributesError({
        code: "ipc_call_failed",
        message: "Failed to load character attributes",
      });
    } finally {
      setIsLoadingAttributes(false);
    }
  }, [characterId]);

  useEffect(() => {
    loadAttributes();
  }, [loadAttributes]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (
      isSubmitting ||
      !formData.attributeKey.trim() ||
      !formData.attributeValue.trim()
    )
      return;

    setIsSubmitting(true);
    try {
      if (editingAttribute) {
        // Update existing attribute
        const result = await ipcRendererService.invoke<{ success: boolean }>(
          ipcChannels.UPDATE_CHARACTER_ATTRIBUTE,
          {
            id: editingAttribute.id,
            attributeValue: formData.attributeValue.trim(),
          }
        );

        if (result?.success) {
          await loadAttributes();
          resetForm();
        } else {
          setAttributesError({
            code: "update_failed",
            message: "Failed to update attribute",
          });
        }
      } else {
        // Create new attribute
        const result = await ipcRendererService.invoke<CharacterAttribute>(
          ipcChannels.CREATE_CHARACTER_ATTRIBUTE,
          {
            characterId,
            attributeKey: formData.attributeKey.trim(),
            attributeValue: formData.attributeValue.trim(),
          }
        );

        if (result && "id" in result) {
          await loadAttributes();
          resetForm();
        } else {
          setAttributesError(result as IpcErrorData);
        }
      }
    } catch (_error) {
      setAttributesError({
        code: "ipc_call_failed",
        message: "Failed to save attribute",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle attribute deletion
  const handleDelete = async (attributeId: string) => {
    if (!confirm("Are you sure you want to delete this attribute?")) return;

    try {
      const result = await ipcRendererService.invoke<{ success: boolean }>(
        ipcChannels.DELETE_CHARACTER_ATTRIBUTE,
        { id: attributeId }
      );

      if (result?.success) {
        await loadAttributes();
      } else {
        setAttributesError({
          code: "delete_failed",
          message: "Failed to delete attribute",
        });
      }
    } catch (_error) {
      setAttributesError({
        code: "ipc_call_failed",
        message: "Failed to delete attribute",
      });
    }
  };

  // Form helpers
  const resetForm = () => {
    setFormData({
      attributeKey: "",
      attributeValue: "",
    });
    setShowAddForm(false);
    setEditingAttribute(null);
  };

  const startEdit = (attribute: CharacterAttribute) => {
    setEditingAttribute(attribute);
    setFormData({
      attributeKey: attribute.attributeKey,
      attributeValue: attribute.attributeValue || "",
    });
    setShowAddForm(true);
  };

  const startAdd = () => {
    setEditingAttribute(null);
    setFormData({
      attributeKey: "",
      attributeValue: "",
    });
    setShowAddForm(true);
  };

  if (isLoadingAttributes) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>Loading attributes...</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3 className={styles.title}>Character Attributes</h3>
        <button
          type="button"
          onClick={startAdd}
          disabled={showAddForm || isSaving}
          className={styles.addButton}
        >
          Add Attribute
        </button>
      </div>

      {attributesError && (
        <div className={styles.error}>Error: {attributesError.message}</div>
      )}

      {showAddForm && (
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formHeader}>
            <h4 className={styles.formTitle}>
              {editingAttribute ? "Edit Attribute" : "Add New Attribute"}
            </h4>
            <button
              type="button"
              onClick={resetForm}
              className={styles.cancelButton}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="attributeKey" className={styles.label}>
              Attribute Name
            </label>
            <input
              id="attributeKey"
              type="text"
              value={formData.attributeKey}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  attributeKey: e.target.value,
                }))
              }
              disabled={isSubmitting || !!editingAttribute} // Disable when editing
              placeholder="e.g., Eye Color, Fighting Style, Background"
              className={styles.input}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="attributeValue" className={styles.label}>
              Value
            </label>
            <textarea
              id="attributeValue"
              value={formData.attributeValue}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  attributeValue: e.target.value,
                }))
              }
              disabled={isSubmitting}
              placeholder="Describe this character attribute..."
              className={styles.textarea}
              rows={3}
              required
            />
          </div>

          <div className={styles.formActions}>
            <button
              type="submit"
              disabled={
                isSubmitting ||
                !formData.attributeKey.trim() ||
                !formData.attributeValue.trim()
              }
              className={styles.submitButton}
            >
              {isSubmitting
                ? "Saving..."
                : editingAttribute
                ? "Update Attribute"
                : "Add Attribute"}
            </button>
          </div>
        </form>
      )}

      <div className={styles.attributesList}>
        {attributes.length === 0 ? (
          <div className={styles.emptyState}>
            <p>No attributes defined yet.</p>
            <p>
              Add attributes to describe key characteristics, abilities, or
              traits of this character.
            </p>
          </div>
        ) : (
          attributes.map((attribute) => (
            <div key={attribute.id} className={styles.attributeCard}>
              <div className={styles.attributeHeader}>
                <h4 className={styles.attributeName}>
                  {attribute.attributeKey}
                </h4>
                <div className={styles.attributeActions}>
                  <button
                    type="button"
                    onClick={() => startEdit(attribute)}
                    disabled={showAddForm || isSaving}
                    className={styles.editButton}
                    title="Edit attribute"
                  >
                    Edit
                  </button>
                  <button
                    type="button"
                    onClick={() => handleDelete(attribute.id)}
                    disabled={showAddForm || isSaving}
                    className={styles.deleteButton}
                    title="Delete attribute"
                  >
                    Delete
                  </button>
                </div>
              </div>
              <div className={styles.attributeValue}>
                {attribute.attributeValue || <em>No value specified</em>}
              </div>
              <div className={styles.attributeMeta}>
                Added {new Date(attribute.createdAt).toLocaleDateString()}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
