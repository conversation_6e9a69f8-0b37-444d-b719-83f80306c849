import React from "react";
import styles from "./CharacterProfileNav.module.css";
import cn from "classnames"; // For conditional class names

// Define the sections available in the profile editor
export type ProfileSection = "details" | "attributes" | "events" | "metadata"; // Add more as needed

interface CharacterProfileNavProps {
  activeSection: ProfileSection;
  onSectionSelect: (section: ProfileSection) => void;
}

const sections: { id: ProfileSection; label: string; icon?: string }[] = [
  { id: "details", label: "Details" /* icon: 'FileText' */ },
  { id: "attributes", label: "Attributes" /* icon: 'List' */ },
  { id: "events", label: "Events" /* icon: 'Calendar' */ },
  // Add 'metadata' or other sections here if needed in nav
];

export const CharacterProfileNav: React.FC<CharacterProfileNavProps> = ({
  activeSection,
  onSectionSelect,
}) => {
  // TODO: Implement search state and logic
  const [searchTerm, setSearchTerm] = React.useState("");

  return (
    <nav className={styles.sidebar}>
      <input
        type="text"
        placeholder="Search profile sections..." // Placeholder text
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className={styles.searchInput} // Apply styles
      />
      <ul className={styles.categories}>
        {sections.map((section) => (
          // Apply active class to li, button remains plain
          <li
            key={section.id}
            className={cn({ [styles.active]: activeSection === section.id })}
            onClick={() => onSectionSelect(section.id)} // Click on li triggers select
          >
            <button>
              {" "}
              {/* Button is now just for semantics/accessibility */}
              {section.label}
            </button>
          </li>
        ))}
      </ul>
    </nav>
  );
};
