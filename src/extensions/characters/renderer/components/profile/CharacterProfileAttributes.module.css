/* Character Attributes Component Styles */

.container {
  padding: 20px;
  max-width: 800px;
}

.loading {
  text-align: center;
  color: var(--app-tab-inactiveForeground);
  padding: 40px;
  font-style: italic;
}

.error {
  background-color: var(--app-input-background);
  color: var(--app-errorForeground);
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid var(--app-border);
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--app-border);
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--app-foreground);
}

.addButton {
  background-color: var(--app-button-background);
  color: var(--app-button-foreground);
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.addButton:hover:not(:disabled) {
  background-color: var(--app-button-hoverBackground);
}

.addButton:disabled {
  background-color: var(--app-input-background);
  color: var(--app-tab-inactiveForeground);
  cursor: not-allowed;
}

/* Form */
.form {
  background-color: var(--app-sideBar-background);
  border: 1px solid var(--app-border);
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

.formHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.formTitle {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--app-foreground);
}

.cancelButton {
  background: none;
  border: 1px solid var(--app-border);
  color: var(--app-foreground);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.cancelButton:hover:not(:disabled) {
  background-color: var(--app-list-hoverBackground);
}

.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.formGroup {
  margin-bottom: 16px;
}

.label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--app-foreground);
  font-size: 14px;
}

.input,
.textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--app-border);
  border-radius: 4px;
  background-color: var(--app-input-background);
  color: var(--app-input-foreground);
  font-family: inherit;
  font-size: 14px;
  transition: border-color 0.2s;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: var(--app-button-background);
  box-shadow: 0 0 0 1px var(--app-button-background);
}

.input:disabled,
.textarea:disabled {
  background-color: var(--app-input-background);
  color: var(--app-tab-inactiveForeground);
  cursor: not-allowed;
  opacity: 0.6;
}

.textarea {
  resize: vertical;
  min-height: 60px;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.submitButton {
  background-color: var(--app-button-background);
  color: var(--app-button-foreground);
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.submitButton:hover:not(:disabled) {
  background-color: var(--app-button-hoverBackground);
}

.submitButton:disabled {
  background-color: var(--app-input-background);
  color: var(--app-tab-inactiveForeground);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Attributes List */
.attributesList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: var(--app-tab-inactiveForeground);
}

.emptyState p {
  margin: 8px 0;
}

.emptyState p:first-child {
  font-weight: 500;
  font-size: 16px;
}

/* Attribute Card */
.attributeCard {
  background-color: var(--app-sideBar-background);
  border: 1px solid var(--app-border);
  border-radius: 6px;
  padding: 16px;
  transition: background-color 0.2s;
}

.attributeCard:hover {
  background-color: var(--app-list-hoverBackground);
}

.attributeHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.attributeName {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--app-foreground);
  flex: 1;
}

.attributeActions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.editButton,
.deleteButton {
  background: none;
  border: 1px solid var(--app-border);
  color: var(--app-foreground);
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.editButton:hover:not(:disabled) {
  background-color: var(--app-list-hoverBackground);
  border-color: var(--app-button-background);
  color: var(--app-button-foreground);
}

.deleteButton:hover:not(:disabled) {
  background-color: var(--app-list-hoverBackground);
  border-color: var(--app-errorForeground);
  color: var(--app-errorForeground);
}

.editButton:disabled,
.deleteButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.attributeValue {
  color: var(--app-foreground);
  line-height: 1.5;
  margin-bottom: 8px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.attributeValue em {
  color: var(--app-tab-inactiveForeground);
  font-style: italic;
}

.attributeMeta {
  font-size: 12px;
  color: var(--app-tab-inactiveForeground);
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--app-border);
}
