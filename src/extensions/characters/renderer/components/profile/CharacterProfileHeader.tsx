import React from "react";
import styles from "./CharacterProfileHeader.module.css"; // Import CSS module

interface CharacterProfileHeaderProps {
  name: string;
  isSaving: boolean;
  onNameChange: (newName: string) => void;
  onSave: () => void;
  // TODO: Add isDirty state later
}

export const CharacterProfileHeader: React.FC<CharacterProfileHeaderProps> = ({
  name,
  isSaving,
  onNameChange,
  onSave,
}) => {
  return (
    <div className={styles.header}>
      <input
        type="text"
        value={name}
        onChange={(e) => onNameChange(e.target.value)}
        className={styles.input}
        placeholder="Character Name"
        disabled={isSaving}
      />
      <button onClick={onSave} disabled={isSaving} className={styles.button}>
        {isSaving ? "Saving..." : "Save Profile"}
      </button>
    </div>
  );
}; // Add semicolon
