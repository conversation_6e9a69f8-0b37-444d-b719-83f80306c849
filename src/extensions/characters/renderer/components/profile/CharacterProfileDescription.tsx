import React from "react";
import styles from "./CharacterProfileDescription.module.css"; // Import CSS module

interface CharacterProfileDescriptionProps {
  description: string;
  isSaving: boolean;
  onDescriptionChange: (newDescription: string) => void;
}

export const CharacterProfileDescription: React.FC<
  CharacterProfileDescriptionProps
> = ({ description, isSaving, onDescriptionChange }) => {
  return (
    <div>
      <label htmlFor="char-desc" className={styles.label}>
        Description:
      </label>
      <textarea
        id="char-desc"
        value={description}
        onChange={(e) => onDescriptionChange(e.target.value)}
        className={styles.textarea}
        placeholder="Enter character description, traits, background..."
        disabled={isSaving}
      />
    </div>
  );
};
