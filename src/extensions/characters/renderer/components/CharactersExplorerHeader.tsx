import React from "react";
import styles from "./CharactersExplorerHeader.module.css";
import {Icon} from "@renderer/components/Icon/Icon";

interface CharactersExplorerHeaderProps {
  onAddCharacter: () => void;
}

export const CharactersExplorerHeader: React.FC<
  CharactersExplorerHeaderProps
> = ({onAddCharacter}) => {
  return (
    <div className={styles.header}>
      <span className={styles.title}>Characters</span>
      <button
        type={"button"}
        className={styles.addButton}
        onClick={onAddCharacter}
        title="Create New Character"
      >
        <Icon name="Plus" size={16}/>
      </button>
    </div>
  );
};
