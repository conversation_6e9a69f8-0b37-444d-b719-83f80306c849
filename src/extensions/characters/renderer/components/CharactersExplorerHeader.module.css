/* Styles adapted from BooksExplorerHeader or common explorer styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0; /* Add bottom padding */
  flex-shrink: 0;
  border-bottom: 1px solid var(--app-border); /* Add separator line */
  color: var(--app-foreground);
}

.title {
  font-weight: bold;
  margin-left: 4px; /* Match CharactersView title margin */
  text-transform: uppercase; /* Optional: Match BooksExplorer style */
  font-size: 0.9em; /* Optional: Match BooksExplorer style */
}

.addButton {
  /* Use styles consistent with other header buttons */
  padding: 2px 4px;
  cursor: pointer;
  border: none;
  background: none;
  color: var(--app-foreground);
  opacity: 0.7;
  border-radius: 3px;
}

.addButton:hover {
  background-color: var(--app-list-hoverBackground);
}
