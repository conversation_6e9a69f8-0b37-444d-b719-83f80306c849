/* Use styles similar to SettingsEditor */
.layout { /* Renamed from editorLayout */
  display: flex;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  background-color: var(--app-editor-background);
  color: var(--app-foreground);
}

.contentArea { /* Renamed from settingsContent */
  flex-grow: 1;
  padding: 20px 30px; /* Match settings padding */
  overflow-y: auto;
  box-sizing: border-box;
}

/* Style for the H1 inside contentArea (used by CharacterProfileHeader now) */
.contentArea h1 { /* Target h1 specifically if needed, or style header component */
  margin-top: 0;
  margin-bottom: 25px;
  border-bottom: 1px solid var(--app-border);
  padding-bottom: 15px;
  font-weight: 600;
  font-size: 1.4em;
}


.loading,
.error {
  padding: 20px;
  color: var(--app-tab-inactiveForeground);
}

.error {
  color: var(--app-errorForeground);
}

/* Add more specific styles if needed */
