/* eslint-disable @eslint-react/hooks-extra/no-direct-set-state-in-use-effect */
import React, { useState, useEffect, useCallback } from "react";
import type { Character } from "../../shared/types";
import type { IpcErrorData } from "@shared/types/ipc";
import * as ipcChannels from "../../shared/constants/ipc-channels";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import { rendererEventBus } from "@renderer/core/services/RendererEventBus"; // Import event bus
import styles from "./CharacterProfileEditor.module.css";
import { CharacterProfileHeader } from "../components/profile/CharacterProfileHeader";
import { CharacterProfileDescription } from "../components/profile/CharacterProfileDescription";
import { CharacterProfileAttributes } from "../components/profile/CharacterProfileAttributes";
import { CharacterProfileEvents } from "../components/profile/CharacterProfileEvents";
import { CharacterProfileMetadata } from "../components/profile/CharacterProfileMetadata";
import {
  CharacterProfileNav,
  ProfileSection,
} from "../components/profile/CharacterProfileNav";
import { EXTENSION_ID } from "../../shared/constants/extension"; // Import EXTENSION_ID

interface CharacterProfileEditorProps {
  dataId: string;
}

export const CharacterProfileEditor: React.FC<CharacterProfileEditorProps> = ({
  dataId,
}) => {
  const [profile, setProfile] = useState<Character | null>(null);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<IpcErrorData | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [activeSection, setActiveSection] = useState<ProfileSection>("details");
  const [isDirty, setIsDirty] = useState(false); // Internal dirty state

  const tabId = `${EXTENSION_ID}:profile-editor:${dataId}`; // Construct the tab ID

  // Load profile data
  useEffect(() => {
    const loadProfile = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await ipcRendererService.invoke<Character | null>(
          ipcChannels.GET_CHARACTER,
          { id: dataId }
        );

        if (result) {
          setProfile(result);
          setName(result.name || "");
          setDescription(result.description || "");
          setIsDirty(false); // Reset internal dirty state on load
        } else {
          setError({
            code: "not_found",
            message: "Character profile not found.",
          });
          setProfile(null);
        }
      } catch (err: unknown) {
        const ipcError = err as IpcErrorData;
        setError(
          ipcError ?? {
            code: "ipc_call_failed",
            message: "Failed to load profile",
          }
        );
        setProfile(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, [dataId]);

  // Save handler
  const handleSave = useCallback(async () => {
    if (!profile || isSaving) return;
    if (!isDirty) {
      console.log("No changes to save.");
      return;
    }
    setIsSaving(true);
    setError(null);
    const updatedData: Partial<Omit<Character, "id" | "createdAt">> = {};
    if (name !== profile.name) updatedData.name = name;
    const newDescription = description.trim() === "" ? null : description;
    if (newDescription !== (profile.description ?? null))
      updatedData.description =
        newDescription === null ? undefined : newDescription;

    if (Object.keys(updatedData).length === 0) {
      console.log("No actual data changes detected to save.");
      setIsSaving(false);
      setIsDirty(false); // Ensure dirty is false
      rendererEventBus.emit("editorDirtyStateChanged", {
        tabId,
        isDirty: false,
      }); // Emit clean state
      return;
    }

    try {
      const result = await ipcRendererService.invoke<{ success: boolean }>(
        ipcChannels.UPDATE_CHARACTER,
        { id: profile.id, data: updatedData }
      );
      if (result?.success) {
        setProfile((prev) =>
          prev ? { ...prev, ...updatedData, updatedAt: Date.now() } : null
        );
        setIsDirty(false); // Reset internal dirty state
        rendererEventBus.emit("editorDirtyStateChanged", {
          tabId,
          isDirty: false,
        }); // Emit clean state
        console.log("Profile saved successfully");
      }
    } catch (err) {
      const error = err as { message?: string };
      setError({
        code: "ipc_call_failed",
        message: error.message || "Failed to save profile",
      });
    } finally {
      setIsSaving(false);
    }
  }, [profile, name, description, isSaving, isDirty, tabId]); // Added tabId dependency

  // Effect to track dirty state and emit event
  useEffect(() => {
    if (!profile || isLoading) return;

    const nameChanged = name !== (profile.name || "");
    const currentDesc = description || "";
    const originalDesc = profile.description || "";
    const descriptionChanged = currentDesc !== originalDesc;
    const currentlyDirty = nameChanged || descriptionChanged;

    if (currentlyDirty !== isDirty) {
      setIsDirty(currentlyDirty);
      console.log(
        `[CharacterProfileEditor] Dirty state for ${tabId}: ${currentlyDirty}`
      );
      rendererEventBus.emit("editorDirtyStateChanged", {
        tabId,
        isDirty: currentlyDirty,
      });
    }
  }, [name, description, profile, isLoading, isDirty, tabId]);

  // Listener for forced save requests from main process
  useEffect(() => {
    if (!dataId) return;
    const targetChannel = `editor:forceSave:${dataId}`;
    const listener = () => {
      console.log(
        `[CharacterProfileEditor] Received force save trigger for ${dataId} on channel ${targetChannel}`
      );
      handleSave();
    };
    const unsubscribe = ipcRendererService.on(targetChannel, listener);
    return () => {
      unsubscribe();
    };
  }, [dataId, handleSave]); // handleSave is stable due to useCallback

  // --- Render Logic ---
  if (isLoading)
    return <div className={styles.loading}>Loading profile...</div>;
  if (error && !profile)
    return (
      <div className={styles.error}>Error loading profile: {error.message}</div>
    );
  if (!profile)
    return <div className={styles.error}>Character profile not available.</div>;

  const renderSectionContent = () => {
    switch (activeSection) {
      case "details":
        return (
          <CharacterProfileDescription
            description={description}
            isSaving={isSaving}
            onDescriptionChange={setDescription}
          />
        );
      case "attributes":
        return (
          <CharacterProfileAttributes
            characterId={profile.id}
            isSaving={isSaving}
          />
        );
      case "events":
        return (
          <CharacterProfileEvents
            characterId={profile.id}
            isSaving={isSaving}
          />
        );
      default:
        return <div>Select a section</div>;
    }
  };

  return (
    <div className={styles.layout}>
      <CharacterProfileNav
        activeSection={activeSection}
        onSectionSelect={setActiveSection}
      />
      <div className={styles.contentArea}>
        <CharacterProfileHeader
          name={name}
          isSaving={isSaving}
          onNameChange={setName}
          onSave={handleSave}
        />
        {error && (
          <div className={styles.error}>Save Error: {error.message}</div>
        )}
        {renderSectionContent()}
        <CharacterProfileMetadata
          createdAt={profile.createdAt}
          updatedAt={profile.updatedAt}
        />
      </div>
    </div>
  );
};

export default CharacterProfileEditor;
