import { create } from 'zustand';
import type { Character } from '../../shared/types';
import { IpcErrorData } from '@shared/types/ipc'; // Import IpcErrorData
import * as ipcChannels from '../../shared/constants/ipc-channels'; // Import channel constants
import { ipcRendererService } from '@renderer/core/services/ipcRendererService';

// State structure for characters UI
interface CharacterStoreState {
  // List View State
  characters: Pick<Character, 'id' | 'name'>[];
  isLoadingList: boolean;
  listError: IpcErrorData | null;

  // Profile Editor State (simplified for now)
  // We might load the full profile directly in the editor component later
  // Or store the currently open profile here if needed across components
  // currentProfile: Character | null;
  // isLoadingProfile: boolean;
  // profileError: IpcError | null;

  // Actions
  loadCharacters: () => Promise<void>;
  // loadProfile: (id: string) => Promise<Character | null>; // Example if storing profile here
}

export const useCharacterStore = create<CharacterStoreState>((set) => ({
  // Initial state
  characters: [],
  isLoadingList: false,
  listError: null,
  // currentProfile: null,
  // isLoadingProfile: false,
  // profileError: null,

  // Actions
  loadCharacters: async () => {
    console.log(`[CharacterStore] loadCharacters() called`);
    set({ isLoadingList: true, listError: null });
    try {
      // Use constant for channel name
      console.log(`[CharacterStore] Invoking ${ipcChannels.GET_CHARACTERS}`);
      const result = await ipcRendererService.invoke<Character[]>(ipcChannels.GET_CHARACTERS);
      console.log(`[CharacterStore] Received ${result.length} characters from IPC`);
      // ipcRendererService throws on error, so isIpcError check is not needed here
      // Check if the result is an array (expected success case)
      if (Array.isArray(result)) {
        // Only keep id and name for the list view state
        const characterList = result.map((char: Character) => ({ id: char.id, name: char.name }));
        console.log(`[CharacterStore] Setting ${characterList.length} characters in store:`, characterList);
        set({ characters: characterList, listError: null });
        console.log(`[CharacterStore] Characters successfully set in store`);
      } else {
         // Handle unexpected result type
         set({ listError: { code: 'unexpected_response', message: 'Invalid data received for character list.' }, characters: [] });
      }
    } catch (err) {
      const error = err as { message?: string };
      set({ listError: { code: 'ipc_call_failed', message: error.message || 'Failed to load characters' }, characters: [] });
    } finally {
      set({ isLoadingList: false });
    }
  },

  // Example action if profile was stored here:
  // loadProfile: async (id: string) => {
  //   set({ isLoadingProfile: true, profileError: null, currentProfile: null });
  //   try {
  //     const result = await window.electronAPI?.invoke(`${EXTENSION_ID}:getCharacterProfile`, id);
  //     if (isIpcError(result)) {
  //       set({ profileError: result });
  //       return null;
  //     } else {
  //       set({ currentProfile: result, profileError: null });
  //       return result;
  //     }
  //   } catch (error: any) {
  //     set({ profileError: { code: 'ipc_call_failed', message: error.message || 'Failed to load profile' } });
  //     return null;
  //   } finally {
  //     set({ isLoadingProfile: false });
  //   }
  // },

}));

// Subscribe to backend events to keep the list updated
const handleCharactersUpdated = (data: unknown) => {
    console.log(`[CharacterStore] Received ${ipcChannels.CHARACTERS_UPDATED_EVENT} event, reloading list...`);
    console.log(`[CharacterStore] Event data:`, data);
    console.log(`[CharacterStore] Current character list length before reload:`, useCharacterStore.getState().characters.length);
    useCharacterStore.getState().loadCharacters();
    console.log(`[CharacterStore] Called loadCharacters() after receiving event`);
};

// Add a test event listener to verify IPC communication
const handleTestEvent = (data: unknown) => {
    console.log(`[CharacterStore] Received test-event:`, data);
};

// Use ipcRendererService.on for subscription
// This subscription will remain active for the lifetime of the application
ipcRendererService.on(
    ipcChannels.CHARACTERS_UPDATED_EVENT, // Use constant
    handleCharactersUpdated
);

// Subscribe to test event to verify IPC communication
ipcRendererService.on('test-event', handleTestEvent);

console.log(`[CharacterStore] Subscribed to ${ipcChannels.CHARACTERS_UPDATED_EVENT} event.`);
console.log(`[CharacterStore] Subscribed to test-event for debugging.`);

// TODO: Implement a proper way to call unsubscribeCharacters when the app/extension unloads.
// This might involve returning it from an initialization function or managing it globally.

// Selectors
export const useCharacterList = () => useCharacterStore((state) => state.characters);
export const useIsLoadingCharacterList = () => useCharacterStore((state) => state.isLoadingList);
export const useCharacterListError = () => useCharacterStore((state) => state.listError);
