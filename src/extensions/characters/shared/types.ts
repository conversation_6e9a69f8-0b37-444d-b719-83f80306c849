// Basic character profile structure
export interface Character {
  id: string;
  name: string;
  description?: string; // Optional description
  bookId?: string; // Link to book - optional for backward compatibility
  role?: string; // Character role in the story
  createdAt: number; // Timestamp
  updatedAt: number; // Timestamp
}

// Character event for tracking development
export interface CharacterEvent {
  id: string;
  characterId: string;
  bookId: string;
  relatedSceneId?: string; // Optional link to scene
  timelinePosition: number; // 0-100 scale representing story progression
  eventType: CharacterEventType;
  description: string;
  impact?: number; // 1-10 scale for significance
  createdAt: number;
  updatedAt: number;
}

// Types of character development events
export type CharacterEventType = 
  | 'Personality'    // Changes in personality traits
  | 'Goals'          // Goal-related developments
  | 'Relationships'  // Relationship changes
  | 'Conflicts';     // Internal or external conflicts

// Character attributes for flexible metadata
export interface CharacterAttribute {
  id: string;
  characterId: string;
  attributeKey: string;
  attributeValue: string;
  createdAt: number;
}

// Extended character profile with events and attributes
export interface CharacterProfile extends Character {
  events: CharacterEvent[];
  attributes: CharacterAttribute[];
}

// Character event creation data
export interface CreateCharacterEventData {
  characterId: string;
  bookId: string;
  relatedSceneId?: string;
  timelinePosition: number;
  eventType: CharacterEventType;
  description: string;
  impact?: number;
}

// Character event update data
export interface UpdateCharacterEventData {
  relatedSceneId?: string;
  timelinePosition?: number;
  eventType?: CharacterEventType;
  description?: string;
  impact?: number;
}
