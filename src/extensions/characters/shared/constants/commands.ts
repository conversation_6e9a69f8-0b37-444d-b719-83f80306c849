/**
 * Константы для идентификаторов команд.
 */
import { EXTENSION_ID } from './extension';

// Команды для персонажей
export const CHARACTER_CREATE = `${EXTENSION_ID}:commands.createCharacter`;
export const CHARACTER_DELETE = `${EXTENSION_ID}:commands.deleteCharacter`;
export const CHARACTER_UPDATE = `${EXTENSION_ID}:commands.updateCharacter`;
export const CHARACTER_OPEN_PROFILE = `${EXTENSION_ID}:commands.openCharacterProfile`;

// Тип для идентификаторов команд
export type CommandId = 
  | typeof CHARACTER_CREATE
  | typeof CHARACTER_DELETE
  | typeof CHARACTER_UPDATE
  | typeof CHARACTER_OPEN_PROFILE;
