/**
 * Константы для идентификаторов каналов IPC.
 */
import { EXTENSION_ID } from './extension';

// Каналы IPC для персонажей
export const GET_CHARACTERS = `${EXTENSION_ID}:channels.getCharacters`;
export const GET_CHARACTER = `${EXTENSION_ID}:channels.getCharacter`;
export const GET_CHARACTER_PROFILE = `${EXTENSION_ID}:channels.getCharacterProfile`;
export const CREATE_CHARACTER = `${EXTENSION_ID}:channels.createCharacter`;
export const UPDATE_CHARACTER = `${EXTENSION_ID}:channels.updateCharacter`;
export const DELETE_CHARACTER = `${EXTENSION_ID}:channels.deleteCharacter`;

// Каналы IPC для событий персонажей
export const GET_CHARACTER_EVENTS = `${EXTENSION_ID}:channels.getCharacterEvents`;
export const CREATE_CHARACTER_EVENT = `${EXTENSION_ID}:channels.createCharacterEvent`;
export const UPDATE_CHARACTER_EVENT = `${EXTENSION_ID}:channels.updateCharacterEvent`;
export const DELETE_CHARACTER_EVENT = `${EXTENSION_ID}:channels.deleteCharacterEvent`;
export const GET_EVENTS_BY_SCENE = `${EXTENSION_ID}:channels.getEventsByScene`;

// Каналы IPC для атрибутов персонажей
export const GET_CHARACTER_ATTRIBUTES = `${EXTENSION_ID}:channels.getCharacterAttributes`;
export const CREATE_CHARACTER_ATTRIBUTE = `${EXTENSION_ID}:channels.createCharacterAttribute`;
export const UPDATE_CHARACTER_ATTRIBUTE = `${EXTENSION_ID}:channels.updateCharacterAttribute`;
export const DELETE_CHARACTER_ATTRIBUTE = `${EXTENSION_ID}:channels.deleteCharacterAttribute`;

// События (Main -> Renderer)
export const CHARACTERS_UPDATED_EVENT = 'characters-updated';
export const CHARACTER_EVENTS_UPDATED_EVENT = 'character-events-updated';

// Тип для идентификаторов каналов IPC
export type IpcChannelId = 
  | typeof GET_CHARACTERS
  | typeof GET_CHARACTER
  | typeof GET_CHARACTER_PROFILE
  | typeof CREATE_CHARACTER
  | typeof UPDATE_CHARACTER
  | typeof DELETE_CHARACTER
  | typeof GET_CHARACTER_EVENTS
  | typeof CREATE_CHARACTER_EVENT
  | typeof UPDATE_CHARACTER_EVENT
  | typeof DELETE_CHARACTER_EVENT
  | typeof GET_EVENTS_BY_SCENE
  | typeof GET_CHARACTER_ATTRIBUTES
  | typeof CREATE_CHARACTER_ATTRIBUTE
  | typeof UPDATE_CHARACTER_ATTRIBUTE
  | typeof DELETE_CHARACTER_ATTRIBUTE
  | typeof CHARACTERS_UPDATED_EVENT
  | typeof CHARACTER_EVENTS_UPDATED_EVENT;
