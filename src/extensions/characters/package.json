{"name": "characters", "publisher": "book-ide", "version": "0.0.1", "main": "main/index.js", "activationEvents": [], "contributes": {"viewContainers": [{"id": "book-ide.characters", "title": "Characters", "icon": "Users", "viewId": "book-ide.characters.view"}], "views": {"book-ide.characters": [{"id": "book-ide.characters.view", "name": "Characters", "componentName": "Characters<PERSON><PERSON>w", "location": "sidebar", "icon": "Users"}]}, "commands": [{"command": "book-ide.characters:commands.createCharacter", "title": "Create Character"}, {"command": "book-ide.characters:commands.deleteCharacter", "title": "Delete Character"}, {"command": "book-ide.characters:commands.openCharacterProfile", "title": "Open Character Profile"}], "menus": {"view/character/item": [{"command": "book-ide.characters:commands.openCharacterProfile", "group": "navigation"}, {"command": "book-ide.characters:commands.deleteCharacter", "group": "navigation"}]}, "editors": [{"editorType": "book-ide.characters:editors.profileEditor", "componentName": "CharacterProfileEditor", "icon": "User"}], "aiTasks": [{"taskId": "book-ide.characters:tasks.consistencyCheck", "description": "Checks scene text for consistency with character profile and events.", "outputSchema": {"type": "array", "items": {"type": "object", "properties": {"inconsistent_text": {"type": "string", "description": "The specific snippet from the scene text that appears inconsistent."}, "explanation": {"type": "string", "description": "A brief explanation of why this text seems inconsistent with the character's profile or past events."}, "context_violated": {"type": "string", "description": "The specific trait or event description from the context that the text contradicts."}, "severity": {"type": "string", "enum": ["low", "medium", "high"], "description": "An estimated severity of the inconsistency."}}, "required": ["inconsistent_text", "explanation", "context_violated", "severity"]}}, "requiredContext": ["sceneContent", "characterProfile", "characterEvents", "sceneTimelinePosition"]}]}}