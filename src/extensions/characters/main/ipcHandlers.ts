// import type { Ipc<PERSON>ainInvokeEvent } from 'electron'; // Not used currently
import type {ExtensionContext} from '@main/extensions/extension.types'; // Import ExtensionContext
import type {CharacterStorage} from './storage';
import {IpcErrorData} from '@shared/types/ipc';
import type {Character, CharacterEvent, CharacterAttribute, CreateCharacterEventData, UpdateCharacterEventData, CharacterEventType} from '../shared/types';
import * as ipcChannels from '../shared/constants/ipc-channels'; // Import channel constants
import {EXTENSION_ID} from '../shared/constants/extension'; // Import EXTENSION_ID from shared
import {CONSISTENCY_CHECK} from '../shared/constants/ai-tasks'; // Import AI task constants
import * as fs from 'fs/promises';
// Replace GenericRecord with standard Record type 
type GenericRecord = Record<string, unknown>;

// Define expected argument type for ai:runTask specific to this extension
interface RunCharacterConsistencyCheckArgs {
  taskId: string; // Should be 'ai-books.characters:consistencyCheck'
  context: {
    sceneId: string;
    characterId: string;
    // Potentially add selectedText later if needed
  };
}

// Import the generic AICompletionResponse
import type { AICompletionResponse as GenericAICompletionResponse } from '@shared/types/ai';

// Define specific response type that extends the generic one
interface ConsistencyCheckResult extends GenericAICompletionResponse {
  issues: {
    type: string;
    description: string;
    severity: 'info' | 'warning' | 'error';
    location?: string;
  }[];
  summary: string;
  metadata?: GenericRecord;
}

// Define payload types for other handlers
interface GetCharacterProfilePayload {
  id: string
}

interface UpdateCharacterProfilePayload {
  id: string;
  data: Partial<Omit<Character, 'id' | 'createdAt'>>
}

interface CreateCharacterPayload {
  name: string;
  description?: string
}

interface DeleteCharacterPayload {
  id: string
}

interface RunConsistencyCheckPayload {
  context: RunCharacterConsistencyCheckArgs['context']
} // Only context needed

// Define payload types for character events
interface CreateCharacterEventPayload {
  characterId: string;
  bookId: string;
  eventType: CharacterEventType;
  description: string;
  timelinePosition: number;
  relatedSceneId?: string;
  impact?: number;
}

interface UpdateCharacterEventPayload {
  id: string;
  data: UpdateCharacterEventData;
}

interface GetCharacterEventsPayload {
  characterId: string;
  timelineStart?: number;
  timelineEnd?: number;
}

interface DeleteCharacterEventPayload {
  id: string;
}

// Define payload types for character attributes
interface CreateCharacterAttributePayload {
  characterId: string;
  attributeKey: string;
  attributeValue: string;
}

interface UpdateCharacterAttributePayload {
  id: string;
  attributeValue: string;
}

interface GetCharacterAttributesPayload {
  characterId: string;
}

interface DeleteCharacterAttributePayload {
  id: string;
}

export function registerCharacterIpcHandlers(context: ExtensionContext, characterStorage: CharacterStorage): void {
  const {logger, subscriptions, ipc, ai, storage} = context; // Destructure from context
  logger.info(`[Characters] Registering IPC Handlers...`);
  
  // Handler to get a single character's profile
  subscriptions.push(
    ipc.handle<GetCharacterProfilePayload, Character | IpcErrorData | null>(
      ipcChannels.GET_CHARACTER, // Use constant
      async (payload: GetCharacterProfilePayload): Promise<Character | IpcErrorData | null> => {
        const {id} = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.GET_CHARACTER} for ${id}`);
          const character = await characterStorage.getCharacterById(id);
          if (!character) {
            logger.warn(`[Characters:IPC] Character ${id} not found for ${ipcChannels.GET_CHARACTER}.`);
            return null;
          }
          return character;
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.GET_CHARACTER} for ${id}`, error);
          return {code: 'storage_error', message: `Не удалось получить профиль персонажа: ${message}`};
        }
      }
    ) // End of handle for getCharacterProfile
  );
  
  // Handler to update a character's profile
  subscriptions.push(
    ipc.handle<UpdateCharacterProfilePayload, { success: boolean } | IpcErrorData>(
      ipcChannels.UPDATE_CHARACTER, // Use constant
      async (payload: UpdateCharacterProfilePayload): Promise<{ success: boolean } | IpcErrorData> => {
        const {id, data} = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.UPDATE_CHARACTER} for ${id}`);
          const success = await characterStorage.updateCharacter(id, data);
          if (success) {
            // Notify renderer about the update
            ipc.send(ipcChannels.CHARACTERS_UPDATED_EVENT, {updatedId: id}); // Send event with ID
            logger.info(`[Characters:IPC] Character ${id} updated.`);
            return {success: true};
          } else {
            logger.warn(`[Characters:IPC] Character ${id} not found or no changes made during ${ipcChannels.UPDATE_CHARACTER}.`);
            return {code: 'update_failed', message: 'Персонаж не найден или данные не изменились.'};
          }
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.UPDATE_CHARACTER} for ${id}`, error);
          return {code: 'storage_error', message: `Не удалось обновить профиль персонажа: ${message}`};
        }
      }
    ) // End of handle for updateCharacterProfile
  );
  
  // Handler to get all characters
  subscriptions.push(
    ipc.handle<unknown, Character[] | IpcErrorData>(
      ipcChannels.GET_CHARACTERS, // Use constant
      async (): Promise<Character[] | IpcErrorData> => {
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.GET_CHARACTERS}`);
          return await characterStorage.getAllCharacters();
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.GET_CHARACTERS}`, error);
          return {code: 'storage_error', message: `Не удалось получить персонажей: ${message}`};
        }
      }
    ) // End of handle for getCharacters
  );
  
  // Optional: Handler to create character via IPC (matches command)
  subscriptions.push(
    ipc.handle<CreateCharacterPayload, Character | IpcErrorData>(
      ipcChannels.CREATE_CHARACTER, // Use constant
      async (payload: CreateCharacterPayload): Promise<Character | IpcErrorData> => {
        const {name, description} = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.CREATE_CHARACTER}`);
          const character = await characterStorage.createCharacter(name, description);
          ipc.send(ipcChannels.CHARACTERS_UPDATED_EVENT, {}); // Notify UI
          logger.info(`[Characters:IPC] Character created with ID: ${character.id}`);
          return character;
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.CREATE_CHARACTER}`, error);
          return {code: 'storage_error', message: `Не удалось создать персонажа: ${message}`};
        }
      }
    ) // End of handle for createCharacter
  );
  
  // Handler to delete character (matches command)
  subscriptions.push(
    ipc.handle<DeleteCharacterPayload, { success: boolean } | IpcErrorData>(
      ipcChannels.DELETE_CHARACTER, // Use constant
      async (payload: DeleteCharacterPayload): Promise<{ success: boolean } | IpcErrorData> => {
        const {id} = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.DELETE_CHARACTER} for ${id}`);
          const success = await characterStorage.deleteCharacter(id);
          if (success) {
            ipc.send(ipcChannels.CHARACTERS_UPDATED_EVENT, {}); // Notify UI
            logger.info(`[Characters:IPC] Character ${id} deleted.`);
            return {success: true};
          } else {
            return {code: 'storage_error', message: 'Персонаж не найден или не удалось удалить.'};
          }
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.DELETE_CHARACTER} for ${id}`, error);
          return {code: 'storage_error', message: `Не удалось удалить персонажа: ${message}`};
        }
      }
    ) // End of handle for deleteCharacter
  );
  
  // --- AI Task Handler ---
  subscriptions.push(
    ipc.handle<RunConsistencyCheckPayload, ConsistencyCheckResult | IpcErrorData>(
      `${EXTENSION_ID}:channels.runConsistencyCheck`, // Use constant
      async (payload: RunConsistencyCheckPayload): Promise<ConsistencyCheckResult | IpcErrorData> => {
        const {context: taskContext} = payload;
        const {sceneId, characterId} = taskContext;
        const taskId = CONSISTENCY_CHECK; // Use constant
        logger.info(`[Characters:IPC] Handling runConsistencyCheck for char ${characterId} in scene ${sceneId}`);
        
        if (!ai) { // Use destructured ai service
          return {code: 'service_unavailable', message: 'AI Service is not available.'};
        }
        if (!storage) { // Use destructured storage service
          return {code: 'service_unavailable', message: 'Storage Service is not available.'};
        }
        // TODO: Need access to SceneContentService or equivalent logic
        
        try {
          // 1. Gather Context
          const characterProfile = await characterStorage.getCharacterById(characterId);
          if (!characterProfile) {
            return {code: 'not_found', message: `Character ${characterId} not found.`};
          }
          
          // Use context.storage (which is StorageServiceAPI)
          const sceneMetadata = await storage.get<{ timeline_position?: number; markdown_file_path: string }>(
            'SELECT timeline_position, markdown_file_path FROM scenes WHERE id = ?', sceneId
          );
          if (!sceneMetadata || !sceneMetadata.markdown_file_path) {
            return {code: 'not_found', message: `Scene metadata or file path for ${sceneId} not found.`};
          }
          const sceneTimelinePosition = sceneMetadata.timeline_position ?? 50;
          
          // Get character events from the database
          const characterEvents = await characterStorage.getCharacterEvents(characterId);
          // Filter events relevant to this point in the timeline
          const relevantEvents = characterEvents.filter(event => 
            event.timelinePosition <= sceneTimelinePosition
          );
          
          const characterEventsSummary = relevantEvents.length > 0 
            ? relevantEvents.map(e => `- ${e.eventType} (Impact ${e.impact || 'N/A'}): ${e.description}`).join('\n')
            : 'No relevant past events recorded.';
          
          // TODO: Implement reading scene content (needs SceneContentService or direct fs access)
          let sceneContent = ''; // Placeholder
          try {
            sceneContent = await fs.readFile(sceneMetadata.markdown_file_path, 'utf-8');
          } catch (readError) {
            logger.error(`[Characters:IPC] Failed to read scene content for ${sceneId} at ${sceneMetadata.markdown_file_path}`, readError);
            return {code: 'file_read_error', message: `Failed to read scene content.`};
          }
          
          const fullContextForAI = {
            characterName: characterProfile.name,
            characterTraits: characterProfile.description || 'No specific traits listed.',
            characterEventsSummary: characterEventsSummary,
            sceneTimelinePosition: sceneTimelinePosition,
            sceneContent: sceneContent,
          };
          
          // 2. Call AIService
          logger.info(`[Characters:IPC] Calling AIService.runTask for ${taskId}`);
          const result = await ai.runTask(taskId, fullContextForAI);
          
          // If the result is an error, return it as-is
          if ('code' in result) {
            return result;
          }
          
          // Otherwise, assume it's a valid AICompletionResponse that matches our schema
          return result as ConsistencyCheckResult;
          
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error gathering context or running AI task ${taskId}`, error);
          return {code: 'handler_error', message: `Error processing consistency check: ${message}`};
        }
      }
    ) // End of handle for runConsistencyCheck
  );

  // --- Character Events Handlers ---
  
  // Handler to create character event
  subscriptions.push(
    ipc.handle<CreateCharacterEventPayload, CharacterEvent | IpcErrorData>(
      ipcChannels.CREATE_CHARACTER_EVENT,
      async (payload: CreateCharacterEventPayload): Promise<CharacterEvent | IpcErrorData> => {
        const { characterId, bookId, eventType, description, timelinePosition, relatedSceneId, impact } = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.CREATE_CHARACTER_EVENT} for character ${characterId}`);
          const eventData: CreateCharacterEventData = {
            characterId,
            bookId,
            eventType,
            description,
            timelinePosition,
            relatedSceneId,
            impact
          };
          const newEvent = await characterStorage.createCharacterEvent(eventData);
          ipc.send(ipcChannels.CHARACTER_EVENTS_UPDATED_EVENT, { characterId });
          return newEvent;
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.CREATE_CHARACTER_EVENT}`, error);
          return { code: 'storage_error', message: `Не удалось создать событие персонажа: ${message}` };
        }
      }
    )
  );

  // Handler to get character events
  subscriptions.push(
    ipc.handle<GetCharacterEventsPayload, CharacterEvent[] | IpcErrorData>(
      ipcChannels.GET_CHARACTER_EVENTS,
      async (payload: GetCharacterEventsPayload): Promise<CharacterEvent[] | IpcErrorData> => {
        const { characterId, timelineStart, timelineEnd } = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.GET_CHARACTER_EVENTS} for character ${characterId}`);
          let events: CharacterEvent[];
          if (timelineStart !== undefined && timelineEnd !== undefined) {
            events = await characterStorage.getEventsByTimelineRange(characterId, timelineStart, timelineEnd);
          } else {
            events = await characterStorage.getCharacterEvents(characterId);
          }
          return events;
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.GET_CHARACTER_EVENTS}`, error);
          return { code: 'storage_error', message: `Не удалось получить события персонажа: ${message}` };
        }
      }
    )
  );

  // Handler to update character event
  subscriptions.push(
    ipc.handle<UpdateCharacterEventPayload, { success: boolean } | IpcErrorData>(
      ipcChannels.UPDATE_CHARACTER_EVENT,
      async (payload: UpdateCharacterEventPayload): Promise<{ success: boolean } | IpcErrorData> => {
        const { id, data } = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.UPDATE_CHARACTER_EVENT} for event ${id}`);
          const success = await characterStorage.updateCharacterEvent(id, data);
          if (success) {
            ipc.send(ipcChannels.CHARACTER_EVENTS_UPDATED_EVENT, { eventId: id });
            return { success: true };
          } else {
            return { code: 'update_failed', message: 'Событие не найдено или данные не изменились.' };
          }
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.UPDATE_CHARACTER_EVENT}`, error);
          return { code: 'storage_error', message: `Не удалось обновить событие персонажа: ${message}` };
        }
      }
    )
  );

  // Handler to delete character event
  subscriptions.push(
    ipc.handle<DeleteCharacterEventPayload, { success: boolean } | IpcErrorData>(
      ipcChannels.DELETE_CHARACTER_EVENT,
      async (payload: DeleteCharacterEventPayload): Promise<{ success: boolean } | IpcErrorData> => {
        const { id } = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.DELETE_CHARACTER_EVENT} for event ${id}`);
          const success = await characterStorage.deleteCharacterEvent(id);
          if (success) {
            ipc.send(ipcChannels.CHARACTER_EVENTS_UPDATED_EVENT, { eventId: id });
            return { success: true };
          } else {
            return { code: 'delete_failed', message: 'Событие не найдено или не удалось удалить.' };
          }
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.DELETE_CHARACTER_EVENT}`, error);
          return { code: 'storage_error', message: `Не удалось удалить событие персонажа: ${message}` };
        }
      }
    )
  );

  // --- Character Attributes Handlers ---
  
  // Handler to create character attribute
  subscriptions.push(
    ipc.handle<CreateCharacterAttributePayload, CharacterAttribute | IpcErrorData>(
      ipcChannels.CREATE_CHARACTER_ATTRIBUTE,
      async (payload: CreateCharacterAttributePayload): Promise<CharacterAttribute | IpcErrorData> => {
        const { characterId, attributeKey, attributeValue } = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.CREATE_CHARACTER_ATTRIBUTE} for character ${characterId}`);
          const newAttribute = await characterStorage.createCharacterAttribute(characterId, attributeKey, attributeValue);
          ipc.send(ipcChannels.CHARACTER_EVENTS_UPDATED_EVENT, { characterId }); // Reuse same event for simplicity
          return newAttribute;
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.CREATE_CHARACTER_ATTRIBUTE}`, error);
          return { code: 'storage_error', message: `Не удалось создать атрибут персонажа: ${message}` };
        }
      }
    )
  );

  // Handler to get character attributes
  subscriptions.push(
    ipc.handle<GetCharacterAttributesPayload, CharacterAttribute[] | IpcErrorData>(
      ipcChannels.GET_CHARACTER_ATTRIBUTES,
      async (payload: GetCharacterAttributesPayload): Promise<CharacterAttribute[] | IpcErrorData> => {
        const { characterId } = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.GET_CHARACTER_ATTRIBUTES} for character ${characterId}`);
          const attributes = await characterStorage.getCharacterAttributes(characterId);
          return attributes;
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.GET_CHARACTER_ATTRIBUTES}`, error);
          return { code: 'storage_error', message: `Не удалось получить атрибуты персонажа: ${message}` };
        }
      }
    )
  );

  // Handler to update character attribute
  subscriptions.push(
    ipc.handle<UpdateCharacterAttributePayload, { success: boolean } | IpcErrorData>(
      ipcChannels.UPDATE_CHARACTER_ATTRIBUTE,
      async (payload: UpdateCharacterAttributePayload): Promise<{ success: boolean } | IpcErrorData> => {
        const { id, attributeValue } = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.UPDATE_CHARACTER_ATTRIBUTE} for attribute ${id}`);
          const success = await characterStorage.updateCharacterAttribute(id, attributeValue);
          if (success) {
            ipc.send(ipcChannels.CHARACTER_EVENTS_UPDATED_EVENT, { attributeId: id }); // Reuse same event for simplicity
            return { success: true };
          } else {
            return { code: 'update_failed', message: 'Атрибут не найден или данные не изменились.' };
          }
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.UPDATE_CHARACTER_ATTRIBUTE}`, error);
          return { code: 'storage_error', message: `Не удалось обновить атрибут персонажа: ${message}` };
        }
      }
    )
  );

  // Handler to delete character attribute
  subscriptions.push(
    ipc.handle<DeleteCharacterAttributePayload, { success: boolean } | IpcErrorData>(
      ipcChannels.DELETE_CHARACTER_ATTRIBUTE,
      async (payload: DeleteCharacterAttributePayload): Promise<{ success: boolean } | IpcErrorData> => {
        const { id } = payload;
        try {
          logger.info(`[Characters:IPC] Handling ${ipcChannels.DELETE_CHARACTER_ATTRIBUTE} for attribute ${id}`);
          const success = await characterStorage.deleteCharacterAttribute(id);
          if (success) {
            ipc.send(ipcChannels.CHARACTER_EVENTS_UPDATED_EVENT, { attributeId: id }); // Reuse same event for simplicity
            return { success: true };
          } else {
            return { code: 'delete_failed', message: 'Атрибут не найден или не удалось удалить.' };
          }
        } catch (error: unknown) {
          const message = error instanceof Error ? error.message : String(error);
          logger.error(`[Characters:IPC] Error handling ${ipcChannels.DELETE_CHARACTER_ATTRIBUTE}`, error);
          return { code: 'storage_error', message: `Не удалось удалить атрибут персонажа: ${message}` };
        }
      }
    )
  );
  
  logger.info(`[${EXTENSION_ID}] All IPC Handlers registered.`);
}
