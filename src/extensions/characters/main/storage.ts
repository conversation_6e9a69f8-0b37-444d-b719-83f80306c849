import type { StorageServiceAPI } from '@main/services/storage.service';
import type { 
  Character, 
  CharacterEvent, 
  CharacterAttribute, 
  CharacterProfile, 
  CreateCharacterEventData, 
  UpdateCharacterEventData 
} from '../shared/types';
import { logger } from '@main/services/logging.service';

// Класс для работы с хранилищем персонажей
export class CharacterStorage {
  private storage: StorageServiceAPI;

  constructor(storageAPI: StorageServiceAPI) {
    this.storage = storageAPI;
  }  // --- Schema ---
  async ensureSchema(): Promise<void> {
    logger.info('[CharacterStorage] Ensuring schema...');
    
    // First, check if we need to migrate existing characters table
    await this.migrateCharactersTable();
    
    const createCharactersTable = `
      CREATE TABLE IF NOT EXISTS characters (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        bookId TEXT, -- Link to book
        role TEXT, -- Character role
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (bookId) REFERENCES books(id) ON DELETE SET NULL
      );
    `;

    const createCharacterEventsTable = `
      CREATE TABLE IF NOT EXISTS character_events (
        id TEXT PRIMARY KEY NOT NULL,
        characterId TEXT NOT NULL,
        bookId TEXT NOT NULL,
        relatedSceneId TEXT,
        timelinePosition INTEGER NOT NULL, -- 0-100 scale
        eventType TEXT NOT NULL, -- 'Personality', 'Goals', 'Relationships', 'Conflicts'
        description TEXT NOT NULL,
        impact INTEGER, -- 1-10 scale
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (characterId) REFERENCES characters(id) ON DELETE CASCADE,
        FOREIGN KEY (relatedSceneId) REFERENCES scenes(id) ON DELETE SET NULL
      );
    `;

    const createCharacterAttributesTable = `
      CREATE TABLE IF NOT EXISTS character_attributes (
        id TEXT PRIMARY KEY NOT NULL,
        characterId TEXT NOT NULL,
        attributeKey TEXT NOT NULL,
        attributeValue TEXT,
        createdAt INTEGER NOT NULL,
        FOREIGN KEY (characterId) REFERENCES characters(id) ON DELETE CASCADE
      );
    `;

    // Create all tables in a transaction
    await this.storage.transaction(() => {
      this.storage.run(createCharactersTable);
      this.storage.run(createCharacterEventsTable);
      this.storage.run(createCharacterAttributesTable);
    });

    logger.info('[CharacterStorage] Schema ensured.');
  }

  // --- Migration ---
  private async migrateCharactersTable(): Promise<void> {
    try {
      // Check if characters table exists and get its schema
      const tableInfo = await this.storage.all<{ name: string; type: string; notnull: number; dflt_value: string | null; pk: number }>(
        "PRAGMA table_info(characters)"
      );
      
      if (tableInfo.length === 0) {
        // Table doesn't exist yet, no migration needed
        logger.info('[CharacterStorage] Characters table does not exist yet, no migration needed');
        return;
      }

      // Check if bookId and role columns exist
      const hasBookId = tableInfo.some(col => col.name === 'bookId');
      const hasRole = tableInfo.some(col => col.name === 'role');

      if (hasBookId && hasRole) {
        // Both columns exist, no migration needed
        logger.info('[CharacterStorage] Characters table already has bookId and role columns');
        return;
      }

      logger.info('[CharacterStorage] Migrating characters table to add missing columns');
      
      // Add missing columns using ALTER TABLE
      await this.storage.transaction(async () => {
        if (!hasBookId) {
          logger.info('[CharacterStorage] Adding bookId column to characters table');
          await this.storage.run("ALTER TABLE characters ADD COLUMN bookId TEXT");
        }
        
        if (!hasRole) {
          logger.info('[CharacterStorage] Adding role column to characters table');
          await this.storage.run("ALTER TABLE characters ADD COLUMN role TEXT");
        }
      });

      logger.info('[CharacterStorage] Characters table migration completed successfully');
    } catch (error) {
      logger.error('[CharacterStorage] Error during characters table migration:', error);
      throw error;
    }
  }

  // --- Character CRUD ---
  async createCharacter(name: string, description?: string, bookId?: string, role?: string): Promise<Character> {
    logger.info(`[CharacterStorage] Creating character with name "${name}"`);
    const now = Date.now();
    const newCharacter: Character = {
      id: `char-${now}-${Math.random().toString(36).substring(2, 8)}`,
      name,
      description: description || undefined,
      bookId,
      role,
      createdAt: now,
      updatedAt: now,
    };
    
    await this.storage.run(
      'INSERT INTO characters (id, name, description, bookId, role, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?)',
      newCharacter.id, newCharacter.name, newCharacter.description, newCharacter.bookId, newCharacter.role, newCharacter.createdAt, newCharacter.updatedAt
    );
    
    logger.info(`[CharacterStorage] Character created with ID: ${newCharacter.id}`);
    return newCharacter;
  }

  async getAllCharacters(bookId?: string): Promise<Character[]> {
    logger.info(`[CharacterStorage] Getting all characters${bookId ? ` for book ${bookId}` : ''}`);
    
    if (bookId) {
      return this.storage.all<Character>(
        'SELECT id, name, description, bookId, role, createdAt, updatedAt FROM characters WHERE bookId = ? ORDER BY name ASC', 
        bookId
      );
    } else {
      return this.storage.all<Character>(
        'SELECT id, name, description, bookId, role, createdAt, updatedAt FROM characters ORDER BY name ASC'
      );
    }
  }

  async getCharacterById(id: string): Promise<Character | null> {
    logger.info(`[CharacterStorage] Getting character by ID ${id}`);
    const character = await this.storage.get<Character>(
      'SELECT id, name, description, bookId, role, createdAt, updatedAt FROM characters WHERE id = ?', 
      id
    );
    return character || null;
  }

  async getCharacterProfile(id: string): Promise<CharacterProfile | null> {
    logger.info(`[CharacterStorage] Getting full character profile for ${id}`);
    
    const character = await this.getCharacterById(id);
    if (!character) {
      return null;
    }

    const events = await this.getCharacterEvents(id);
    const attributes = await this.getCharacterAttributes(id);

    return {
      ...character,
      events,
      attributes
    };
  }

  async updateCharacter(id: string, data: Partial<Omit<Character, 'id' | 'createdAt'>>): Promise<boolean> {
    logger.info(`[CharacterStorage] Updating character ${id}`);
    const fieldsToUpdate = Object.keys(data).filter(key => key !== 'id' && key !== 'createdAt');
    if (fieldsToUpdate.length === 0) {
      logger.warn(`[CharacterStorage] Update character ${id} called with no fields to update.`);
      return false;
    }

    const now = Date.now();
    const setClause = fieldsToUpdate.map(field => `${field} = ?`).join(', ');
    const values = fieldsToUpdate.map(field => (data as Record<string, unknown>)[field]);
    values.push(now); // updatedAt
    values.push(id); // WHERE id = ?

    const sql = `UPDATE characters SET ${setClause}, updatedAt = ? WHERE id = ?`;
    const info = await this.storage.run(sql, ...values);
    logger.info(`[CharacterStorage] Character ${id} updated, changes: ${info.changes}`);
    return info.changes > 0;
  }

  async deleteCharacter(id: string): Promise<boolean> {
    logger.info(`[CharacterStorage] Deleting character ${id}`);
    // Events and attributes will be deleted by CASCADE
    const info = await this.storage.run('DELETE FROM characters WHERE id = ?', id);
    logger.info(`[CharacterStorage] Character ${id} deleted, changes: ${info.changes}`);
    return info.changes > 0;
  }

  // --- Character Events CRUD ---
  async createCharacterEvent(data: CreateCharacterEventData): Promise<CharacterEvent> {
    logger.info(`[CharacterStorage] Creating character event for character ${data.characterId}`);
    const now = Date.now();
    const newEvent: CharacterEvent = {
      id: `event-${now}-${Math.random().toString(36).substring(2, 8)}`,
      characterId: data.characterId,
      bookId: data.bookId,
      relatedSceneId: data.relatedSceneId,
      timelinePosition: data.timelinePosition,
      eventType: data.eventType,
      description: data.description,
      impact: data.impact,
      createdAt: now,
      updatedAt: now,
    };

    await this.storage.run(
      'INSERT INTO character_events (id, characterId, bookId, relatedSceneId, timelinePosition, eventType, description, impact, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
      newEvent.id, newEvent.characterId, newEvent.bookId, newEvent.relatedSceneId, newEvent.timelinePosition, 
      newEvent.eventType, newEvent.description, newEvent.impact, newEvent.createdAt, newEvent.updatedAt
    );

    logger.info(`[CharacterStorage] Character event created with ID: ${newEvent.id}`);
    return newEvent;
  }

  async getCharacterEvents(characterId: string): Promise<CharacterEvent[]> {
    logger.info(`[CharacterStorage] Getting events for character ${characterId}`);
    return this.storage.all<CharacterEvent>(
      'SELECT id, characterId, bookId, relatedSceneId, timelinePosition, eventType, description, impact, createdAt, updatedAt FROM character_events WHERE characterId = ? ORDER BY timelinePosition ASC',
      characterId
    );
  }

  async getCharacterEventById(eventId: string): Promise<CharacterEvent | null> {
    logger.info(`[CharacterStorage] Getting character event by ID ${eventId}`);
    const event = await this.storage.get<CharacterEvent>(
      'SELECT id, characterId, bookId, relatedSceneId, timelinePosition, eventType, description, impact, createdAt, updatedAt FROM character_events WHERE id = ?',
      eventId
    );
    return event || null;
  }

  async updateCharacterEvent(eventId: string, data: UpdateCharacterEventData): Promise<boolean> {
    logger.info(`[CharacterStorage] Updating character event ${eventId}`);
    const fieldsToUpdate = Object.keys(data);
    if (fieldsToUpdate.length === 0) {
      logger.warn(`[CharacterStorage] Update character event ${eventId} called with no fields to update.`);
      return false;
    }

    const now = Date.now();
    const setClause = fieldsToUpdate.map(field => `${field} = ?`).join(', ');
    const values = fieldsToUpdate.map(field => (data as Record<string, unknown>)[field]);
    values.push(now); // updatedAt
    values.push(eventId); // WHERE id = ?

    const sql = `UPDATE character_events SET ${setClause}, updatedAt = ? WHERE id = ?`;
    const info = await this.storage.run(sql, ...values);
    logger.info(`[CharacterStorage] Character event ${eventId} updated, changes: ${info.changes}`);
    return info.changes > 0;
  }

  async deleteCharacterEvent(eventId: string): Promise<boolean> {
    logger.info(`[CharacterStorage] Deleting character event ${eventId}`);
    const info = await this.storage.run('DELETE FROM character_events WHERE id = ?', eventId);
    logger.info(`[CharacterStorage] Character event ${eventId} deleted, changes: ${info.changes}`);
    return info.changes > 0;
  }

  // --- Character Attributes CRUD ---
  async createCharacterAttribute(characterId: string, attributeKey: string, attributeValue: string): Promise<CharacterAttribute> {
    logger.info(`[CharacterStorage] Creating character attribute ${attributeKey} for character ${characterId}`);
    const now = Date.now();
    const newAttribute: CharacterAttribute = {
      id: `attr-${now}-${Math.random().toString(36).substring(2, 8)}`,
      characterId,
      attributeKey,
      attributeValue,
      createdAt: now,
    };

    await this.storage.run(
      'INSERT INTO character_attributes (id, characterId, attributeKey, attributeValue, createdAt) VALUES (?, ?, ?, ?, ?)',
      newAttribute.id, newAttribute.characterId, newAttribute.attributeKey, newAttribute.attributeValue, newAttribute.createdAt
    );

    logger.info(`[CharacterStorage] Character attribute created with ID: ${newAttribute.id}`);
    return newAttribute;
  }

  async getCharacterAttributes(characterId: string): Promise<CharacterAttribute[]> {
    logger.info(`[CharacterStorage] Getting attributes for character ${characterId}`);
    return this.storage.all<CharacterAttribute>(
      'SELECT id, characterId, attributeKey, attributeValue, createdAt FROM character_attributes WHERE characterId = ? ORDER BY attributeKey ASC',
      characterId
    );
  }

  async updateCharacterAttribute(attributeId: string, attributeValue: string): Promise<boolean> {
    logger.info(`[CharacterStorage] Updating character attribute ${attributeId}`);
    const info = await this.storage.run(
      'UPDATE character_attributes SET attributeValue = ? WHERE id = ?',
      attributeValue, attributeId
    );
    logger.info(`[CharacterStorage] Character attribute ${attributeId} updated, changes: ${info.changes}`);
    return info.changes > 0;
  }

  async deleteCharacterAttribute(attributeId: string): Promise<boolean> {
    logger.info(`[CharacterStorage] Deleting character attribute ${attributeId}`);
    const info = await this.storage.run('DELETE FROM character_attributes WHERE id = ?', attributeId);
    logger.info(`[CharacterStorage] Character attribute ${attributeId} deleted, changes: ${info.changes}`);
    return info.changes > 0;
  }

  // --- Utility methods ---
  async getEventsByScene(sceneId: string): Promise<CharacterEvent[]> {
    logger.info(`[CharacterStorage] Getting character events for scene ${sceneId}`);
    return this.storage.all<CharacterEvent>(
      'SELECT id, characterId, bookId, relatedSceneId, timelinePosition, eventType, description, impact, createdAt, updatedAt FROM character_events WHERE relatedSceneId = ? ORDER BY timelinePosition ASC',
      sceneId
    );
  }

  async getEventsByTimelineRange(characterId: string, startPosition: number, endPosition: number): Promise<CharacterEvent[]> {
    logger.info(`[CharacterStorage] Getting character events for character ${characterId} in timeline range ${startPosition}-${endPosition}`);
    return this.storage.all<CharacterEvent>(
      'SELECT id, characterId, bookId, relatedSceneId, timelinePosition, eventType, description, impact, createdAt, updatedAt FROM character_events WHERE characterId = ? AND timelinePosition >= ? AND timelinePosition <= ? ORDER BY timelinePosition ASC',
      characterId, startPosition, endPosition
    );
  }
}
