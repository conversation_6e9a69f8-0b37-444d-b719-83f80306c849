import type {ExtensionContext} from '@main/extensions/extension.types';
// import { Disposable } from '@shared/types/common';; // Not used currently
import {logger} from '@main/services/logging.service';
import {CharacterStorage} from './storage';
import {registerCharacterCommands} from './commands';
import {registerCharacterIpcHandlers} from './ipcHandlers';
import {consistencyCheckTask} from './ai/consistency-check.task'; // Import AI task
import {EXTENSION_ID} from '../shared/constants/extension'; // Import from shared

export async function activate(context: ExtensionContext): Promise<void> {
  logger.info(`Activating extension: ${EXTENSION_ID}`);
  
  // Create storage instance
  // Assuming context.storage provides the StorageServiceAPI
  const characterStorage = new CharacterStorage(context.storage);
  
  // Гарантируем наличие схемы БД
  try {
    await characterStorage.ensureSchema();
  } catch (error) {
    logger.error(`[${EXTENSION_ID}] Failed to ensure database schema. Extension activation failed.`, error);
    return; // Прерываем активацию
  }
  
  // Register command handlers (definitions are in package.json)
  registerCharacterCommands(context, characterStorage);
  
  // Register IPC Handlers
  registerCharacterIpcHandlers(context, characterStorage);
  
  // Register AI Tasks
  if (context.ai) {
    logger.info(`Registering AI task: ${consistencyCheckTask.taskId}`);
    context.subscriptions.push(context.ai.registerTask(consistencyCheckTask));
  } else {
    logger.warn(`AI service not available, skipping AI task registration`);
  }
  
  // Note: Views, View Containers, Menus, and Editors are registered
  // via contributions in package.json and processed by ExtensionRegistry.
  // The disposables for these are automatically added to context.subscriptions
  // by the ExtensionRegistry.
  // AI Tasks are declared in package.json, but their implementations need to be
  // registered here via context.ai.registerTask.
  
  logger.info(`Extension ${EXTENSION_ID} activated successfully.`);
  
  // Возвращаем API расширения (если нужно)
  // Example: Provide a way for other extensions to get character data
  // return {
  //   getCharacterById: characterStorage.getCharacterById.bind(characterStorage),
  //   // Add getCharacterEvents etc. if needed
  // };
}

export function deactivate(): Promise<void> {
  logger.info(`Deactivating extension: ${EXTENSION_ID}`);
  // Очистка ресурсов происходит автоматически через context.subscriptions
  return Promise.resolve();
}
