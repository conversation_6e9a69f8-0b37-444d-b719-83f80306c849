import type {ExtensionContext} from '@main/extensions/extension.types';
import type {CharacterStorage} from './storage';
import * as commands from '../shared/constants/commands';
import * as ipcChannels from '../shared/constants/ipc-channels';
import {PROFILE_EDITOR} from '../shared/constants/editors';
import {CommandId as _CommandId, CommandIds as CoreCommandIds} from '@shared/constants/command-ids';

async function handleCreateCharacter(context: ExtensionContext, characterStorage: CharacterStorage) {
  const {logger, dialogs, ipc, commands: commandsService} = context; // Destructure from context
  logger.info(`[Characters:Command] Handling ${commands.CHARACTER_CREATE}`);
  try {
    const name = await dialogs.showInputDialog({title: 'Create Character', prompt: 'Enter character name:'});
    if (name) {
      const newCharacter = await characterStorage.create<PERSON>haracter(name);
      // Use context.ipc.send with the correct channel constant
      logger.info(`[Characters:Command] About to send ${ipcChannels.CHARACTERS_UPDATED_EVENT} event`);
      
      // Send a test event first to verify IPC communication
      logger.info(`[Characters:Command] Sending test event to verify IPC...`);
      ipc.send('test-event', { message: 'test message' });
      
      ipc.send(ipcChannels.CHARACTERS_UPDATED_EVENT, {}); // Send empty payload or relevant data
      logger.info(`[Characters:Command] Sent ${ipcChannels.CHARACTERS_UPDATED_EVENT} event after character creation`);
      logger.info(`[Characters:Command] Character created: ${newCharacter.id}`);
      
      // Open the profile editor for the new character directly using the core command
      try {
        const editorType = PROFILE_EDITOR;
        const title = `Character: ${newCharacter.name || newCharacter.id}`;
        await commandsService.executeCommand({
          commandId: CoreCommandIds.OPEN_EDITOR,
          args: {
            editorType: editorType,
            dataId: newCharacter.id,
            title: title,
            options: {preserveFocus: false}
          }
        });
        logger.info(`[Characters:Command] Requested editor open for new character ${newCharacter.id}`);
      } catch (openError) {
        logger.error(`[Characters:Command] Error opening editor for new character ${newCharacter.id}:`, openError);
        // Notify user about creation success but editor open failure?
        context.notifications.showWarning('Character created, but failed to open editor.');
      }
      
      return newCharacter;
    }
  } catch (error) {
    logger.error(`[Characters:Command] Error executing ${commands.CHARACTER_CREATE}:`, error);
    // Определяем сообщение или ошибку для уведомления
    const notificationMessage: string | Error = error instanceof Error ? error : 'Failed to create character.';
    context.notifications.showError(notificationMessage);
  }
  return null;
}

async function handleDeleteCharacter(context: ExtensionContext, characterStorage: CharacterStorage, characterId?: string) {
  const {logger, dialogs, ipc, notifications} = context; // Destructure from context
  logger.info(`[Characters:Command] Handling ${commands.CHARACTER_DELETE} for ID: ${characterId}`);
  if (!characterId) {
    logger.warn(`[Characters:Command] ${commands.CHARACTER_DELETE} called without characterId.`);
    notifications.showWarning('No character selected to delete.');
    return {success: false, error: 'No character ID provided.'};
  }
  
  // TODO: Get character name for a better confirmation message
  // const character = await characterStorage.getCharacterById(characterId);
  // const characterName = character ? character.name : `ID ${characterId}`;
  
  // Request confirmation
  const confirmation = await dialogs.showConfirmationDialog({
    type: 'warning',
    title: 'Confirm Character Deletion',
    message: `Are you sure you want to delete this character?`, // Generic message for now
    detail: `Character ID: ${characterId}\nThis action cannot be undone.`,
    buttons: ['Cancel', 'Delete'], // Keep Delete as the second button (index 1)
    defaultId: 0, // Index of the default button ('Cancel')
    cancelId: 0, // Index of the button that cancels ('Cancel')
  });
  
  if (confirmation.response !== 1) { // Assuming 'Delete' is the second button
    logger.info(`[Characters:Command] Character deletion cancelled by user.`);
    return {success: false, cancelled: true};
  }
  
  // User confirmed deletion
  try {
    const success = await characterStorage.deleteCharacter(characterId);
    if (success) {
      logger.info(`[Characters:Command] Character ${characterId} deleted successfully.`);
      ipc.send(ipcChannels.CHARACTERS_UPDATED_EVENT, {}); // Notify UI
      notifications.showInfo(`Character deleted.`);
      // TODO: Close editor if this character's profile was open?
      return {success: true};
    } else {
      logger.warn(`[Characters:Command] Character ${characterId} not found during deletion.`);
      notifications.showWarning('Character not found or could not be deleted.');
      return {success: false, error: 'Character not found.'};
    }
  } catch (error) {
    logger.error(`[Characters:Command] Error deleting character ${characterId}:`, error);
    const notificationMessage: string | Error = error instanceof Error ? error : 'Failed to delete character.';
    notifications.showError(notificationMessage);
    return {success: false, error: error instanceof Error ? error.message : String(error)};
  }
}

// Removed handleOpenCharacterProfile as it's now handled directly in Renderer

export function registerCharacterCommands(context: ExtensionContext, characterStorage: CharacterStorage): void {
  const {logger, subscriptions, commands: commandsService} = context; // Destructure from context
  logger.info(`[Characters] Registering Commands...`);
  
  subscriptions.push(
    commandsService.registerCommand({
      id: commands.CHARACTER_CREATE, // Cast to CommandId type
      title: 'Character: Create New',
      category: 'Character',
      handler: () => handleCreateCharacter(context, characterStorage)
    })
  );
  
  subscriptions.push(
    commandsService.registerCommand({
      id: commands.CHARACTER_DELETE, // Cast to CommandId type
      title: 'Character: Delete Selected',
      category: 'Character',
      handler: (args?: { characterId?: string } | string) => {
        const id = typeof args === 'string' ? args : args?.characterId;
        return handleDeleteCharacter(context, characterStorage, id);
      }
    })
  );
  
  // Removed registration for CHARACTER_OPEN_PROFILE
  
  // TODO: Register update command handler when implemented
  logger.info(`[Characters] Commands registered.`);
}
