import {CONSISTENCY_CHECK} from '../../shared/constants/ai-tasks';
import {AITaskDefinition} from "@shared/types/ai";

export const consistencyCheckTask: AITaskDefinition = {
  taskId: CONSISTENCY_CHECK,
  description: 'Checks scene text for consistency with character profile and events.',
  // TODO: Refine this prompt template
  promptTemplate: `
Context:
Character Name: {{characterName}}
Key Traits: {{characterTraits}}
Relevant Past Events (up to timeline position {{sceneTimelinePosition}}):
{{characterEventsSummary}}

Scene Text Snippet (or full scene):
\`\`\`
{{sceneContent}}
\`\`\`

Task:
Analyze the provided scene text snippet. Identify any actions, dialogue, or internal thoughts of {{characterName}} that seem inconsistent with their established Key Traits or Relevant Past Events listed above. Focus only on inconsistencies related to the provided character context.

Output JSON conforming to the schema provided. If no inconsistencies are found, return an empty array [].
    `.trim(),
  // Define the expected JSON output structure
  outputSchema: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        inconsistent_text: {
          type: 'string',
          description: 'The specific snippet from the scene text that appears inconsistent.'
        },
        explanation: {
          type: 'string',
          description: 'A brief explanation of why this text seems inconsistent with the character\'s profile or past events.'
        },
        context_violated: {
          type: 'string',
          description: 'The specific trait or event description from the context that the text contradicts.'
        },
        severity: {
          type: 'string',
          enum: ['low', 'medium', 'high'],
          description: 'An estimated severity of the inconsistency.'
        }
      },
      required: ['inconsistent_text', 'explanation', 'context_violated', 'severity']
    }
  },
  requiredContext: ['sceneContent', 'characterProfile', 'characterEvents', 'sceneTimelinePosition'] // Hints for AIService
};
