import {IpcErrorData} from '@shared/types/ipc';
import * as ipcChannels from '../../shared/constants/ipc-channels';
import {ExtensionContext} from "@main/extensions/extension.types";
import {EXTENSION_ID} from "@/extensions/books/shared/constants";
import {BookStorage} from "@/extensions/books/main/storage";
import {Book} from "@/extensions/books/shared/types";
import {BookCommandArgs} from '@shared/types/commands';

// --- Book Command Handlers ---

// Return type updated
export async function handleCreateNewBook(context: ExtensionContext, bookStorage: BookStorage): Promise<Book | null | IpcErrorData> {
  const {logger} = context;
  
  logger.info(`[${EXTENSION_ID}.bookCommands] Handling createNewBook`);
  const title = await context.dialogs.showInputDialog({
    title: "Create New Book",
    prompt: "Enter the title for the new book:",
    placeholder: "My Awesome Novel",
  });
  if (!title) {
    logger.info(`[${EXTENSION_ID}.bookCommands] Book creation cancelled by user.`);
    return null;
  }
  const trimmedTitle = title.trim();
  if (!trimmedTitle) {
    logger.info(`[${EXTENSION_ID}.bookCommands] Book creation cancelled due to empty title.`);
    return {code: 'validation_failed', message: 'Название книги не может быть пустым.'};
  }
  try {
    const newBook = await bookStorage.createBook(trimmedTitle);
    context.ipc.send(ipcChannels.BOOKS_UPDATED_EVENT, undefined);
    logger.info(`[${EXTENSION_ID}.bookCommands] Book created: ${newBook.id}`);
    return newBook;
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.bookCommands] Error creating book`, error);
    context.notifications.showError(`Не удалось создать книгу: ${message}`);
    return {code: 'storage_error', message: `Не удалось создать книгу: ${message}`};
  }
}

export async function handleRenameBook(context: ExtensionContext, bookStorage: BookStorage, cmdArgs: BookCommandArgs): Promise<{
  success: true
} | null | IpcErrorData> { // Use IpcErrorData
  const {logger} = context;
  const actualBookId = cmdArgs.bookId ?? context.context.getContext('activeBookId') as string | undefined;
  logger.info(`[${EXTENSION_ID}.bookCommands] Handling renameBook. BookId: ${actualBookId}, Received args:`, cmdArgs);
  if (!actualBookId) {
    const errorMsg = 'Не выбрана книга для переименования.';
    context.notifications.showError(errorMsg);
    return {code: 'validation_failed', message: errorMsg};
  }
  const currentBook = await bookStorage.getBookById(actualBookId);
  if (!currentBook) {
    const errorMsg = `Книга с ID ${actualBookId} не найдена.`;
    context.notifications.showError(errorMsg);
    return {code: 'not_found', message: errorMsg};
  }
  
  const newTitle = await context.dialogs.showInputDialog({
    title: "Rename Book",
    prompt: "Enter the new title for the book:",
    defaultValue: currentBook.title,
    placeholder: "My Awesome Novel",
  });
  if (!newTitle) {
    logger.info(`[${EXTENSION_ID}.bookCommands] Book rename cancelled by user.`);
    return null;
  }
  const trimmedTitle = newTitle.trim();
  if (!trimmedTitle) {
    logger.info(`[${EXTENSION_ID}.bookCommands] Book rename cancelled due to empty title.`);
    return {code: 'validation_failed', message: 'Название книги не может быть пустым.'};
  }
  if (trimmedTitle === currentBook.title) {
    logger.info(`[${EXTENSION_ID}.bookCommands] Book rename skipped, title unchanged.`);
    return null;
  }
  
  try {
    await bookStorage.renameBook(actualBookId, trimmedTitle);
    context.ipc.send(ipcChannels.BOOKS_UPDATED_EVENT, undefined); // Use context.ipc.send and constant
    context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: actualBookId}); // Убираем undefined
    context.notifications.showInfo(`Book renamed to "${trimmedTitle}" successfully.`);
    logger.info(`[${EXTENSION_ID}.bookCommands] Book renamed: ${actualBookId}`);
    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.bookCommands] Error renaming book`, error);
    context.notifications.showError(`Не удалось переименовать книгу: ${message}`);
    return {code: 'storage_error', message: `Не удалось переименовать книгу: ${message}`};
  }
}

// Return type updated
// Modify args handling
export async function handleDeleteBook(context: ExtensionContext, bookStorage: BookStorage, cmdArgs: BookCommandArgs): Promise<{
  success: true
} | null | IpcErrorData> { // Use IpcErrorData
  const {logger} = context;
  const actualBookId = cmdArgs.bookId ?? context.context.getContext('activeBookId') as string | undefined;
  logger.info(`[${EXTENSION_ID}.bookCommands] Handling deleteBook. BookId: ${actualBookId}, Received args:`, cmdArgs);
  if (!actualBookId) {
    const errorMsg = 'Не выбрана книга для удаления.';
    context.notifications.showError(errorMsg);
    return {code: 'validation_failed', message: errorMsg};
  }
  const currentBook = await bookStorage.getBookById(actualBookId);
  if (!currentBook) {
    logger.warn(`[${EXTENSION_ID}.bookCommands] Book ${actualBookId} not found for deletion.`);
    return {success: true}; // Consider success if not found
  }
  
  const confirmation = await context.dialogs.showConfirmationDialog({
    type: 'warning',
    title: 'Confirm Deletion',
    message: `Вы уверены, что хотите удалить книгу "${currentBook.title}"?`,
    detail: 'Это действие необратимо и удалит все главы, сцены и связанные файлы.',
    buttons: ['Отмена', 'Удалить'],
    defaultId: 1, // Default to Delete button
    cancelId: 0, // Cancel button index
  });
  
  if (confirmation.response !== 1) { // 1 is the index of 'Удалить'
    logger.info(`[${EXTENSION_ID}.bookCommands] Book deletion cancelled by user.`);
    return null; // Return null when cancelled
  }
  
  try {
    await bookStorage.deleteBook(actualBookId);
    context.ipc.send(ipcChannels.BOOKS_UPDATED_EVENT, undefined); // Use context.ipc.send and constant
    if (context.context.getContext('activeBookId') === actualBookId) {
      context.context.setContext('activeBookId', null);
      context.context.setContext('activeChapterId', null);
      context.context.setContext('activeSceneId', null);
    }
    context.notifications.showInfo(`Book "${currentBook.title}" deleted successfully.`);
    logger.info(`[${EXTENSION_ID}.bookCommands] Book deleted: ${actualBookId}`);
    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.bookCommands] Error deleting book`, error);
    context.notifications.showError(`Не удалось удалить книгу: ${message}`);
    return {code: 'storage_error', message: `Не удалось удалить книгу: ${message}`};
  }
}
