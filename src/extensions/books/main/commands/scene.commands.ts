import * as ipcChannels from '../../shared/constants/ipc-channels'; // Импорт каналов расширения
import {CommandIds as CoreCommandIds} from '@shared/constants/command-ids';
import {ExtensionContext} from "@main/extensions/extension.types"; // Импорт идентификаторов команд ядра
import {IpcErrorData} from '@shared/types/ipc';
import {BookStorage} from '../storage';
import {SceneService} from '../services/scene.service';
import {EXTENSION_ID, SCENE_EDITOR} from "@/extensions/books/shared/constants";
import {SceneCommandArgs} from '@shared/types/commands';
import {ReorderScenesArgs} from "@/extensions/books/shared/types";

export async function handleCreateScene(context: ExtensionContext, bookStorage: BookStorage, sceneService: SceneService, cmdArgs: SceneCommandArgs): Promise<{
  success: true,
  sceneId: string
} | IpcErrorData> { // Use IpcErrorData
  const {logger} = context;
  const chapterId = cmdArgs.chapterId;

  logger.info(`[${EXTENSION_ID}.sceneCommands] Handling createScene. ChapterId: ${chapterId}, Received args:`, cmdArgs);
  if (!chapterId) {
    const errorMsg = 'Не выбрана глава для добавления сцены. Выберите главу в проводнике.';
    context.notifications.showError(errorMsg);
    return {code: 'validation_failed', message: errorMsg};
  }

  const title = await context.dialogs.showInputDialog({
    title: "Create New Scene",
    prompt: "Enter the title for the new scene:",
    placeholder: "Scene Title",
  });

  if (!title) {
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene creation cancelled by user.`);
    return {code: 'cancelled', message: "Scene creation cancelled by user"};
  }

  const trimmedTitle = title.trim();
  if (!trimmedTitle) {
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene creation cancelled due to empty title.`);
    return {code: 'validation_failed', message: 'Название сцены не может быть пустым.'};
  }
  try {
    const position = await bookStorage.getNextScenePosition(chapterId);
    const newScene = await bookStorage.createScene(chapterId, trimmedTitle, position);
    const chapter = await bookStorage.getChapterById(chapterId);
    if (chapter) {
      context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: chapter.bookId});
    }
    context.notifications.showInfo(`Scene "${trimmedTitle}" created successfully.`);
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene created: ${newScene.id}`);
    await context.commands.executeCommand({
      commandId: CoreCommandIds.OPEN_EDITOR, args: {
        editorType: SCENE_EDITOR,
        dataId: newScene.id,
        title: newScene.title || 'Untitled Scene',
        options: {preserveFocus: false} // Фокусировка на новом редакторе
      }
    });

    return {success: true, sceneId: newScene.id};

  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.sceneCommands] Error creating scene`, error);
    context.notifications.showError(`Не удалось создать сцену: ${message}`);
    return {code: 'storage_error', message: `Не удалось создать сцену: ${message}`};
  }
}


export async function handleSaveScene(context: ExtensionContext): Promise<{ success: true } | IpcErrorData> {
  const {logger} = context;
  logger.info(`[${EXTENSION_ID}.sceneCommands] Command editor.action.saveScene triggered. Executing core command '${CoreCommandIds.TRIGGER_SAVE_ACTIVE_EDITOR}'.`);
  try {
    // Используем CommandService для выполнения команды ядра, которая запускает сохранение в рендерере
    await context.commands.executeCommand({
      commandId: CoreCommandIds.TRIGGER_SAVE_ACTIVE_EDITOR,
      args: {}
    });
    // Обработчик команды ядра отправит целевое событие в соответствующее окно рендерера.
    // Фактическое сохранение происходит асинхронно в рендерере через его собственный вызов IPC (ipcChannels.SAVE_SCENE_CONTENT).
    return {success: true};
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.sceneCommands] Error executing core command '${CoreCommandIds.TRIGGER_SAVE_ACTIVE_EDITOR}'`, error);
    context.notifications.showError(`Ошибка инициации сохранения: ${message}`);
    return {code: 'command_failed', message: `Ошибка инициации сохранения: ${message}`};
  }
}

export async function handleRenameScene(context: ExtensionContext, bookStorage: BookStorage, sceneService: SceneService, cmdArgs: SceneCommandArgs): Promise<{
  success: true
} | IpcErrorData> {
  const {logger} = context;
  const sceneId = cmdArgs.sceneId
  logger.info(`[${EXTENSION_ID}.sceneCommands] Handling renameScene. SceneId: ${sceneId}, Received args:`, cmdArgs);
  // Примечание: sceneService передается для потенциального переименования файла, хотя bookStorage.renameScene в настоящее время его не использует.
  if (!sceneId) {
    const errorMsg = 'Не выбрана сцена для переименования.';
    context.notifications.showError(errorMsg);
    return {code: 'validation_failed', message: errorMsg};
  }
  const currentScene = await bookStorage.getSceneMetadata(sceneId);
  if (!currentScene) {
    const errorMsg = `Сцена с ID ${sceneId} не найдена.`;
    context.notifications.showError(errorMsg);
    return {code: 'not_found', message: errorMsg};
  }

  const newTitle = await context.dialogs.showInputDialog({
    title: "Rename Scene",
    prompt: "Enter the new title for the scene:",
    defaultValue: currentScene.title,
    placeholder: "Scene Title",
  });

  if (!newTitle) {
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene rename cancelled by user.`);
    return {code: 'cancelled', message: "Scene rename cancelled by user."};
  }
  const trimmedTitle = newTitle.trim();
  if (!trimmedTitle) {
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene rename cancelled due to empty title.`);
    return {code: 'validation_failed', message: 'Название сцены не может быть пустым.'};
  }
  if (trimmedTitle === currentScene.title) {
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene rename skipped, title unchanged.`);
    return {code: 'cancelled', message: "Scene rename skipped, title unchanged."};
  }

  try {
    await bookStorage.renameScene(sceneId, trimmedTitle);
    const chapter = await bookStorage.getChapterById(currentScene.chapterId);
    if (chapter) {
      context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: chapter.bookId});
    }
    context.notifications.showInfo(`Scene renamed to "${trimmedTitle}" successfully.`);
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene renamed: ${sceneId}`);
    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.sceneCommands] Error renaming scene`, error);
    context.notifications.showError(`Не удалось переименовать сцену: ${message}`);
    return {code: 'storage_error', message: `Не удалось переименовать сцену: ${message}`};
  }
}

// Добавлен параметр sceneService
// Изменена обработка аргументов
export async function handleDeleteScene(context: ExtensionContext, bookStorage: BookStorage, sceneService: SceneService, ...cmdArgs: SceneCommandArgs[]): Promise<{
  success: true
} | IpcErrorData> { // Use IpcErrorData
  const {logger} = context;
  // Извлекаем потенциальный аргумент
  const arg = cmdArgs[0];
  // Correctly access sceneId from SceneCommandArgs
  const sceneIdFromArgs = (typeof arg === 'object' && arg !== null && typeof arg.sceneId === 'string' ? arg.sceneId : undefined);
  // Запасной вариант - использование контекста
  const sceneId = sceneIdFromArgs ?? context.context.getContext('activeSceneId') as string | undefined;
  logger.info(`[${EXTENSION_ID}.sceneCommands] Handling deleteScene. SceneId: ${sceneId}, Received args:`, cmdArgs);
  // Примечание: sceneService передается, но может не использоваться напрямую здесь, так как bookStorage.deleteScene обрабатывает его.
  if (!sceneId) {
    const errorMsg = 'Не выбрана сцена для удаления.';
    context.notifications.showError(errorMsg);
    return {code: 'validation_failed', message: errorMsg};
  }
  const currentScene = await bookStorage.getSceneMetadata(sceneId);
  if (!currentScene) {
    logger.warn(`[${EXTENSION_ID}.sceneCommands] Scene ${sceneId} not found for deletion.`);
    return {success: true};
  }

  const confirmation = await context.dialogs.showConfirmationDialog({
    type: 'warning',
    title: 'Confirm Deletion',
    message: `Вы уверены, что хотите удалить сцену "${currentScene.title}"?`,
    detail: 'Это действие необратимо и удалит файл сцены.',
    buttons: ['Отмена', 'Удалить'],
    defaultId: 1,
    cancelId: 0,
  });

  if (confirmation.response !== 1) {
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene deletion cancelled by user.`);
    return {code: 'cancelled', message: "Scene deletion cancelled by user"};
  }

  try {
    await bookStorage.deleteScene(sceneId);
    const chapter = await bookStorage.getChapterById(currentScene.chapterId);
    if (chapter) {
      context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: chapter.bookId});
    }
    if (context.context.getContext('activeSceneId') === sceneId) {
      context.context.setContext('activeSceneId', null);
    }
    context.notifications.showInfo(`Scene "${currentScene.title}" deleted successfully.`);
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene deleted: ${sceneId}`);
    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.sceneCommands] Error deleting scene`, error);
    context.notifications.showError(`Не удалось удалить сцену: ${message}`);
    return {code: 'storage_error', message: `Не удалось удалить сцену: ${message}`};
  }
}

export async function handleMoveScene(context: ExtensionContext, bookStorage: BookStorage, sceneService: SceneService, cmdArgs: SceneCommandArgs): Promise<{
  success: true
} | IpcErrorData> { // Use IpcErrorData
  const {logger, notifications, ipc} = context;
  logger.info(`[${EXTENSION_ID}.sceneCommands] Handling moveScene. Received args:`, cmdArgs);
  const {sceneId, chapterId, position} = cmdArgs;

  try {

    if (!sceneId) {
      notifications.showError('Scene ID is required.');
      return {code: 'invalid_args', message: 'Scene ID is required.'};
    }

    const originalScene = await bookStorage.getSceneMetadata(sceneId);
    if (!originalScene) {
      notifications.showError(`Original scene ${sceneId} not found.`);
      return {code: 'not_found', message: `Original scene ${sceneId} not found.`};
    }

    const originalChapter = await bookStorage.getChapterById(originalScene.chapterId);
    if (!originalChapter) {
      notifications.showError(`Original chapter ${originalScene.chapterId} not found.`);
      return {code: 'not_found', message: `Original chapter ${originalScene.chapterId} not found.`};
    }

    if (!chapterId) {
      notifications.showError('Chapter ID is required.');
      return {code: 'invalid_args', message: 'Chapter ID is required.'};
    }
    if (position === undefined || position === null) {
      notifications.showError('Position is required.');
      return {code: 'invalid_args', message: 'Position is required.'};
    }

    await bookStorage.moveScene(sceneId, chapterId, position);

    ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: originalChapter.bookId});
    notifications.showInfo(`Scene moved successfully.`);
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scene moved: ${sceneId}`);

    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.sceneCommands] Error moving scene`, error);
    context.notifications.showError(`Не удалось переместить сцену: ${message}`);
    return {code: 'storage_error', message: `Не удалось переместить сцену: ${message}`};
  }
}

// Изменена обработка аргументов (предполагается, что переупорядочивание вызывается только программно с правильным объектом)
export async function handleReorderScenes(context: ExtensionContext, bookStorage: BookStorage, cmdArgs: ReorderScenesArgs): Promise<{
  success: true
} | IpcErrorData> { // Use IpcErrorData
  const {logger} = context;
  logger.info(`[${EXTENSION_ID}.sceneCommands] Handling reorderScenes. Received args:`, cmdArgs);
  const {chapterId, orderedSceneIds} = cmdArgs;
  try {
    const chapter = await bookStorage.getChapterById(chapterId);
    if (!chapter) throw new Error(`Chapter ${chapterId} not found.`);

    await bookStorage.reorderScenes(chapterId, orderedSceneIds);
    context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: chapter.bookId});
    context.notifications.showInfo(`Scenes reordered successfully.`);
    logger.info(`[${EXTENSION_ID}.sceneCommands] Scenes reordered for chapter: ${chapterId}`);
    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.sceneCommands] Error reordering scenes`, error);
    context.notifications.showError(`Не удалось пересортировать сцены: ${message}`);
    return {code: 'storage_error', message: `Не удалось пересортировать сцены: ${message}`};
  }
}
