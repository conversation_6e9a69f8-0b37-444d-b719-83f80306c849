import * as ipcChannels from '../../shared/constants/ipc-channels';
import {ExtensionContext} from "@main/extensions/extension.types";
import {IpcErrorData} from '@shared/types/ipc';
import {Chapter} from "@/extensions/books/shared/types";
import {BookStorage} from '../storage';
import {EXTENSION_ID} from '../../shared/constants';
import {SceneService} from "@/extensions/books/main/services/scene.service";
import {ChapterCommandArgs} from '@shared/types/commands';

export async function handleCreateChapter(context: ExtensionContext, bookStorage: BookStorage, cmdArgs: ChapterCommandArgs): Promise<Chapter | null | IpcErrorData> { // Use IpcErrorData
  const {logger} = context;
  const actualBookId = cmdArgs.bookId ?? context.context.getContext('activeBookId') as string | undefined;
  logger.info(`[${EXTENSION_ID}.chapterCommands] Handling createChapter. BookId: ${actualBookId}, Received args:`, cmdArgs);
  
  if (!actualBookId) {
    const errorMsg = 'Не выбрана книга для добавления главы. Выберите книгу в проводнике.';
    context.notifications.showError(errorMsg);
    return {code: 'validation_failed', message: errorMsg};
  }
  
  const title = await context.dialogs.showInputDialog({
    title: "Create New Chapter",
    prompt: "Enter the title for the new chapter:",
    placeholder: "Chapter Title",
  });
  
  if (!title) {
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter creation cancelled by user.`);
    return null;
  }
  const trimmedTitle = title.trim();
  if (!trimmedTitle) {
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter creation cancelled due to empty title.`);
    return {code: 'validation_failed', message: 'Название главы не может быть пустым.'};
  }
  try {
    const position = await bookStorage.getNextChapterPosition(actualBookId);
    const newChapter = await bookStorage.createChapter(actualBookId, trimmedTitle, position);
    context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: actualBookId});
    context.notifications.showInfo(`Chapter "${trimmedTitle}" created successfully.`);
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter created: ${newChapter.id}`);
    return newChapter;
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.chapterCommands] Error creating chapter`, error);
    context.notifications.showError(`Не удалось создать главу: ${message}`);
    return {code: 'storage_error', message: `Не удалось создать главу: ${message}`};
  }
}

export async function handleRenameChapter(context: ExtensionContext, bookStorage: BookStorage, cmdArgs: ChapterCommandArgs): Promise<{
  success: true
} | null | IpcErrorData> {
  const {logger} = context;
  
  const actualChapterId = cmdArgs.chapterId ?? context.context.getContext('activeChapterId') as string | undefined;
  logger.info(`[${EXTENSION_ID}.chapterCommands] Handling renameChapter. ChapterId: ${actualChapterId}, Received args:`, cmdArgs);
  if (!actualChapterId) {
    const errorMsg = 'Не выбрана глава для переименования.';
    context.notifications.showError(errorMsg);
    return {code: 'validation_failed', message: errorMsg};
  }
  const currentChapter = await bookStorage.getChapterById(actualChapterId);
  if (!currentChapter) {
    const errorMsg = `Глава с ID ${actualChapterId} не найдена.`;
    context.notifications.showError(errorMsg);
    return {code: 'not_found', message: errorMsg};
  }
  
  const newTitle = await context.dialogs.showInputDialog({
    title: "Rename Chapter",
    prompt: "Enter the new title for the chapter:",
    defaultValue: currentChapter.title,
    placeholder: "Chapter Title",
  });
  if (!newTitle) {
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter rename cancelled by user.`);
    return null;
  }
  const trimmedTitle = newTitle.trim();
  if (!trimmedTitle) {
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter rename cancelled due to empty title.`);
    return {code: 'validation_failed', message: 'Название главы не может быть пустым.'};
  }
  if (trimmedTitle === currentChapter.title) {
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter rename skipped, title unchanged.`);
    return null;
  }
  
  try {
    await bookStorage.renameChapter(actualChapterId, trimmedTitle);
    context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: currentChapter.bookId}); // Убираем undefined
    context.notifications.showInfo(`Chapter renamed to "${trimmedTitle}" successfully.`);
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter renamed: ${actualChapterId}`);
    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.chapterCommands] Error renaming chapter`, error);
    context.notifications.showError(`Не удалось переименовать главу: ${message}`);
    return {code: 'storage_error', message: `Не удалось переименовать главу: ${message}`};
  }
}

export async function handleDeleteChapter(context: ExtensionContext, bookStorage: BookStorage, sceneService: SceneService, cmdArgs: ChapterCommandArgs): Promise<{
  success: true
} | null | IpcErrorData> { // Use IpcErrorData
  const {logger} = context;
  const actualChapterId = cmdArgs.chapterId ?? context.context.getContext('activeChapterId') as string | undefined;
  logger.info(`[${EXTENSION_ID}.chapterCommands] Handling deleteChapter. ChapterId: ${actualChapterId}, Received args:`, cmdArgs);
  // Note: sceneService is passed but might not be directly used here, as bookStorage.deleteChapter handles it.
  if (!actualChapterId) {
    const errorMsg = 'Не выбрана глава для удаления.';
    context.notifications.showError(errorMsg);
    return {code: 'validation_failed', message: errorMsg};
  }
  const currentChapter = await bookStorage.getChapterById(actualChapterId);
  if (!currentChapter) {
    logger.warn(`[${EXTENSION_ID}.chapterCommands] Chapter ${actualChapterId} not found for deletion.`);
    return {success: true};
  }
  
  const confirmation = await context.dialogs.showConfirmationDialog({
    type: 'warning',
    title: 'Confirm Deletion',
    message: `Вы уверены, что хотите удалить главу "${currentChapter.title}"?`,
    detail: 'Это действие необратимо и удалит все сцены внутри этой главы и их файлы.',
    buttons: ['Отмена', 'Удалить'],
    defaultId: 1,
    cancelId: 0,
  });
  
  if (confirmation.response !== 1) {
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter deletion cancelled by user.`);
    return null;
  }
  
  try {
    await bookStorage.deleteChapter(actualChapterId);
    context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId: currentChapter.bookId}); // Убираем undefined
    if (context.context.getContext('activeChapterId') === actualChapterId) {
      context.context.setContext('activeChapterId', null);
      context.context.setContext('activeSceneId', null);
    }
    context.notifications.showInfo(`Chapter "${currentChapter.title}" deleted successfully.`);
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter deleted: ${actualChapterId}`);
    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.chapterCommands] Error deleting chapter`, error);
    context.notifications.showError(`Не удалось удалить главу: ${message}`);
    return {code: 'storage_error', message: `Не удалось удалить главу: ${message}`};
  }
}

export async function handleMoveChapter(context: ExtensionContext, bookStorage: BookStorage, sceneService: SceneService, cmdArgs: ChapterCommandArgs): Promise<{
  success: true
} | IpcErrorData> { // Use IpcErrorData
  const {logger} = context;
  logger.info(`[${EXTENSION_ID}.chapterCommands] Handling moveChapter. Received args:`, cmdArgs);
  const {chapterId, bookId, position} = cmdArgs;
  // Removed redundant validation
  try {
    if (!chapterId) {
      context.notifications.showError('Chapter ID is required.');
      return {code: 'invalid_args', message: 'Chapter ID is required.'};
    }
    if (!bookId) {
      context.notifications.showError('Book ID is required.');
      return {code: 'invalid_args', message: 'Book ID is required.'};
    }
    if (position === undefined || position === null) {
      context.notifications.showError('Position is required.');
      return {code: 'invalid_args', message: 'Position is required.'};
    }

    const originalChapter = await bookStorage.getChapterById(chapterId);
    if (!originalChapter) {
      context.notifications.showError(`Original chapter ${chapterId} not found.`);
      return {code: 'not_found', message: `Original chapter ${chapterId} not found.`};
    }
    
    await bookStorage.moveChapter(chapterId, bookId, position);
    
    if (originalChapter.bookId !== bookId) {
      context.ipc.send(ipcChannels.STRUCTURE_UPDATED_EVENT, {bookId});
    }
    context.notifications.showInfo(`Chapter moved successfully.`);
    logger.info(`[${EXTENSION_ID}.chapterCommands] Chapter moved: ${chapterId}`);
    return {success: true};
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`[${EXTENSION_ID}.chapterCommands] Error moving chapter`, error);
    context.notifications.showError(`Не удалось переместить главу: ${message}`);
    return {code: 'storage_error', message: `Не удалось переместить главу: ${message}`};
  }
}

export async function handleReorderChapters(_context: ExtensionContext, _bookStorage: BookStorage, _cmdArgs: ChapterCommandArgs): Promise<{
  success: true
} | IpcErrorData> {
  // TODO: implement
  return {success: true};
}
