import { EXTENSION_ID } from '../shared/constants/extension';
import {ExtensionContext} from "@main/extensions/extension.types";
export function registerSettings(context: ExtensionContext): void {
    const { subscriptions, settings, logger } = context;
        logger.info(`[${EXTENSION_ID}] Registering Settings...`);

    // Register settings
    subscriptions.push(
        settings.registerSetting({
            id: 'books.autoSave',
            type: 'boolean',
            default: true,
            label: 'Auto Save',
            description: 'Automatically save changes to your book.',
            scope: 'user',
        }),
        settings.registerSetting({
            id: 'books.enableAutoSync',
            type: 'boolean',
            default: false,
            label: 'Enable Auto Sync',
            description: 'Automatically sync your books with the cloud.',
            scope: 'user',
        }),
        settings.registerSetting({
            id: 'books.defaultFontSize',
            type: 'number',
            default: 14,
            label: 'Default Font Size',
            description: 'The default font size for the book editor.',
            scope: 'user',
        }),
        settings.registerSetting({
            id: 'books.explorer.defaultSortOrder',
            label: 'Default Sort Order', // Добавляем label
            description: 'Default sort order for books in the explorer view.',
            type: 'enum',
            scope: 'user',
            default: 'createdAt',
            enum: ['createdAt', 'title'],
            enumDescriptions: ['Sort by creation date (newest first)', 'Sort alphabetically by title']
        }),
        context.settings.registerSetting({
            id: 'books.editor.showWordCount',
            label: 'Editor: Show Word Count', // Добавляем label
            description: 'Show word count indicator in the scene editor status bar.',
            type: 'boolean',
            scope: 'user',
            default: true
        })
    );
}
