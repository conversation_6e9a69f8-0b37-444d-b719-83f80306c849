import {
  handleCreate<PERSON><PERSON>pter,
  handleD<PERSON>te<PERSON>hapter,
  handleRenameChapter,
  handleReorderChapters
} from "@/extensions/books/main/commands/chapter.commands";
import {handleCreateNewBook, handleDeleteBook, handleRenameBook} from "@/extensions/books/main/commands/book.commands";
import {ExtensionContext} from "@main/extensions/extension.types";
import {BookStorage} from "@/extensions/books/main/storage";
import {SceneService} from "@/extensions/books/main/services/scene.service";
import {
  handleCreateScene,
  handleDeleteScene,
  handleMoveScene,
  handleRenameScene,
  handleReorderScenes,
  handleSaveScene
} from "@/extensions/books/main/commands/scene.commands";
import * as commands from "@/extensions/books/shared/constants/commands";
// Import CommandId and CommandArgs explicitly
import {BookCommandArgs, ChapterCommandArgs, CommandArgs, SceneCommandArgs} from '@shared/types/commands';
import {ReorderScenesArgs} from "@/extensions/books/shared/types";
import {IpcErrorData} from "@shared/types/ipc";

export function registerBookCommands(context: ExtensionContext, bookStorage: BookStorage, sceneService: SceneService): void {
  const {subscriptions, logger} = context;
  const commandService = context.commands;
  const scopedLogger = logger.createScopedLogger('BooksExtensionCommands');
  scopedLogger.info(`Registering Commands...`);
  
  // Helper function for argument validation and casting
  const createHandler = <T>(
    handlerFunc: (specificArgs: T) => unknown,
    requiredArgsCheck?: (args: T) => boolean // Optional check for required fields
  ) => {
    return (args?: CommandArgs) => {
      if (!args) {
        return Promise.resolve({
          success: false,
          error: {code: 'INVALID_ARGS', message: 'Missing required arguments'} as IpcErrorData
        });
      }
      const specificArgs = args as T;
      if (requiredArgsCheck && !requiredArgsCheck(specificArgs)) {
        return Promise.resolve({
          success: false,
          error: {code: 'INVALID_ARGS', message: 'Invalid arguments provided'} as IpcErrorData
        });
      }
      return handlerFunc(specificArgs);
    };
  };
  
  // --- Book Commands ---
  subscriptions.push(
    commandService.registerCommand({
      id: commands.CREATE_BOOK,
      title: 'Create New Book',
      category: 'Book',
      handler: () => handleCreateNewBook(context, bookStorage),
      keybinding: ['Cmd+N', 'Ctrl+N']
    })
  );
  subscriptions.push(
    commandService.registerCommand({
      id: commands.RENAME_BOOK,
      title: 'Rename Book',
      category: 'Book',
      showInPalette: false,
      // Use helper to wrap handler
      handler: createHandler<BookCommandArgs>(
        (args) => handleRenameBook(context, bookStorage, args),
        (args) => !!args.bookId && !!args.title // Example validation
      )
    })
  );
  subscriptions.push(
    commandService.registerCommand({
      id: commands.DELETE_BOOK, // Cast ID
      title: 'Delete Book',
      category: 'Book',
      showInPalette: false,
      // Use helper to wrap handler
      handler: createHandler<BookCommandArgs>(
        (args) => handleDeleteBook(context, bookStorage, args),
        (args) => !!args.bookId // Example validation
      )
    })
  );
  
  // --- Chapter Commands ---
  subscriptions.push(
    commandService.registerCommand({
      id: commands.CREATE_CHAPTER, // Cast ID
      title: 'Create New Chapter',
      category: 'Book',
      showInPalette: false,
      // Use helper to wrap handler
      handler: createHandler<ChapterCommandArgs>(
        (args) => handleCreateChapter(context, bookStorage, args),
        (args) => !!args.bookId
      ),
      keybinding: 'CmdOrCtrl+Alt+C'
    })
  );
  subscriptions.push(
    commandService.registerCommand({
      id: commands.RENAME_CHAPTER, // Cast ID
      title: 'Rename Chapter',
      category: 'Book',
      handler: createHandler<ChapterCommandArgs>(
        (args) => handleRenameChapter(context, bookStorage, args)
      )
    })
  );
  subscriptions.push(
    commandService.registerCommand({
      id: commands.DELETE_CHAPTER, // Cast ID
      title: 'Delete Chapter',
      category: 'Book',
      showInPalette: false,
      handler: createHandler<ChapterCommandArgs>(
        (specificArgs) => handleDeleteChapter(context, bookStorage, sceneService, specificArgs),
        (args) => !!args.bookId && !!args.chapterId // Example validation
      )
    })
  );
  
  subscriptions.push(
    commandService.registerCommand({
      id: commands.REORDER_CHAPTERS, // Cast ID
      title: 'Reorder Chapters',
      showInPalette: false,
      handler: createHandler<ChapterCommandArgs>( // Adjust type if needed
        (specificArgs) => handleReorderChapters(context, bookStorage, specificArgs),
      )
    })
  );
  // --- Scene Commands ---
  subscriptions.push(
    commandService.registerCommand({
      id: commands.CREATE_SCENE, // Cast ID
      title: 'Create New Scene',
      category: 'Book',
      showInPalette: false,
      // Use helper to wrap handler
      handler: createHandler<SceneCommandArgs>(
        (specificArgs) => handleCreateScene(context, bookStorage, sceneService, specificArgs),
      )
    })
  );
  
  subscriptions.push(
    commandService.registerCommand({
      id: commands.SAVE_SCENE, // Cast ID
      title: 'Save Scene',
      category: 'Editor',
      showInPalette: false,
      // This handler doesn't seem to use args directly, but let's keep the pattern for consistency or future use
      handler: (_args?: CommandArgs) => handleSaveScene(context), // Pass context, args might be needed later for specific save options
      keybinding: ['Cmd+S', 'Ctrl+S']
    })
  );
  subscriptions.push(
    commandService.registerCommand({
      id: commands.RENAME_SCENE, // Cast ID
      title: 'Rename Scene',
      category: 'Book',
      showInPalette: false,
      // Use helper to wrap handler
      handler: createHandler<SceneCommandArgs>(
        (specificArgs) => handleRenameScene(context, bookStorage, sceneService, specificArgs),
      )
    })
  );
  subscriptions.push(
    commandService.registerCommand({
      id: commands.DELETE_SCENE, // Cast ID
      title: 'Delete Scene',
      category: 'Book',
      showInPalette: false,
      // Use helper to wrap handler
      handler: createHandler<SceneCommandArgs>(
        (specificArgs) => handleDeleteScene(context, bookStorage, sceneService, specificArgs),
      )
    })
  );
  subscriptions.push(
    commandService.registerCommand({
      id: commands.MOVE_SCENE, // Cast ID
      title: 'Move Scene',
      showInPalette: false,
      // Use helper to wrap handler
      handler: createHandler<SceneCommandArgs>( // Assuming SceneCommandArgs has target chapterId and position
        (specificArgs) => handleMoveScene(context, bookStorage, sceneService, specificArgs),
      )
    })
  );
  subscriptions.push(
    commandService.registerCommand({
      id: commands.REORDER_SCENES, // Cast ID
      title: 'Reorder Scenes',
      showInPalette: false,
      // Use helper to wrap handler
      handler: createHandler<ReorderScenesArgs>( // Use the specific ReorderScenesArgs type
        (specificArgs) => handleReorderScenes(context, bookStorage, specificArgs),
      )
    })
  );
  
  scopedLogger.info(`All book commands registered.`);
}
