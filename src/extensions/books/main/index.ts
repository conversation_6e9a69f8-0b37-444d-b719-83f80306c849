import {registerBookCommands} from './commands';
import {registerBookIpcHandlers} from './ipc/handlers';
import {BookStorage} from './storage';
import {SceneService} from './services/scene.service';
import {registerSettings} from './settings';
import {ExtensionContext} from "@main/extensions/extension.types";
import {autocompleteTask} from './ai/autocomplete.task';
import {EXTENSION_ID} from '../shared/constants/extension';

export async function activate(context: ExtensionContext): Promise<void> {
  const {settings, git, logger} = context;
  logger.info(`Activating extension: ${EXTENSION_ID}`);
  
  const sceneService = new SceneService(settings, git, logger);
  const bookStorage = new BookStorage(context.storage, sceneService);
  
  // Ensure settings service is available
  if (!settings) {
    logger.error(`[${EXTENSION_ID}] SettingsService not found in context. Activation failed.`);
    context.notifications?.showError('Critical error: Settings service is unavailable.');
    return;
  }
  
  try {
    await bookStorage.ensureSchema();
  } catch (error) {
    logger.error(`[${EXTENSION_ID}] Failed to ensure database schema. Extension activation failed.`, error);
    context.notifications?.showError(`Failed to initialize book database: ${error instanceof Error ? error.message : String(error)}`);
    return; // Прерываем активацию, если схема не создана
  }
  
  // Register command handlers (definitions are in package.json)
  registerBookCommands(context, bookStorage, sceneService);
  
  // Register IPC Handlers
  registerBookIpcHandlers(context, bookStorage);
  
  // Register settings
  registerSettings(context);
  
  // Регистрируем AI-задачу автодополнения
  if (context.ai) {
    logger.info(`Registering AI task: ${autocompleteTask.taskId}`);
    context.subscriptions.push(context.ai.registerTask(autocompleteTask));
  } else {
    logger.warn(`AI service not available, skipping AI task registration`);
  }
  logger.info(`Extension ${EXTENSION_ID} activated successfully.`);
}

export function deactivate(): Promise<void> {
  // Очистка ресурсов происходит автоматически через context.subscriptions
  return Promise.resolve();
}
