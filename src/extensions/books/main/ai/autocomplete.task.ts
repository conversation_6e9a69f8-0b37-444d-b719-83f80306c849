import {AUTOCOMPLETE_SCENE} from '../../shared/constants/ai-tasks';
import {AITaskDefinition} from "@shared/types/ai";

export const autocompleteTask: AITaskDefinition = {
  taskId: AUTOCOMPLETE_SCENE,
  description: 'Continues writing the scene text based on the preceding content.',
  // Simple prompt for continuation
  promptTemplate: `Continue writing the following text:\n\n{{textBeforeCursor}}`,
  // Output schema is simple text, so we can use a basic schema or even omit for non-structured streaming
  // For consistency, let's define a simple schema expecting a string completion.
  outputSchema: {
    type: 'object', // Even for streaming, the initial setup might expect an object schema
    properties: {
      completion: {
        type: 'string',
        description: 'The generated continuation of the text.'
      }
    },
    // required: ['completion'] // Not strictly required if we just stream text
  },
  requiredContext: ['textBeforeCursor'] // The context needed from the caller
};
