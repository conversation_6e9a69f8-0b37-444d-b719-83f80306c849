import type {Book, BookStructure, Chapter, ChapterInfo, Scene, SceneInfo} from '../shared/types';
import {logger} from '@main/services/logging.service';
import {SceneService} from './services/scene.service';
import {StorageServiceAPI} from '@main/services/storage.service';

// Класс для работы с хранилищем метаданных книг в БД
export class BookStorage {
  private storage: StorageServiceAPI;
  private sceneService: SceneService; // Add SceneService reference
  
  constructor(
    storageAPI: StorageServiceAPI,
    sceneService: SceneService // Inject SceneService
  ) {
    this.storage = storageAPI;
    this.sceneService = sceneService; // Store SceneService instance
    logger.info(`[BookStorage] Initialized.`);
  }
  
  // --- Schema ---
  async ensureSchema(): Promise<void> {
    logger.info('[BookStorage] Ensuring schema...');
    const createBooksTable = `
        CREATE TABLE IF NOT EXISTS books
        (
            id
            TEXT
            PRIMARY
            KEY
            NOT
            NULL,
            title
            TEXT
            NOT
            NULL,
            createdAt
            INTEGER
            NOT
            NULL
        );
    `;
    const createChaptersTable = `
        CREATE TABLE IF NOT EXISTS chapters
        (
            id
            TEXT
            PRIMARY
            KEY
            NOT
            NULL,
            title
            TEXT
            NOT
            NULL,
            bookId
            TEXT
            NOT
            NULL,
            position
            INTEGER
            NOT
            NULL
            DEFAULT
            0,
            FOREIGN
            KEY
        (
            bookId
        ) REFERENCES books
        (
            id
        ) ON DELETE CASCADE
            );
    `;
    const createScenesTable = `
        CREATE TABLE IF NOT EXISTS scenes
        (
            id
            TEXT
            PRIMARY
            KEY
            NOT
            NULL,
            title
            TEXT
            NOT
            NULL,
            jsonFilePath
            TEXT
            NOT
            NULL
            UNIQUE, -- Store path to JSON state file
            chapterId
            TEXT
            NOT
            NULL,
            position
            INTEGER
            NOT
            NULL
            DEFAULT
            0,
            FOREIGN
            KEY
        (
            chapterId
        ) REFERENCES chapters
        (
            id
        ) ON DELETE CASCADE
            );
    `;
    // Add history table
    const createHistoryTable = `
        CREATE TABLE IF NOT EXISTS history_log
        (
            id
            INTEGER
            PRIMARY
            KEY
            AUTOINCREMENT,
            timestamp
            INTEGER
            NOT
            NULL,
            entityType
            TEXT
            NOT
            NULL, -- 'book', 'chapter', 'scene'
            entityId
            TEXT
            NOT
            NULL,
            action
            TEXT
            NOT
            NULL, -- 'update', 'delete'
            previousState
            TEXT  -- JSON string of the state before the action
        );
    `;
    // Используем транзакцию для создания таблиц
    await this.storage.transaction(() => {
      this.storage.run(createBooksTable);
      this.storage.run(createChaptersTable);
      this.storage.run(createScenesTable);
      this.storage.run(createHistoryTable); // Create history table
    });
    logger.info('[BookStorage] Schema ensured.');
  }
  
  // --- Books ---
  async getBooks(): Promise<Book[]> {
    logger.info('[BookStorage] Getting all books');
    return this.storage.all<Book>('SELECT id, title, createdAt FROM books ORDER BY createdAt DESC');
  }
  
  async getBookById(id: string): Promise<Book | null> {
    logger.info(`[BookStorage] Getting book by ID ${id}`);
    const book = await this.storage.get<Book>('SELECT id, title, createdAt FROM books WHERE id = ?', id);
    return book || null;
  }
  
  async createBook(title: string): Promise<Book> {
    logger.info(`[BookStorage] Creating book with title "${title}"`);
    const newBook: Book = {
      id: `book-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      title,
      createdAt: Date.now(),
    };
    await this.storage.run('INSERT INTO books (id, title, createdAt) VALUES (?, ?, ?)',
      newBook.id, newBook.title, newBook.createdAt
    );
    logger.info(`[BookStorage] Book created with ID: ${newBook.id}`);
    // Initialize Git repo using SceneService
    await this.sceneService.initRepoForBook(newBook.id);
    return newBook;
  }
  
  async deleteBook(bookId: string): Promise<void> {
    logger.info(`[BookStorage] Deleting book ${bookId}`);
    // Removed bookPath calculation
    
    const currentBook = await this.getBookById(bookId); // Get state before deleting
    try {
      // Use transaction to ensure atomicity between DB and file system operations
      await this.storage.transaction(async () => {
        // 1. Log history *before* deleting
        if (currentBook) {
          await this.logHistory('book', bookId, 'delete', currentBook);
        }
        // 2. Delete book record from DB (CASCADE should handle chapters and scenes)
        const info = await this.storage.run('DELETE FROM books WHERE id = ?', bookId);
        if (info.changes === 0 && currentBook) {
          logger.warn(`[BookStorage] Book record with ID ${bookId} not found in DB during deletion, though it existed before.`);
        } else if (info.changes > 0) {
          logger.info(`[BookStorage] Deleted book record from DB: ${bookId}`);
        }
        
        // 3. Delete the project directory using SceneService
        await this.sceneService.deleteBookDir(bookId);
        
        // 4. Git cleanup is handled within sceneService.deleteBookDir implicitly
      });
      logger.info(`[BookStorage] Book ${bookId} deleted successfully.`);
    } catch (error) {
      logger.error(`[BookStorage] Error deleting book ${bookId}`, error);
      throw error; // Rethrow error after rollback
    }
  }
  
  async renameChapter(chapterId: string, newTitle: string): Promise<void> {
    const trimmedTitle = newTitle.trim();
    if (!trimmedTitle) {
      throw new Error('Chapter title cannot be empty.');
    }
    logger.info(`[BookStorage] Renaming chapter ${chapterId} to "${trimmedTitle}"`);
    const currentChapter = await this.getChapterById(chapterId); // Get state before update
    if (!currentChapter) {
      throw new Error(`Chapter with ID ${chapterId} not found for renaming.`);
    }
    try {
      // Log history *before* update
      await this.logHistory('chapter', chapterId, 'update', currentChapter);
      await this.storage.run('UPDATE chapters SET title = ? WHERE id = ?', trimmedTitle, chapterId);
      logger.info(`[BookStorage] Renamed chapter ${chapterId}`);
    } catch (error) {
      logger.error(`[BookStorage] Error renaming chapter ${chapterId}`, error);
      throw error;
    }
  }
  
  async renameScene(sceneId: string, newTitle: string): Promise<void> {
    const trimmedTitle = newTitle.trim();
    if (!trimmedTitle) {
      throw new Error('Scene title cannot be empty.');
    }
    logger.info(`[BookStorage] Renaming scene ${sceneId} to "${trimmedTitle}"`);
    const currentScene = await this.getSceneMetadata(sceneId); // Get state before update
    if (!currentScene) {
      throw new Error(`Scene with ID ${sceneId} not found for renaming.`);
    }
    try {
      // 1. Log history *before* updating
      await this.logHistory('scene', sceneId, 'update', currentScene);
      // 2. Update title in DB
      await this.storage.run('UPDATE scenes SET title = ? WHERE id = ?', trimmedTitle, sceneId);
      logger.info(`[BookStorage] Renamed scene ${sceneId} in DB`);
      
      // 3. Rename the file using SceneService (Optional - implement in SceneService if needed)
      // const chapter = await this.getChapterById(currentScene.chapterId);
      // if (chapter) {
      //     await this.sceneService.renameSceneFile(chapter.bookId, currentScene.chapterId, sceneId, trimmedTitle); // Example call
      // }
      // logger.info(`[BookStorage] TODO: Implement file renaming in SceneService and call it here.`);
      
    } catch (error) {
      logger.error(`[BookStorage] Error renaming scene ${sceneId}`, error);
      throw error;
    }
  }
  
  async renameBook(bookId: string, newTitle: string): Promise<void> {
    const trimmedTitle = newTitle.trim();
    if (!trimmedTitle) {
      throw new Error('Book title cannot be empty.');
    }
    logger.info(`[BookStorage] Renaming book ${bookId} to "${trimmedTitle}"`);
    const currentBook = await this.getBookById(bookId); // Get state before update
    if (!currentBook) {
      throw new Error(`Book with ID ${bookId} not found for renaming.`);
    }
    try {
      // Log history *before* update
      await this.logHistory('book', bookId, 'update', currentBook);
      await this.storage.run('UPDATE books SET title = ? WHERE id = ?', trimmedTitle, bookId);
      logger.info(`[BookStorage] Renamed book ${bookId}`);
    } catch (error) {
      logger.error(`[BookStorage] Error renaming book ${bookId}`, error);
      throw error;
    }
  }
  
  // --- Chapters ---
  async getChapterById(id: string): Promise<Chapter | null> {
    logger.info(`[BookStorage] Getting chapter by ID ${id}`);
    const chapter = await this.storage.get<Chapter>('SELECT id, title, bookId, position FROM chapters WHERE id = ?', id);
    return chapter || null;
  }
  
  async getNextChapterPosition(bookId: string): Promise<number> {
    logger.info(`[BookStorage] Getting next chapter position for book ${bookId}`);
    const result = await this.storage.get<{
      maxPosition: number | null
    }>('SELECT MAX(position) as maxPosition FROM chapters WHERE bookId = ?', bookId);
    return (result?.maxPosition ?? -1) + 1;
  }
  
  async createChapter(bookId: string, title: string, position: number): Promise<Chapter> {
    logger.info(`[BookStorage] Creating chapter "${title}" for book ${bookId}`);
    const newChapter: Chapter = {
      id: `ch-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      title,
      bookId,
      position,
    };
    await this.storage.run('INSERT INTO chapters (id, title, bookId, position) VALUES (?, ?, ?, ?)',
      newChapter.id, newChapter.title, newChapter.bookId, newChapter.position
    );
    logger.info(`[BookStorage] Chapter created with ID: ${newChapter.id}`);
    return newChapter;
  }
  
  async deleteChapter(chapterId: string): Promise<void> {
    logger.info(`[BookStorage] Deleting chapter ${chapterId}`);
    const chapter = await this.getChapterById(chapterId);
    if (!chapter) {
      logger.warn(`[BookStorage] Chapter metadata not found for deletion: ${chapterId}`);
      return;
    }
    const bookId = chapter.bookId;
    // Removed repoPath, chapterSceneDir calculation
    
    // Get list of scene IDs to delete their files *before* deleting DB records
    const scenesToDelete = await this.storage.all<{ id: string }>(
      'SELECT id FROM scenes WHERE chapterId = ?', chapterId
    );
    
    try {
      await this.storage.transaction(async () => {
        // 1. Log history *before* deleting chapter
        await this.logHistory('chapter', chapterId, 'delete', chapter);
        
        // 2. Delete individual scene files using SceneService *before* DB cascade delete
        // This ensures we have the scene IDs before they are gone from the DB
        for (const scene of scenesToDelete) {
          // Log scene deletion history (optional, could be noisy)
          // const sceneData = await this.getSceneMetadata(scene.id);
          // if (sceneData) await this.logHistory('scene', scene.id, 'delete', sceneData);
          await this.sceneService.deleteSceneFile(bookId, chapterId, scene.id);
        }
        
        // 3. Delete chapter record from DB (CASCADE should delete scene records now)
        const info = await this.storage.run('DELETE FROM chapters WHERE id = ?', chapterId);
        if (info.changes === 0) {
          throw new Error(`Chapter record with ID ${chapterId} failed to delete.`);
        }
        logger.info(`[BookStorage] Deleted chapter record from DB: ${chapterId}`);
        
        // 4. Delete the (now empty) chapter directory using SceneService
        await this.sceneService.deleteChapterDir(bookId, chapterId);
        
        // 5. Git removal is handled by sceneService.deleteSceneFile
      });
      logger.info(`[BookStorage] Chapter ${chapterId} deleted successfully.`);
    } catch (error) {
      logger.error(`[BookStorage] Error deleting chapter ${chapterId}`, error);
      throw error;
    }
  }
  
  // --- Scenes ---
  async getNextScenePosition(chapterId: string): Promise<number> {
    logger.info(`[BookStorage] Getting next scene position for chapter ${chapterId}`);
    const result = await this.storage.get<{
      maxPosition: number | null
    }>('SELECT MAX(position) as maxPosition FROM scenes WHERE chapterId = ?', chapterId);
    return (result?.maxPosition ?? -1) + 1;
  }
  
  async createScene(chapterId: string, title: string, position: number): Promise<Omit<Scene, 'content'>> {
    logger.info(`[BookStorage] Creating scene "${title}" for chapter ${chapterId}`);
    const chapter = await this.getChapterById(chapterId);
    if (!chapter) {
      throw new Error(`Chapter with ID ${chapterId} not found.`);
    }
    const bookId = chapter.bookId; // Need bookId for path and SceneService
    
    // Let TypeScript infer the type here, as jsonFilePath is added later
    const newSceneData = {
      id: `s-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      title,
      chapterId,
      position,
    };
    
    try {
      // Create the JSON file using SceneService, which returns the path
      // Assuming SceneService.createSceneFile now handles JSON creation and returns the .json path
      const jsonFilePath = await this.sceneService.createSceneFile(bookId, chapterId, newSceneData.id);
      logger.info(`[BookStorage] Scene JSON file created by SceneService at: ${jsonFilePath}`);
      
      // Insert metadata into DB, including the jsonFilePath returned by SceneService
      await this.storage.run('INSERT INTO scenes (id, title, chapterId, position, jsonFilePath) VALUES (?, ?, ?, ?, ?)',
        newSceneData.id, newSceneData.title, newSceneData.chapterId, newSceneData.position, jsonFilePath
      );
      logger.info(`[BookStorage] Scene metadata created with ID: ${newSceneData.id}`);
      // Return metadata including the actual jsonFilePath
      return {...newSceneData, jsonFilePath}; // Return jsonFilePath
    } catch (error) {
      logger.error(`[BookStorage] Error creating scene JSON file or DB entry for "${title}"`, error);
      // TODO: Consider cleanup if DB insert fails after file creation?
      throw error;
    }
  }
  
  // Removed getSceneFilePath and getRelativeScenePath helpers
  
  async getSceneMetadata(sceneId: string): Promise<{
    id: string,
    title: string,
    chapterId: string,
    jsonFilePath: string,
    position: number
  } | null> {
    logger.info(`[BookStorage] Getting metadata for scene ${sceneId}`);
    // Select jsonFilePath instead of filePath
    const sceneMeta = await this.storage.get<{
      id: string,
      title: string,
      chapterId: string,
      jsonFilePath: string,
      position: number
    }>(
      'SELECT id, title, chapterId, jsonFilePath, position FROM scenes WHERE id = ?', sceneId
    );
    return sceneMeta || null;
  }
  
  // --- Structure ---
  async getBookStructure(bookId: string): Promise<BookStructure | null> {
    logger.info(`[BookStorage] Getting structure for book ${bookId}`);
    const bookExists = await this.getBookById(bookId);
    if (!bookExists) {
      logger.warn(`[BookStorage] Book not found for structure query: ${bookId}`);
      return null;
    }
    
    const chaptersData = await this.storage.all<Omit<ChapterInfo, 'scenes'>>(
      'SELECT id, title, position FROM chapters WHERE bookId = ? ORDER BY position ASC', bookId
    );
    
    if (!chaptersData || chaptersData.length === 0) {
      logger.info(`[BookStorage] No chapters found for book ${bookId}`);
      return {chapters: []};
    }
    
    const chaptersWithScenes = await Promise.all(chaptersData.map(async (ch): Promise<ChapterInfo> => {
      // Fetch jsonFilePath instead of filePath
      const scenes = await this.storage.all<Omit<SceneInfo, 'content' | 'filePath' | 'jsonFilePath'> & {
        jsonFilePath: string
      }>(
        'SELECT id, title, position, jsonFilePath FROM scenes WHERE chapterId = ? ORDER BY position ASC', ch.id
      );
      // Adapt the return type if SceneInfo expects jsonFilePath
      return {
        ...ch,
        scenes: scenes.map(s => ({id: s.id, title: s.title, position: s.position, jsonFilePath: s.jsonFilePath})) || []
      };
    }));
    
    return {chapters: chaptersWithScenes};
  }
  
  // --- Scenes Content (Delegated to SceneService) ---
  async getSceneContent(sceneId: string): Promise<string | null> {
    logger.info(`[BookStorage] Getting content for scene ${sceneId} via SceneService`);
    const sceneMeta = await this.getSceneMetadata(sceneId); // Fetch full metadata
    if (!sceneMeta) {
      logger.warn(`[BookStorage] Metadata not found for scene ${sceneId}, cannot get content.`);
      return null;
    }
    const chapter = await this.getChapterById(sceneMeta.chapterId);
    if (!chapter) {
      logger.warn(`[BookStorage] Chapter ${sceneMeta.chapterId} not found for scene ${sceneId}, cannot get content.`);
      return null; // Or throw?
    }
    
    return this.sceneService.readSceneContent(chapter.bookId, sceneMeta.chapterId, sceneId);
  }
  
  async saveSceneContent(sceneId: string, content: string): Promise<void> {
    logger.info(`[BookStorage] Saving content for scene ${sceneId} via SceneService`);
    const sceneMeta = await this.getSceneMetadata(sceneId); // Fetch full metadata
    if (!sceneMeta) {
      logger.error(`[BookStorage] Cannot save content, metadata not found for scene ${sceneId}`);
      throw new Error(`Metadata not found for scene ${sceneId}. Cannot save content.`);
    }
    const chapter = await this.getChapterById(sceneMeta.chapterId);
    if (!chapter) {
      logger.error(`[BookStorage] Cannot save content, chapter ${sceneMeta.chapterId} not found for scene ${sceneId}`);
      throw new Error(`Chapter not found for scene ${sceneId}. Cannot save content.`);
    }
    // Delegate to SceneService
    await this.sceneService.writeSceneContent(chapter.bookId, sceneMeta.chapterId, sceneId, content);
    // Note: DB is not updated here unless specific metadata changes
  }
  
  async deleteScene(sceneId: string): Promise<void> {
    logger.info(`[BookStorage] Deleting scene ${sceneId} via SceneService`);
    const sceneMeta = await this.getSceneMetadata(sceneId); // Get metadata *before* deleting DB record
    if (!sceneMeta) {
      logger.warn(`[BookStorage] Scene metadata not found for deletion: ${sceneId}`);
      return; // Already gone or never existed
    }
    const chapter = await this.getChapterById(sceneMeta.chapterId);
    if (!chapter) {
      logger.error(`[BookStorage] Chapter ${sceneMeta.chapterId} not found for scene ${sceneId} during deletion.`);
      // Decide how to handle: maybe proceed with DB deletion but log error?
      // For now, throw to ensure consistency.
      throw new Error(`Chapter ${sceneMeta.chapterId} not found for scene ${sceneId}. Cannot delete scene consistently.`);
    }
    const bookId = chapter.bookId;
    
    try {
      // Start transaction
      await this.storage.transaction(async () => {
        // 1. Log history *before* deleting DB record
        await this.logHistory('scene', sceneId, 'delete', sceneMeta);
        // 2. Delete DB record
        const info = await this.storage.run('DELETE FROM scenes WHERE id = ?', sceneId);
        if (info.changes === 0) {
          throw new Error(`Scene record with ID ${sceneId} failed to delete from DB.`);
        }
        logger.info(`[BookStorage] Deleted scene record from DB: ${sceneId}`);
        
        // 3. Delete file using SceneService
        await this.sceneService.deleteSceneFile(bookId, sceneMeta.chapterId, sceneId);
        // Git removal is handled within deleteSceneFile
      });
      logger.info(`[BookStorage] Scene ${sceneId} deleted successfully.`);
    } catch (error) {
      logger.error(`[BookStorage] Error deleting scene ${sceneId}`, error);
      throw error; // Rethrow error after rollback
    }
  }
  
  /**
   * Updates the position of multiple chapters within a book.
   */
  async reorderChapters(bookId: string, orderedChapterIds: string[]): Promise<void> {
    logger.info(`[BookStorage] Reordering chapters for book ${bookId}`);
    try {
      await this.storage.transaction(async () => {
        await Promise.all(orderedChapterIds.map((chapterId, index) => {
          return this.storage.run(
            'UPDATE chapters SET position = ? WHERE id = ? AND bookId = ?',
            index, chapterId, bookId
          );
        }));
      });
      logger.info(`[BookStorage] Reordered ${orderedChapterIds.length} chapters for book ${bookId}`);
    } catch (error) {
      logger.error(`[BookStorage] Error reordering chapters for book ${bookId}`, error);
      throw error;
    }
  }
  
  // --- Reordering / Moving ---
  
  /**
   * Updates the position of multiple scenes within a chapter.
   */
  async reorderScenes(chapterId: string, orderedSceneIds: string[]): Promise<void> {
    logger.info(`[BookStorage] Reordering scenes for chapter ${chapterId}`);
    try {
      await this.storage.transaction(async () => {
        await Promise.all(orderedSceneIds.map((sceneId, index) => {
          return this.storage.run(
            'UPDATE scenes SET position = ? WHERE id = ? AND chapterId = ?',
            index, sceneId, chapterId
          );
        }));
      });
      logger.info(`[BookStorage] Reordered ${orderedSceneIds.length} scenes for chapter ${chapterId}`);
    } catch (error) {
      logger.error(`[BookStorage] Error reordering scenes for chapter ${chapterId}`, error);
      throw error;
    }
  }
  
  /**
   * Moves a chapter to a different book or a new position within the same book.
   */
  async moveChapter(chapterId: string, targetBookId: string, newPosition: number): Promise<void> {
    logger.info(`[BookStorage] Moving chapter ${chapterId} to book ${targetBookId} at position ${newPosition}`);
    const chapter = await this.getChapterById(chapterId);
    if (!chapter) {
      throw new Error(`Chapter with ID ${chapterId} not found.`);
    }
    const originalBookId = chapter.bookId;
    
    // Get scene info before moving DB records
    const scenesToMove = await this.storage.all<{ id: string }>(
      'SELECT id FROM scenes WHERE chapterId = ?', chapterId
    );
    
    try {
      await this.storage.transaction(async () => {
        // --- DB Updates ---
        // 1. Shift positions in the target book
        await this.storage.run(
          'UPDATE chapters SET position = position + 1 WHERE bookId = ? AND position >= ?',
          targetBookId, newPosition
        );
        // 2. Update the chapter's bookId and position
        const info = await this.storage.run(
          'UPDATE chapters SET bookId = ?, position = ? WHERE id = ?',
          targetBookId, newPosition, chapterId
        );
        if (info.changes === 0) {
          throw new Error(`Chapter ${chapterId} not found during move operation.`);
        }
        // 3. Shift positions in the original book (if different book)
        if (originalBookId !== targetBookId) {
          await this.storage.run(
            'UPDATE chapters SET position = position - 1 WHERE bookId = ? AND position > ?',
            originalBookId, chapter.position
          );
        }
        logger.info(`[BookStorage] Updated chapter ${chapterId} DB record for move.`);
        
        // --- File System Updates (using SceneService) ---
        // 4. Move the chapter directory (containing scene files)
        await this.sceneService.moveChapterDir(originalBookId, targetBookId, chapterId);
        
        // 5. Update scene jsonFilePaths in DB (critical!)
        await Promise.all(scenesToMove.map(scene => {
          // Use SceneService to get the *new* expected JSON path
          // Assuming getSceneFilePath is adapted or replaced by getSceneJsonPath
          const newJsonFilePath = this.sceneService.getSceneFilePath(targetBookId, chapterId, scene.id); // Adapt if method name changes
          return this.storage.run('UPDATE scenes SET jsonFilePath = ? WHERE id = ?', newJsonFilePath, scene.id);
        }));
        logger.info(`[BookStorage] Updated JSON file paths for ${scenesToMove.length} scenes in DB after chapter move.`);
        
        // 6. Git handling is now managed within sceneService.moveChapterDir and subsequent scene moves if needed
      });
      logger.info(`[BookStorage] Chapter ${chapterId} moved successfully.`);
      // Log history for the move
      await this.logHistory('chapter', chapterId, 'update', chapter); // Log original state
    } catch (error) {
      logger.error(`[BookStorage] Error moving chapter ${chapterId}`, error);
      throw error;
    }
  }
  
  /**
   * Moves a scene to a different chapter or a new position within the same chapter.
   */
  async moveScene(sceneId: string, targetChapterId: string, newPosition: number): Promise<void> {
    logger.info(`[BookStorage] Moving scene ${sceneId} to chapter ${targetChapterId} at position ${newPosition}`);
    
    const sceneMeta = await this.getSceneMetadata(sceneId);
    if (!sceneMeta) {
      throw new Error(`Scene with ID ${sceneId} not found.`);
    }
    const originalChapterId = sceneMeta.chapterId;
    
    // Get target chapter to find the target bookId
    const targetChapter = await this.getChapterById(targetChapterId);
    if (!targetChapter) {
      throw new Error(`Target chapter with ID ${targetChapterId} not found.`);
    }
    const targetBookId = targetChapter.bookId;
    
    // Get original chapter to find the original bookId
    const originalChapter = await this.getChapterById(originalChapterId);
    if (!originalChapter) {
      // This should ideally not happen if sceneMeta was found, but check anyway
      throw new Error(`Original chapter with ID ${originalChapterId} not found for scene ${sceneId}.`);
    }
    const originalBookId = originalChapter.bookId;
    
    
    try {
      await this.storage.transaction(async () => {
        // --- File System Move (using SceneService) ---
        // 1. Move the actual file first. SceneService handles Git mv.
        // This returns the new path where the file was actually moved.
        const newFilePath = await this.sceneService.moveSceneFile(
          sceneId,
          originalBookId,
          originalChapterId,
          targetBookId,
          targetChapterId
        );
        
        // --- DB Updates ---
        // 2. Shift positions in the target chapter
        await this.storage.run(
          'UPDATE scenes SET position = position + 1 WHERE chapterId = ? AND position >= ?',
          targetChapterId, newPosition
        );
        // 3. Update the scene's chapterId, position, and *jsonFilePath*
        const info = await this.storage.run(
          'UPDATE scenes SET chapterId = ?, position = ?, jsonFilePath = ? WHERE id = ?',
          targetChapterId, newPosition, newFilePath, sceneId // Use the actual new path (assuming it's the JSON path now)
        );
        if (info.changes === 0) {
          // If file move succeeded but DB update failed, we have inconsistency.
          logger.error(`[BookStorage] Scene file moved to ${newFilePath}, but failed to update DB record for scene ${sceneId}. Manual correction might be needed.`);
          throw new Error(`Scene ${sceneId} DB record not found during move operation update.`);
        }
        // 4. Shift positions in the original chapter (if different chapter)
        if (originalChapterId !== targetChapterId) {
          await this.storage.run(
            'UPDATE scenes SET position = position - 1 WHERE chapterId = ? AND position > ?',
            originalChapterId, sceneMeta.position // Use original position from metadata
          );
        }
        logger.info(`[BookStorage] Updated scene ${sceneId} DB record for move.`);
        
      });
      logger.info(`[BookStorage] Scene ${sceneId} moved successfully to ${targetChapterId}.`);
      // Log history for the move
      await this.logHistory('scene', sceneId, 'update', sceneMeta); // Log original state
    } catch (error) {
      logger.error(`[BookStorage] Error moving scene ${sceneId}`, error);
      throw error;
    }
  }
  
  // --- History Logging Helper ---
  private async logHistory(entityType: string, entityId: string, action: 'update' | 'delete', previousState: unknown): Promise<void> {
    const previousStateJson = previousState ? JSON.stringify(previousState) : null;
    try {
      await this.storage.run(
        'INSERT INTO history_log (timestamp, entityType, entityId, action, previousState) VALUES (?, ?, ?, ?, ?)',
        Date.now(), entityType, entityId, action, previousStateJson
      );
      logger.info(`[BookStorage] Logged history: ${action} ${entityType} ${entityId}`);
    } catch (error) {
      logger.error(`[BookStorage] Failed to log history for ${entityType} ${entityId}`, error);
    }
  }
}
