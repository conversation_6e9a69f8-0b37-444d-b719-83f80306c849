import type {ConfigurationServiceAPI} from '@main/services/configuration.service';
import type {GitServiceAPI} from '@main/services/git.service';
import {LoggingServiceAPI} from '@main/services/logging.service';
import * as fs from 'fs/promises';
import * as path from 'path';

import {defaultProjectsRoot} from '@main/defaults/settings'; // Import default path
// These imports are needed for type references in other files
// that import this service

export class SceneService {
  private readonly git: GitServiceAPI | undefined;
  private settings: ConfigurationServiceAPI;
  private logger: LoggingServiceAPI;
  
  constructor(settingsAPI: ConfigurationServiceAPI, gitService: GitServiceAPI, logger: LoggingServiceAPI) {
    this.settings = settingsAPI;
    this.git = gitService;
    this.logger = logger;
    logger.info(`[SceneService] Initialized.`);
    
    if (!this.git) {
      logger.warn(`[SceneService] GitService not provided. Git operations will be skipped.`);
    }
  }
  
  // --- Path Helpers ---
  
  /** Gets the full path to a scene's Markdown file. */
  public async getSceneFilePath(bookId: string, chapterId: string, sceneId: string): Promise<string> {
    const chapterDir = await this.getChapterSceneDir(bookId, chapterId);
    return path.join(chapterDir, `${sceneId}.md`);
  }
  
  async readSceneContent(bookId: string, chapterId: string, sceneId: string): Promise<string | null> {
    const filePath = await this.getSceneFilePath(bookId, chapterId, sceneId);
    this.logger.info(`[SceneService] Reading content for scene ${sceneId} from ${filePath}`);
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      this.logger.info(`[SceneService] Content loaded for scene ${sceneId}`);
      return content;
    } catch (error) {
      const err = error as { code?: string, message: string };
      if (err.code === 'ENOENT') {
        this.logger.error(`[SceneService] Scene file not found at path: ${filePath}`);
        return null; // File not found is not necessarily a critical error here
      }
      this.logger.error(`[SceneService] Error reading scene file ${filePath}`, error);
      throw error; // Rethrow other errors
    }
  }
  
  async writeSceneContent(bookId: string, chapterId: string, sceneId: string, content: string): Promise<void> {
    const filePath = await this.getSceneFilePath(bookId, chapterId, sceneId);
    this.logger.info(`[SceneService] Writing content for scene ${sceneId} to ${filePath}`);
    try {
      // Ensure directory exists
      await fs.mkdir(path.dirname(filePath), {recursive: true});
      // Write content
      await fs.writeFile(filePath, content, 'utf-8');
      this.logger.info(`[SceneService] Content saved for scene ${sceneId}`);
      
      // Stage changes with Git
      await this.stageScene(bookId, filePath);
      
    } catch (error) {
      this.logger.error(`[SceneService] Error writing scene file ${filePath}`, error);
      throw error;
    }
  }
  
  async createSceneFile(bookId: string, chapterId: string, sceneId: string): Promise<string> {
    const filePath = await this.getSceneFilePath(bookId, chapterId, sceneId);
    const sceneDir = path.dirname(filePath);
    this.logger.info(`[SceneService] Creating scene file for ${sceneId} at ${filePath}`);
    try {
      await fs.mkdir(sceneDir, {recursive: true});
      await fs.writeFile(filePath, '', 'utf-8'); // Create empty file
      this.logger.info(`[SceneService] Created empty scene file: ${filePath}`);
      // Optionally stage the newly created file?
      // await this.stageScene(bookId, filePath); // Decide if new files should be staged immediately
      return filePath;
    } catch (error) {
      this.logger.error(`[SceneService] Error creating scene file ${filePath}`, error);
      throw error;
    }
  }
  
  async deleteSceneFile(bookId: string, chapterId: string, sceneId: string): Promise<void> {
    const filePath = await this.getSceneFilePath(bookId, chapterId, sceneId);
    this.logger.info(`[SceneService] Deleting scene file ${filePath}`);
    try {
      await fs.unlink(filePath);
      this.logger.info(`[SceneService] Deleted scene file: ${filePath}`);
      // Remove from Git index
      await this.removeScene(bookId, filePath);
    } catch (error) {
      const err = error as { code?: string, message: string };
      if (err.code === 'ENOENT') {
        this.logger.warn(`[SceneService] Scene file not found during deletion: ${filePath}`);
        // If file doesn't exist, maybe Git remove still needed if indexed?
        await this.removeScene(bookId, filePath); // Attempt removal anyway
      } else {
        this.logger.error(`[SceneService] Error deleting scene file ${filePath}`, error);
        throw error;
      }
    }
  }
  
  // --- File I/O Operations ---
  
  async deleteChapterDir(bookId: string, chapterId: string): Promise<void> {
    const chapterDir = await this.getChapterSceneDir(bookId, chapterId);
    this.logger.info(`[SceneService] Deleting chapter scene directory ${chapterDir}`);
    try {
      await fs.rm(chapterDir, {recursive: true, force: true});
      this.logger.info(`[SceneService] Deleted chapter scene directory: ${chapterDir}`);
      // Git removal for files inside is handled by deleteSceneFile calls before this
    } catch (error) {
      const err = error as { code?: string, message: string };
      if (err.code === 'ENOENT') {
        this.logger.warn(`[SceneService] Chapter scene directory not found during deletion: ${chapterDir}`);
      } else {
        this.logger.error(`[SceneService] Error deleting chapter scene directory ${chapterDir}`, error);
        throw error;
      }
    }
  }
  
  async deleteBookDir(bookId: string): Promise<void> {
    const bookDir = await this.getBookDir(bookId);
    this.logger.info(`[SceneService] Deleting book directory ${bookDir}`);
    try {
      await fs.rm(bookDir, {recursive: true, force: true});
      this.logger.info(`[SceneService] Deleted book directory: ${bookDir}`);
    } catch (error) {
      const err = error as { code?: string, message: string };
      if (err.code === 'ENOENT') {
        this.logger.warn(`[SceneService] Book directory not found during deletion: ${bookDir}`);
      } else {
        this.logger.error(`[SceneService] Error deleting book directory ${bookDir}`, error);
        throw error;
      }
    }
  }
  
  async moveSceneFile(sceneId: string, originalBookId: string, originalChapterId: string, targetBookId: string, targetChapterId: string): Promise<string> {
    const originalFilePath = await this.getSceneFilePath(originalBookId, originalChapterId, sceneId);
    const newFilePath = await this.getSceneFilePath(targetBookId, targetChapterId, sceneId);
    this.logger.info(`[SceneService] Moving scene file ${sceneId} from ${originalFilePath} to ${newFilePath}`);
    
    if (originalFilePath === newFilePath) {
      this.logger.info(`[SceneService] Scene file path unchanged, skipping move for ${sceneId}.`);
      return newFilePath;
    }
    
    try {
      // Ensure target directory exists
      await fs.mkdir(path.dirname(newFilePath), {recursive: true});
      // Move the file
      await fs.rename(originalFilePath, newFilePath);
      this.logger.info(`[SceneService] Moved scene file to ${newFilePath}`);
      
      // Handle Git move
      await this.moveSceneGit(originalBookId, targetBookId, originalFilePath, newFilePath);
      
      return newFilePath;
    } catch (error) {
      const err = error as { code?: string, message: string };
      if (err.code === 'ENOENT') {
        this.logger.error(`[SceneService] Original scene file not found during move: ${originalFilePath}. Cannot move.`);
        // Throw error because we can't proceed without the original file
        throw new Error(`Original scene file not found: ${originalFilePath}`);
      } else {
        this.logger.error(`[SceneService] Error moving scene file ${sceneId}`, error);
        throw error;
      }
    }
  }
  
  async moveChapterDir(originalBookId: string, targetBookId: string, chapterId: string): Promise<void> {
    const originalChapterDir = await this.getChapterSceneDir(originalBookId, chapterId);
    const targetChapterDir = await this.getChapterSceneDir(targetBookId, chapterId);
    this.logger.info(`[SceneService] Moving chapter directory ${chapterId} from ${originalChapterDir} to ${targetChapterDir}`);
    
    if (originalChapterDir === targetChapterDir) {
      this.logger.info(`[SceneService] Chapter directory path unchanged, skipping move for ${chapterId}.`);
      return;
    }
    
    try {
      // Ensure target book directory exists
      await fs.mkdir(path.dirname(targetChapterDir), {recursive: true});
      // Attempt to move the directory
      await fs.rename(originalChapterDir, targetChapterDir);
      this.logger.info(`[SceneService] Moved chapter directory to ${targetChapterDir}`);
      // Git handling for directory move is complex, usually handled by moving individual files within Git.
      // The moveSceneGit calls within moveChapter in BookStorage should handle this.
    } catch (error) {
      const err = error as { code?: string, message: string };
      if (err.code === 'ENOENT') {
        this.logger.warn(`[SceneService] Original chapter scene directory not found during move: ${originalChapterDir}. Skipping directory move.`);
      } else if (err.code === 'EPERM' || err.code === 'EBUSY') {
        this.logger.error(`[SceneService] Permission error moving directory ${originalChapterDir}. Files might be locked.`, error);
        throw new Error(`Failed to move chapter directory due to permissions or lock.`);
      } else {
        this.logger.error(`[SceneService] Error moving chapter directory ${chapterId}`, error);
        throw error;
      }
    }
  }
  
  async initRepoForBook(bookId: string): Promise<void> {
    if (!this.git) return;
    const projectsRoot = await this.getCurrentProjectsRoot(); // Получаем актуальный путь
    const bookPath = await this.getBookDir(bookId); // getBookDir уже использует актуальный путь
    this.logger.info(`[SceneService] Initializing Git repository for book ${bookId} at ${bookPath}`);
    try {
      // Ensure the base project directory exists first
      await fs.mkdir(projectsRoot, {recursive: true}); // Используем актуальный путь
      await this.git.initRepo(bookPath);
      this.logger.info(`[SceneService] Initialized Git repository at: ${bookPath}`);
      // TODO: Create initial commit? (e.g., with .gitignore)
    } catch (gitError) {
      this.logger.error(`[SceneService] Failed to initialize Git repository for book ${bookId}`, gitError);
      // Don't throw, just log
    }
  }
  
  async stageScene(bookId: string, filePath: string): Promise<void> {
    if (!this.git) return;
    const repoPath = await this.getBookDir(bookId);
    const relativePath = await this.getRelativeScenePath(filePath, bookId);
    this.logger.info(`[SceneService] Staging scene file via Git: ${relativePath} in repo ${repoPath}`);
    try {
      await this.git.stage(repoPath, relativePath);
      this.logger.info(`[SceneService] Staged scene file via Git: ${relativePath}`);
    } catch (gitError) {
      this.logger.error(`[SceneService] Git staging failed for scene file ${relativePath}`, gitError);
      // Don't throw, just log
    }
  }
  
  async removeScene(bookId: string, filePath: string): Promise<void> {
    if (!this.git) return;
    const repoPath = await this.getBookDir(bookId);
    const relativePath = await this.getRelativeScenePath(filePath, bookId);
    this.logger.info(`[SceneService] Removing scene file from Git index: ${relativePath} in repo ${repoPath}`);
    try {
      await this.git.remove(repoPath, relativePath);
      this.logger.info(`[SceneService] Removed scene file from Git index: ${relativePath}`);
    } catch (gitError) {
      // Log error, but don't throw. Git state might be inconsistent if file was already removed.
      this.logger.error(`[SceneService] Git remove failed for scene file ${relativePath}. It might have already been removed or not tracked.`, gitError);
    }
  }
  
  async moveSceneGit(originalBookId: string, targetBookId: string, originalFilePath: string, newFilePath: string): Promise<void> {
    if (!this.git) return;
    
    const originalRepoPath = await this.getBookDir(originalBookId);
    const targetRepoPath = await this.getBookDir(targetBookId);
    const originalRelativePath = await this.getRelativeScenePath(originalFilePath, originalBookId);
    const newRelativePath = await this.getRelativeScenePath(newFilePath, targetBookId);
    
    this.logger.info(`[SceneService] Handling Git move for scene: ${originalRelativePath} -> ${newRelativePath}`);
    
    try {
      if (originalRepoPath === targetRepoPath) {
        // Move within the same repository
        if (typeof this.git.mv === 'function') {
          await this.git.mv(originalRepoPath, originalRelativePath, newRelativePath);
          this.logger.info(`[SceneService] Moved scene file within Git repo: ${originalRelativePath} -> ${newRelativePath}`);
        } else {
          // Fallback if mv is not available
          await this.git.remove(originalRepoPath, originalRelativePath);
          await this.git.stage(targetRepoPath, newRelativePath); // Stage in target (same as original)
          this.logger.warn(`[SceneService] Used git rm/add fallback for moving scene file within repo: ${newRelativePath}`);
        }
      } else {
        // Move between different repositories (conceptually)
        // 1. Remove from the old repository index
        await this.git.remove(originalRepoPath, originalRelativePath);
        this.logger.info(`[SceneService] Removed scene file from original Git repo index: ${originalRelativePath}`);
        // 2. Stage in the new repository index
        await this.git.stage(targetRepoPath, newRelativePath);
        this.logger.info(`[SceneService] Staged scene file in target Git repo index: ${newRelativePath}`);
        // Note: This loses file history across the move. True cross-repo history preservation is complex.
      }
      // TODO: Commit changes? Probably not here.
    } catch (gitError) {
      this.logger.error(`[SceneService] Git move/stage/remove failed during scene move for ${originalRelativePath}`, gitError);
      // Don't throw, just log
    }
  }
  
  // --- Git Operations ---
  
  // Implement Disposable
  async dispose(): Promise<void> {
    this.logger.info('[SceneService] Disposing...');
    // Add a small delay to allow pending file writes to potentially complete.
    // This is not a guaranteed solution but might help in many cases.
    // A more robust solution would involve tracking active write operations.
    await new Promise(resolve => setTimeout(resolve, 150)); // Increased delay slightly
    this.logger.info('[SceneService] Dispose finished (after delay).');
  }
  
  /** Gets the current project root path from settings. */
  private async getCurrentProjectsRoot(): Promise<string> {
    // Получаем актуальный путь каждый раз
    return await this.settings.getValue<string>('workbench.projectsRoot', defaultProjectsRoot);
  }
  
  /** Gets the base directory for a specific book project. */
  private async getBookDir(bookId: string): Promise<string> {
    const projectsRoot = await this.getCurrentProjectsRoot(); // Получаем актуальный путь
    return path.join(projectsRoot, bookId);
  }
  
  /** Gets the directory containing scenes for a specific chapter. */
  private async getChapterSceneDir(bookId: string, chapterId: string): Promise<string> {
    const bookDir = await this.getBookDir(bookId);
    return path.join(bookDir, 'scenes', chapterId);
  }
  
  /** Gets the relative path of a scene file within its book's Git repository. */
  private async getRelativeScenePath(filePath: string, bookId: string): Promise<string> {
    const bookDir = await this.getBookDir(bookId);
    return path.relative(bookDir, filePath);
  }
}
