import {IpcErrorData,} from "@shared/types/ipc";
import {Book, BookStructure} from "../../shared/types";
import {ExtensionContext} from "@main/extensions/extension.types";
import {BookStorage} from "@/extensions/books/main/storage";
import * as ipcChannels from "../../shared/constants/ipc-channels";
import {AUTOCOMPLETE_SCENE} from "@/extensions/books/shared/constants";
import {randomUUID} from 'crypto';

interface GetBookStructureArgs {
  bookId: string
}

type GetBookStructureResult = BookStructure | null;
type GetBooksResult = Book[];
type SaveSceneContentResult = undefined;


interface AutocompleteScenePayload {
  textBeforeCursor: string;
}

interface TokenStreamPayload {
  requestId: string;
  token?: string;
  error?: IpcErrorData;
  isDone: boolean;
}

interface GetSceneContentArgs {
  sceneId: string;
}

interface SaveSceneContentArgs {
  sceneId: string;
  content: string;
}

export function registerBookIpcHandlers(context: ExtensionContext, bookStorage: BookStorage): void {
  const scopedLogger = context.logger; // Use the logger provided in context
  scopedLogger.info(`Registering IPC Handlers...`);
  
  // Use the new ipc from context
  const {ipc, subscriptions, ai} = context; // Destructure aiService
  
  // Note: sceneService is available here if needed for future handlers
  // Force re-check by adding a comment
  // getBookStructure
  subscriptions.push(
    ipc.handle<GetBookStructureArgs, GetBookStructureResult>(ipcChannels.GET_BOOK_STRUCTURE, async (args) => {
      const {bookId} = args;
      if (!bookId) throw new Error('Invalid book ID provided.');
      return await bookStorage.getBookStructure(bookId);
    })
  );
  
  // getBooks
  subscriptions.push(
    ipc.handle<never, GetBooksResult>(ipcChannels.GET_BOOKS, async () => {
      return bookStorage.getBooks();
    })
  );
  
  // getSceneContent
  subscriptions.push(
    ipc.handle<GetSceneContentArgs, string | null>(ipcChannels.GET_SCENE_CONTENT, async (args) => {
      const {sceneId} = args;
      console.log(args)
      if (!sceneId) {
        const error = new Error('Неверный ID сцены.') as Error & { code: string };
        error.code = 'VALIDATION_FAILED';
        throw error;
      }
      return bookStorage.getSceneContent(sceneId)
    })
  );
  
  // saveSceneContent
  subscriptions.push(
    ipc.handle<SaveSceneContentArgs, SaveSceneContentResult>(ipcChannels.SAVE_SCENE_CONTENT, async (args) => {
      const {sceneId, content} = args;
      if (!sceneId) {
        const error = new Error('Неверный ID сцены.') as Error & { code: string };
        error.code = 'VALIDATION_FAILED';
        throw error;
      }
      await bookStorage.saveSceneContent(sceneId, content);
    })
  );
  
  // --- AI Autocomplete Handler (Streaming) ---
  subscriptions.push(
    // This handler INITIATES the stream and returns a request ID
    ipc.handle<AutocompleteScenePayload, { requestId: string } | IpcErrorData>(
      ipcChannels.AUTOCOMPLETE_SCENE_STREAM_START,
      async (payload: AutocompleteScenePayload): Promise<{ requestId: string } | IpcErrorData> => {
        const {textBeforeCursor} = payload;
        const taskId = AUTOCOMPLETE_SCENE;
        const requestId = randomUUID(); // Generate unique ID for this stream
        const streamChannel = `ai:tokenStream:${requestId}`; // Channel for sending tokens
        
        scopedLogger.info(`IPC: Handling ${ipcChannels.AUTOCOMPLETE_SCENE_STREAM_START} (Request ID: ${requestId})`);
        
        if (!ai) {
          return {code: 'service_unavailable', message: 'AI Service is not available.'};
        }
        
        // Prepare context for the AI task
        const aiContext = {textBeforeCursor};
        
        // Start the streaming task but DON'T await the full generator here
        // We run it in the background and send tokens via ipc.send
        (async () => {
          try {
            const streamGenerator = ai.runStreamingTask(taskId, aiContext);
            for await (const chunk of streamGenerator) {
              if (typeof chunk === 'string') {
                // Send token to renderer
                const tokenPayload: TokenStreamPayload = {requestId, token: chunk, isDone: false};
                context.ipc.send(streamChannel, tokenPayload); // Use 'send'
              } else {
                // Send error to renderer
                const errorPayload: TokenStreamPayload = {requestId, error: chunk, isDone: true};
                context.ipc.send(streamChannel, errorPayload); // Use 'send'
                return; // Stop streaming on error
              }
            }
            // Signal completion
            const donePayload: TokenStreamPayload = {requestId, isDone: true};
            context.ipc.send(streamChannel, donePayload); // Use 'send'
            scopedLogger.info(`Streaming finished for request ID: ${requestId}`);
            
          } catch (error: unknown) {
            const message = error instanceof Error ? error.message : String(error);
            scopedLogger.error(`Error during AI streaming task execution for ${requestId}`, {error});
            // Send error to renderer
            const errorPayload: TokenStreamPayload = {
              requestId,
              error: {code: 'streaming_execution_error', message: `Streaming failed: ${message}`},
              isDone: true
            };
            context.ipc.send(streamChannel, errorPayload); // Use 'send'
          }
        })().catch(err => {
          // Catch errors from the async IIFE itself
          scopedLogger.error(`Unhandled error in streaming IIFE for ${requestId}`, {err});
          const errorPayload: TokenStreamPayload = {
            requestId,
            error: {code: 'internal_error', message: `Internal streaming error.`},
            isDone: true
          };
          context.ipc.send(streamChannel, errorPayload); // Use 'send'
        });
        
        console.log("returning requestId", requestId)
        // Immediately return the request ID so the renderer can start listening
        return {requestId};
        
      }
    )
  );
  
  
  scopedLogger.info(`Book IPC Handlers registered.`);
}
