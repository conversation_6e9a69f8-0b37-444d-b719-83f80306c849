{"name": "books", "publisher": "book-ide", "version": "0.0.1", "main": "main/index.js", "activationEvents": [], "contributes": {"viewContainers": [{"id": "book-ide.books.books", "title": "Explorer", "icon": "Library", "viewId": "book-ide.books.explorer"}], "views": {"book-ide.books.books": [{"id": "book-ide.books.explorer", "name": "Books Explorer", "componentName": "explorer", "location": "sidebar", "icon": "Library"}]}, "commands": [{"command": "book-ide.books:commands.createBook", "title": "Create New Book", "category": "Book"}, {"command": "book-ide.books:commands.renameBook", "title": "<PERSON><PERSON>", "category": "Book"}, {"command": "book-ide.books:commands.deleteBook", "title": "Delete Book", "category": "Book"}, {"command": "book-ide.books:commands.createChapter", "title": "New Chapter", "category": "Book"}, {"command": "book-ide.books:commands.renameChapter", "title": "Rename Chapter", "category": "Book"}, {"command": "book-ide.books:commands.deleteChapter", "title": "Delete Chapter", "category": "Book"}, {"command": "book-ide.books:commands.reorderChapters", "title": "Reorder Chapters", "category": "Book"}, {"command": "book-ide.books:commands.createScene", "title": "New Scene", "category": "Book"}, {"command": "book-ide.books:commands.renameScene", "title": "Rename Scene", "category": "Book"}, {"command": "book-ide.books:commands.deleteScene", "title": "Delete Scene", "category": "Book"}, {"command": "book-ide.books:commands.saveScene", "title": "Save Scene", "category": "Editor"}, {"command": "book-ide.books:commands.moveScene", "title": "Move Scene", "category": "Book"}, {"command": "book-ide.books:commands.reorderScenes", "title": "Reorder Scenes", "category": "Book"}], "menus": {"explorer/context/book": [{"command": "book-ide.books:commands.createChapter", "group": "1_structure@1"}, {"command": "book-ide.books:commands.renameBook", "group": "2_modification@1"}, {"command": "book-ide.books:commands.deleteBook", "group": "9_manage@1"}], "explorer/context/chapter": [{"command": "book-ide.books:commands.createScene", "group": "1_structure@1"}, {"command": "book-ide.books:commands.renameChapter", "group": "2_modification@1"}, {"command": "book-ide.books:commands.deleteChapter", "group": "9_manage@1"}], "editor/context": [{"command": "book-ide.books:commands.saveScene", "group": "1_save"}], "explorer/context/scene": [{"command": "book-ide.books:commands.renameScene", "group": "2_modification@1"}, {"command": "book-ide.books:commands.deleteScene", "group": "9_manage@1"}]}, "editors": [{"editorType": "book-ide.books:editors.sceneEditor", "componentName": "Editor", "icon": "FileText"}], "aiTasks": [{"taskId": "book-ide.books:tasks.autocompleteScene", "description": "Provides text autocompletion suggestions within the scene editor.", "inputSchema": {"type": "object", "properties": {"textBeforeCursor": {"type": "string"}, "textAfterCursor": {"type": "string"}, "sceneId": {"type": "string"}}, "required": ["textBeforeCursor", "textAfterCursor", "sceneId"]}, "outputSchema": {"type": "object", "properties": {"suggestion": {"type": "string"}}, "required": ["suggestion"]}}]}}