import React, { use<PERSON><PERSON>back, useEffect } from "react";
import {
  closestCenter,
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

import {
  useBooks,
  useBooksError,
  useBooksStore,
  useBookStructure,
  useIsLoadingBooks,
  useIsLoadingStructure,
  useSelectedBookId,
  useStructureError,
} from "../state/booksStore";
import type { ChapterInfo, SceneInfo } from "../../shared/types";
import {
  CommandsExecuteArgs,
  IpcErrorData,
  MenuShowContextMenuArgs,
} from "@shared/types/ipc";
import { IpcChannels } from "@shared/constants/ipc-channels";
import { CommandIds as CoreCommandIds } from "@shared/constants/command-ids";
import * as BooksCommandIds from "../../shared/constants/commands";
import { SCENE_EDITOR } from "../../shared/constants/editors";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import "./BooksExplorer.view.css";
import { useActiveEditorContext } from "../hooks/useActiveEditorContext";
import { useBookExplorerDnd } from "../hooks/useBookExplorerDnd";
import { BooksExplorerHeader } from "../components/BooksExplorerHeader";
import { BooksExplorerPlaceholder } from "../components/BooksExplorerPlaceholder";
import { BookSelector } from "../components/BookSelector";
import { ChapterItem } from "../components/ChapterItem";
import { SortableSceneItem } from "../components/SortableSceneItem";
import { Icon } from "../components/Icon";

export const ExplorerView: React.FC = () => {
  // State hooks from Zustand
  const { loadBooks, setSelectedBookId } = useBooksStore();
  const books = useBooks();
  const isLoadingBooks = useIsLoadingBooks();
  const booksError = useBooksError();
  const selectedBookId = useSelectedBookId();
  const bookStructure = useBookStructure();
  const isLoadingStructure = useIsLoadingStructure();
  const structureError = useStructureError();
  // Use the custom hook to get the active scene ID
  const activeSceneIdInExplorer = useActiveEditorContext();

  // Use the custom hook for DND logic
  const {
    activeId,
    activeItemData,
    handleDragStart,
    handleDragEnd,
    handleDragCancel,
  } = useBookExplorerDnd({ bookStructure, selectedBookId });

  // dnd-kit sensors (can remain here or be moved to the hook if preferred)
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Load books on mount
  useEffect(() => {
    loadBooks();
  }, [loadBooks]);

  // --- Action Handlers ---
  const handleSceneClick = useCallback(async (scene: SceneInfo) => {
    // Make async
    console.log(
      `[BooksExplorerView] handleSceneClick called for scene: ${scene.id} (${scene.title})`
    );
    try {
      const args: CommandsExecuteArgs = {
        commandId: CoreCommandIds.OPEN_EDITOR, // Используем константу ядра
        args: {
          editorType: SCENE_EDITOR, // Используем импортированную константу
          dataId: scene.id,
          title: scene.title || "Untitled Scene",
        },
      };
      await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args); // Используем константу
    } catch (err) {
      const errorData = err as IpcErrorData;
      console.error("Error opening scene editor:", errorData.message, err);
      alert(`Error opening scene: ${errorData.message || "Unknown error"}`);
    }
    // We don't call openOrFocusTab here anymore, the OPEN_EDITOR command handles it
  }, []);

  const handleNewBookClick = useCallback(async () => {
    // Make async
    try {
      const args: CommandsExecuteArgs = {
        commandId: BooksCommandIds.CREATE_BOOK,
        args: {},
      };
      await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args); // Используем константу
    } catch (err) {
      const errorData = err as IpcErrorData;
      alert(`Error creating book: ${errorData.message || "Unknown error"}`);
    }
  }, []);

  const handleAddChapterClick = useCallback(async () => {
    // Make async
    if (!selectedBookId) {
      alert("Please select a book first.");
      return;
    }
    try {
      const args: CommandsExecuteArgs = {
        commandId: BooksCommandIds.CREATE_CHAPTER,
        args: { bookId: selectedBookId },
      };

      await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args); // Используем константу
    } catch (err) {
      const errorData = err as IpcErrorData;
      alert(`Error adding chapter: ${errorData.message || "Unknown error"}`);
    }
  }, [selectedBookId]);

  const handleAddSceneClick = useCallback(async (chapterId: string) => {
    // Make async
    try {
      const args: CommandsExecuteArgs = {
        commandId: BooksCommandIds.CREATE_SCENE,
        args: { chapterId: chapterId },
      };
      await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args); // Используем константу
    } catch (err) {
      const errorData = err as IpcErrorData;
      alert(`Error adding scene: ${errorData.message || "Unknown error"}`);
    }
  }, []);

  const handleContextMenu = useCallback(
    async (
      // Make async
      event: React.MouseEvent,
      itemType: "book" | "chapter" | "scene",
      itemId: string
    ) => {
      event.preventDefault();
      event.stopPropagation();
      console.debug(`Context menu requested for ${itemType} ${itemId}`);

      let contextIdentifier: string | null = null;
      const menuArgs: {
        itemId: string;
        bookId?: string | null;
        chapterId?: string;
        x?: number;
        y?: number;
      } = {
        itemId,
        x: event.clientX,
        y: event.clientY,
      };

      switch (itemType) {
        case "book":
          contextIdentifier = "explorer/context/book";
          menuArgs.bookId = itemId;
          break;
        case "chapter":
          contextIdentifier = "explorer/context/chapter";
          menuArgs.bookId = selectedBookId;
          menuArgs.chapterId = itemId;
          break;
        case "scene": {
          contextIdentifier = "explorer/context/scene";
          menuArgs.bookId = selectedBookId;
          const chapter = bookStructure?.chapters.find((ch: ChapterInfo) =>
            ch.scenes.some((s: SceneInfo) => s.id === itemId)
          );
          if (chapter) {
            menuArgs.chapterId = chapter.id;
          }
          break;
        }
      }

      if (contextIdentifier) {
        try {
          console.log(
            `Showing context menu for ${contextIdentifier} at (${menuArgs.x}, ${menuArgs.y})`
          );
          console.log("Menu args:", menuArgs);

          const args: MenuShowContextMenuArgs = { contextIdentifier, menuArgs };
          console.log("Full IPC args:", args);

          const result = await ipcRendererService.invoke(
            IpcChannels.MENU_SHOW_CONTEXT_MENU,
            args
          ); // Используем константу

          console.log("Context menu result:", result);
        } catch (err) {
          const errorData = err as IpcErrorData;
          console.error("Error showing context menu:", errorData.message, err);
        }
      } else {
        console.warn(
          "No context identifier determined for item type:",
          itemType
        );
      }
    },
    [selectedBookId, bookStructure]
  );

  if (booksError) return <p style={{ color: "red" }}>Error</p>;
  if (isLoadingBooks) return <p>Loading books...</p>;

  const currentChapters: ChapterInfo[] = bookStructure
    ? bookStructure.chapters
    : [];
  const chapterIds = currentChapters.map((ch) => `chapter-${ch.id}`);

  return (
    <div className="books-explorer-container">
      <BooksExplorerHeader
        selectedBookId={selectedBookId}
        onAddChapterClick={handleAddChapterClick}
        onNewBookClick={handleNewBookClick}
      />
      {/* Content Section (Scrollable) */}
      <div className="books-explorer-content">
        <BookSelector
          books={books}
          selectedBookId={selectedBookId}
          isLoading={isLoadingBooks}
          onChange={setSelectedBookId}
          onContextMenu={(e, bookId) => handleContextMenu(e, "book", bookId)}
        />

        {selectedBookId && isLoadingStructure && <p>Loading structure...</p>}
        {selectedBookId && structureError && (
          <p style={{ color: "red" }}>Error:</p>
        )}

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          onDragCancel={handleDragCancel}
        >
          <div
            onContextMenu={(e) =>
              selectedBookId && handleContextMenu(e, "book", selectedBookId)
            }
            style={{ flexGrow: 1 }}
          >
            <SortableContext
              items={chapterIds}
              strategy={verticalListSortingStrategy}
            >
              <div style={{ paddingTop: "5px" }}>
                {selectedBookId &&
                  currentChapters.map((chapter) => (
                    <ChapterItem
                      key={chapter.id}
                      id={`chapter-${chapter.id}`}
                      chapter={chapter}
                      onSceneClick={handleSceneClick}
                      onAddSceneClick={handleAddSceneClick}
                      onContextMenu={handleContextMenu}
                      activeSceneId={activeSceneIdInExplorer} // Pass active scene ID down
                    />
                  ))}
              </div>
            </SortableContext>
          </div>

          <DragOverlay>
            {activeId && activeItemData ? ( // Check activeItemData exists
              <>
                {/* Check type based on activeItemData structure */}
                {"scenes" in activeItemData && ( // It's a ChapterInfo
                  <div className="chapter-item drag-overlay-item">
                    <Icon
                      name="ChevronRight"
                      size={16}
                      className="chapter-chevron"
                    />
                    <span className="chapter-title">
                      {activeItemData.title || "Untitled Chapter"}
                    </span>
                  </div>
                )}
                {"scene" in activeItemData && ( // It's { scene: SceneInfo; chapterId: string }
                  <SortableSceneItem
                    id={String(activeId)} // Use activeId from hook
                    scene={activeItemData.scene}
                    chapterId={activeItemData.chapterId}
                    onSceneClick={() => {
                      // No-op for drag overlay
                    }}
                    onContextMenu={() => {
                      // No-op for drag overlay
                    }}
                    isActive={false} // Pass default value for overlay item
                  />
                )}
              </>
            ) : null}
          </DragOverlay>
        </DndContext>

        <BooksExplorerPlaceholder
          booksCount={books.length}
          selectedBookId={selectedBookId}
          isLoadingStructure={isLoadingStructure}
          structureError={structureError}
          chaptersCount={currentChapters.length}
          onNewBookClick={handleNewBookClick}
        />
      </div>{" "}
      {/* End of books-explorer-content */}
    </div>
  );
};

export default ExplorerView;
