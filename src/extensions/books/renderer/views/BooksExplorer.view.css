/* Basic styling for the explorer */
.books-explorer-container {
  padding: 0; /* Remove padding from main container */
  font-size: 0.9em;
  color: var(--app-foreground); /* Default text color */
  height: 100%; /* Ensure container fills space */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent main container from scrolling */
}

/* --- Header Styles --- */
.books-explorer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0; /* Padding for the header */
    border-bottom: 1px solid var(--app-border);
    flex-shrink: 0; /* Prevent header from shrinking */
}

.view-title {
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.9em;
    opacity: 0.8;
}

.view-actions {
    display: flex;
    align-items: center;
    gap: 4px; /* Space between icons */
}

.view-action-button {
    /* Reset button styles */
    background: none;
    border: none;
    padding: 2px;
    margin: 0;
    color: var(--app-foreground);
    cursor: pointer;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: opacity 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.view-action-button:hover {
    opacity: 1;
    background-color: var(--app-list-hoverBackground);
}

.view-action-button:active {
     background-color: var(--app-list-activeSelectionBackground);
}


/* --- Content Styles --- */
.books-explorer-content {
    flex-grow: 1; /* Take remaining vertical space */
    overflow-y: auto; /* Enable scrolling only for this section */
}


.books-explorer-content hr {
    border: none;
    border-top: 1px solid var(--app-border);
}

/* Styling for ChapterItem summary */
.books-explorer-container details > summary {
  font-weight: bold;
  position: relative; /* Needed for button positioning */
  /* display: flex etc. are set inline in ChapterItem */
}

/* Styling for the list within ChapterItem */
.books-explorer-container details > ul {
  list-style: none;
  padding-left: 15px; /* Indentation for scenes */
  margin-top: 2px;
  margin-bottom: 5px;
}

/* Ensure summary marker (arrow) is clickable and uses theme color */
.books-explorer-container details > summary::marker,
.books-explorer-container details > summary::-webkit-details-marker {
    color: var(--app-foreground);
    /* padding-right: 4px; */
}

/* Style for placeholder text */
.books-explorer-placeholder p {
    padding: 5px 10px;
    color: var(--app-foreground);
    opacity: 0.7;
    font-style: italic;
    margin-bottom: 10px; /* Space between text and button */
    font-style: normal; /* Override italic from general p */
    opacity: 0.8;
}

/* Style for error messages */
.books-explorer-container p[style*="color: red"] {
    color: var(--app-errorForeground) !important;
    font-style: normal;
    opacity: 1;
}

/* --- Add Scene Button Styles (inside summary) --- */
/* Use specific selector */
.books-explorer-container details > summary > .add-scene-button {
    /* Reset inherited styles */
    width: auto;
    margin-bottom: 0;
    /* Reset default button styles */
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    margin-left: 5px; /* Space from title */
    /* Appearance */
    color: var(--app-foreground);
    cursor: pointer;
    font-size: 1.1em; /* Adjust if using icon */
    line-height: 1;
    opacity: 0; /* Hide by default */
    transition: opacity 0.15s ease-in-out;
    border-radius: 3px;
    width: 20px; /* Fixed width */
    height: 20px; /* Fixed height */
    display: inline-flex; /* Use flex to center icon */
    align-items: center;
    justify-content: center;
    flex-shrink: 0; /* Prevent shrinking */
}

/* Show button on summary hover */
.books-explorer-container details > summary:hover > .add-scene-button {
    opacity: 0.7;
}

/* Increase opacity on button hover */
.books-explorer-container details > summary > .add-scene-button:hover {
    opacity: 1 !important;
    background-color: var(--app-list-hoverBackground);
}

/* Add active state */
.books-explorer-container details > summary > .add-scene-button:active {
     background-color: var(--app-list-activeSelectionBackground);
}

/* --- Placeholder Styles (at the bottom) --- */
.books-explorer-placeholder {
    padding: 15px 10px;
    text-align: center;
    /* flex-grow: 1; */ /* Remove flex-grow if it's not meant to fill empty space */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.placeholder-button {
    /* Use general button styles but adjust width */
    width: auto; /* Don't force 100% width */
    padding: 5px 15px; /* Make it slightly larger */
    margin-bottom: 0; /* Remove bottom margin */
    border: 1px solid var(--app-border);
    background-color: var(--app-button-background);
    color: var(--app-button-foreground);
    border-radius: 3px;
    cursor: pointer;
    text-align: center;
}

.placeholder-button:hover {
     background-color: var(--app-button-hoverBackground);
}

/* Style for the active scene item - Increased Specificity */
.books-explorer-container .scene-item.active-scene {
    background-color: var(--app-list-activeSelectionBackground) !important; /* Use the active selection background */
    color: var(--app-list-activeSelectionForeground) !important; /* Use the active selection foreground */
}

/* Ensure active style persists on hover */
.books-explorer-container .scene-item.active-scene:hover {
    background-color: var(--app-list-activeSelectionBackground) !important; /* Keep active background */
    color: var(--app-list-activeSelectionForeground) !important; /* Keep active text color */
}

/* --- Drag Overlay Styles --- */
.drag-overlay-item {
  padding: 4px 8px; /* Match item padding */
  background-color: var(--app-list-hoverBackground); /* Use hover background for visibility */
  color: var(--app-foreground);
  border-radius: 3px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Add a subtle shadow */
  display: flex;
  align-items: center;
  gap: 4px; /* Space between icon and text */
  font-weight: normal; /* Match scene item weight */
  opacity: 0.9; /* Slightly transparent */
}

/* Style the icon within the overlay */
.drag-overlay-item .chapter-chevron {
    opacity: 0.7;
}

/* Style the title within the overlay */
.drag-overlay-item .chapter-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold; /* Make chapter title bold like in the list */
}
