import {useCallback, useMemo, useState} from 'react';
import {DragEndEvent, DragStartEvent, UniqueIdentifier} from '@dnd-kit/core';
import {arrayMove} from '@dnd-kit/sortable';
import {
    optimisticallyMoveScene,
    optimisticallyReorderChapters,
    optimisticallyReorderScenes,
    useBooksStore
} from '../state/booksStore';
import type {BookStructure, ChapterInfo, SceneInfo} from '../../shared/types';
import {ipcRendererService} from '@renderer/core/services/ipcRendererService';
import {IpcChannels} from '@shared/constants/ipc-channels';
import * as commands from '../../shared/constants/commands';
import type {CommandsExecuteArgs, IpcErrorData} from '@shared/types/ipc';

// Helper function to parse IDs (copied from BooksExplorerView)
const parseItemId = (id: UniqueIdentifier): { type: 'chapter' | 'scene' | null, realId: string } => {
  const idStr = String(id);
  if (idStr.startsWith('chapter-')) {
    return {type: 'chapter', realId: idStr.replace('chapter-', '')};
  }
  if (idStr.startsWith('scene-')) {
    return {type: 'scene', realId: idStr.replace('scene-', '')};
  }
  return {type: null, realId: idStr};
};

interface UseBookExplorerDndProps {
  bookStructure: BookStructure | null;
  selectedBookId: string | null;
}

interface UseBookExplorerDndReturn {
  activeId: UniqueIdentifier | null;
  activeItemData: ChapterInfo | { scene: SceneInfo; chapterId: string } | null;
  handleDragStart: (event: DragStartEvent) => void;
  handleDragEnd: (event: DragEndEvent) => void;
  handleDragCancel: () => void;
}

/**
 * Custom hook to encapsulate dnd-kit logic for the Books Explorer.
 */
export const useBookExplorerDnd = ({
                                     bookStructure,
                                     selectedBookId,
                                   }: UseBookExplorerDndProps): UseBookExplorerDndReturn => {
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  
  // Find active item data for overlay rendering
  const activeItemData = useMemo(() => {
    if (!activeId || !bookStructure) return null;
    const parsed = parseItemId(activeId);
    if (parsed.type === 'chapter') {
      return bookStructure.chapters.find((ch) => ch.id === parsed.realId) || null;
    }
    if (parsed.type === 'scene') {
      for (const chapter of bookStructure.chapters) {
        const scene = chapter.scenes.find((s) => s.id === parsed.realId);
        if (scene) return {scene, chapterId: chapter.id};
      }
    }
    return null;
  }, [activeId, bookStructure]);
  
  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id);
    console.debug(`[useBookExplorerDnd] DragStart: ${event.active.id}`);
  }, []);
  
  const handleDragCancel = useCallback(() => {
    setActiveId(null);
    console.debug('[useBookExplorerDnd] DragCancel');
  }, []);
  
  const handleDragEnd = useCallback(async (event: DragEndEvent) => {
    setActiveId(null); // Reset activeId regardless of outcome
    const {active, over} = event;
    
    if (!over || !bookStructure || !selectedBookId) {
      console.debug("[useBookExplorerDnd] Drag end cancelled: no target or structure/book missing.");
      return;
    }
    
    if (active.id === over.id) {
      console.debug("[useBookExplorerDnd] Drag end cancelled: dropped on self.");
      return; // Dropped on self
    }
    
    const activeParsed = parseItemId(active.id);
    const overParsed = parseItemId(over.id);
    
    console.debug(`[useBookExplorerDnd] DragEnd: Active=${activeParsed.type}:${activeParsed.realId}, Over=${overParsed.type}:${overParsed.realId}`);
    
    // --- Chapter Reordering ---
    if (activeParsed.type === 'chapter' && overParsed.type === 'chapter') {
      const oldIndex = bookStructure.chapters.findIndex((ch) => ch.id === activeParsed.realId);
      const newIndex = bookStructure.chapters.findIndex((ch) => ch.id === overParsed.realId);
      
      if (oldIndex === -1 || newIndex === -1) {
        console.error("[useBookExplorerDnd] Chapter reorder failed: index not found.");
        return;
      }
      
      const newOrderedChapters = arrayMove(bookStructure.chapters, oldIndex, newIndex);
      const newOrderedIds = newOrderedChapters.map(ch => ch.id);
      
      optimisticallyReorderChapters(newOrderedIds);
      try {
        const args: CommandsExecuteArgs = {
          commandId: commands.REORDER_CHAPTERS,
          args: {bookId: selectedBookId, orderedChapterIds: newOrderedIds}
        };
        await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args);
        console.debug("[useBookExplorerDnd] Chapter reorder IPC success.");
      } catch (err: unknown) {
        const errorData = err as IpcErrorData;
        console.error("[useBookExplorerDnd] IPC Error reordering chapters:", errorData?.message, err);
        await useBooksStore.getState().loadStructure(selectedBookId); // Rollback on error
      }
      return;
    }
    
    // --- Scene Reordering / Moving ---
    if (activeParsed.type === 'scene') {
      let sourceChapter: ChapterInfo | undefined;
      let sourceSceneIndex = -1;
      for (const ch of bookStructure.chapters) {
        const index = ch.scenes.findIndex((s) => s.id === activeParsed.realId);
        if (index !== -1) {
          sourceChapter = ch;
          sourceSceneIndex = index;
          break;
        }
      }
      
      if (!sourceChapter || sourceSceneIndex === -1) {
        console.error("[useBookExplorerDnd] Scene drag failed: source scene/chapter not found.");
        return;
      }
      
      let targetChapter: ChapterInfo | undefined;
      let targetSceneIndex = -1;
      let isOverChapter = false;
      
      if (overParsed.type === 'scene') {
        for (const ch of bookStructure.chapters) {
          const index = ch.scenes.findIndex((s) => s.id === overParsed.realId);
          if (index !== -1) {
            targetChapter = ch;
            targetSceneIndex = index;
            break;
          }
        }
      } else if (overParsed.type === 'chapter') {
        targetChapter = bookStructure.chapters.find((ch) => ch.id === overParsed.realId);
        isOverChapter = true;
        // When dropping ON a chapter, determine if it should be placed at the beginning or end
        // For simplicity here, we append to the end. More complex logic could check drop position relative to chapter bounds.
        targetSceneIndex = targetChapter?.scenes.length ?? 0;
      }
      
      if (!targetChapter) {
        console.error("[useBookExplorerDnd] Scene drag failed: target chapter not found.");
        return;
      }
      
      // Case 1: Reordering within the same chapter
      if (sourceChapter.id === targetChapter.id && !isOverChapter) {
        if (sourceSceneIndex === targetSceneIndex) return; // No change needed
        
        const reorderedScenes = arrayMove(sourceChapter.scenes, sourceSceneIndex, targetSceneIndex);
        const reorderedSceneIds = reorderedScenes.map(s => s.id);
        
        optimisticallyReorderScenes(sourceChapter.id, reorderedSceneIds);
        try {
          const args: CommandsExecuteArgs = {
            commandId: commands.REORDER_SCENES,
            args: {chapterId: sourceChapter.id, orderedSceneIds: reorderedSceneIds}
          };
          await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args);
          console.debug("[useBookExplorerDnd] Scene reorder IPC success.");
        } catch (err: unknown) {
          const errorData = err as IpcErrorData;
          console.error("[useBookExplorerDnd] IPC Error reordering scenes:", errorData?.message, err);
          useBooksStore.getState().loadStructure(selectedBookId); // Rollback
        }
        
        // Case 2: Moving scene to a different chapter (or onto a chapter header)
      } else {
        // targetSceneIndex is calculated correctly for dropping onto chapter or between scenes
        optimisticallyMoveScene(activeParsed.realId, sourceChapter.id, targetChapter.id, targetSceneIndex);
        try {
          const args: CommandsExecuteArgs = {
            commandId: commands.MOVE_SCENE,
            args: {
              sceneId: activeParsed.realId,
              chapterId: targetChapter.id,
              position: targetSceneIndex
            }
          };
          await ipcRendererService.invoke(IpcChannels.COMMANDS_EXECUTE, args);
          console.debug("[useBookExplorerDnd] Scene move IPC success.");
        } catch (err: unknown) {
          const errorData = err as IpcErrorData;
          console.error("[useBookExplorerDnd] IPC Error moving scene:", errorData?.message, err);
          useBooksStore.getState().loadStructure(selectedBookId); // Rollback
        }
      }
    }
    
  }, [bookStructure, selectedBookId]);
  
  return {
    activeId,
    activeItemData,
    handleDragStart,
    handleDragEnd,
    handleDragCancel,
  };
};
