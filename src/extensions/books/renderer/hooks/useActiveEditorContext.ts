import { useState, useEffect } from 'react';
import { ipcRendererService } from '@renderer/core/services/ipcRendererService';
import { IpcChannels } from '@shared/constants/ipc-channels';
import { SCENE_EDITOR } from '../../shared/constants/editors';
// Import ContextKey and remove UnknownData import
import type { ContextChangedEventData, ContextGetValueArgs, ContextGetValueResult, IpcErrorData, ContextKey } from '@shared/types/ipc';

// Add interface for editor context type
interface EditorContext {
    editorType: string;
    dataId: string;
    [key: string]: unknown;
}

/**
 * Custom hook to get and subscribe to the active scene ID from the main process context.
 * @returns The ID of the currently active scene editor, or null if none is active or it's not a scene editor.
 */
export const useActiveEditorContext = (): string | null => {
    const [activeSceneId, setActiveSceneId] = useState<string | null>(null);

    useEffect(() => {
        let isMounted = true;
        let unsubscribeContext: (() => void) | undefined;

        // Function to update local state based on context value
        const updateActiveScene = (contextValue: unknown) => { // Use unknown instead of UnknownData
            let sceneId: string | null = null;
            // Type guard to check if the context value has the expected properties
            if (contextValue && typeof contextValue === 'object' && contextValue !== null) {
                const ctx = contextValue as EditorContext;
                if (ctx.editorType === SCENE_EDITOR && typeof ctx.dataId === 'string') {
                    sceneId = ctx.dataId;
                }
            }
            
            if (isMounted) {
                setActiveSceneId(sceneId);
                console.log(`[useActiveEditorContext] Updated activeSceneId to: ${sceneId}`);
            }
        };

        // Get initial value using ipcRendererService
        const getInitialContext = async () => {
            try {
                // Cast string literal to ContextKey
                const args: ContextGetValueArgs = { key: 'activeEditorContext' as ContextKey };
                const initialContext = await ipcRendererService.invoke<ContextGetValueResult>(IpcChannels.CONTEXT_GET, args);
                console.log('[useActiveEditorContext] Received initial activeEditorContext:', initialContext);
                if (isMounted) {
                    updateActiveScene(initialContext);
                }
            } catch (err) {
                const errorData = err as IpcErrorData;
                console.error("[useActiveEditorContext] Failed to get initial context:", errorData.message, err);
            }
        };

        getInitialContext();

        // Subscribe to changes using ipcRendererService
        try {
            unsubscribeContext = ipcRendererService.on<ContextChangedEventData>(IpcChannels.CONTEXT_CHANGED, (payload) => {
                console.log(`[useActiveEditorContext] Received ${IpcChannels.CONTEXT_CHANGED} for activeEditorContext:`, payload.value);
                if (payload && payload.key === 'activeEditorContext') {
                    if (isMounted) {
                        updateActiveScene(payload.value);
                    }
                }
            });
        } catch (error) {
            console.error("[useActiveEditorContext] Failed to subscribe to context changes:", error);
        }

        return () => {
            isMounted = false;
            unsubscribeContext?.(); // Unsubscribe on cleanup
        };
    }, []); // Run only on mount

    return activeSceneId;
};
