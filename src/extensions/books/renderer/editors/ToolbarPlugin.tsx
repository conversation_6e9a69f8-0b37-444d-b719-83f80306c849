import React, { useState, useEffect, useCallback } from "react";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  $getSelection,
  $isRangeSelection,
  $isRootOrShadowRoot,
  FORMAT_TEXT_COMMAND,
  FORMAT_ELEMENT_COMMAND, // Import command for element formatting
  SELECTION_CHANGE_COMMAND,
  COMMAND_PRIORITY_CRITICAL,
  COMMAND_PRIORITY_LOW,
  LexicalNode,
  TextFormatType,
  $createParagraphNode,
  ElementFormatType,
  $isElementNode, // Import $isElementNode
} from "lexical";
import { $setBlocksType } from "@lexical/selection";
import {
  $isHeadingNode,
  // $isQuoteNode, // Not used currently
  $createQuoteNode,
  $createHeadingNode,
  HeadingTagType,
} from "@lexical/rich-text";
import {
  $isListNode,
  ListNode,
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
  REMOVE_LIST_COMMAND,
} from "@lexical/list";
import { $createCodeNode, $isCodeNode } from "@lexical/code";
import {
  $getNearestNodeOfType,
  mergeRegister,
  $findMatchingParent,
} from "@lexical/utils";
import { Icon } from "../../renderer/components/Icon";

// Props for the ToolbarPlugin
interface ToolbarPluginProps {
  onAutocomplete?: () => void; // Callback to trigger autocomplete
  isStreaming?: boolean; // To disable button during streaming
}

// Basic styling using theme variables
const toolbarStyles: React.CSSProperties = {
  display: "flex",
  marginBottom: "1px",
  background: "var(--app-editorGroupHeader-tabsBackground)",
  padding: "4px",
  borderBottom: "1px solid var(--app-border)",
  flexWrap: "wrap",
};

const buttonStyles: React.CSSProperties = {
  border: "none",
  backgroundColor: "transparent",
  padding: "4px",
  cursor: "pointer",
  marginRight: "2px",
  color: "var(--app-foreground)",
  borderRadius: "3px",
  display: "flex",
  alignItems: "center",
};

const buttonActiveStyles: React.CSSProperties = {
  ...buttonStyles,
  backgroundColor: "var(--app-list-activeSelectionBackground)",
  color: "var(--app-tab-activeForeground)",
};

const separatorStyles: React.CSSProperties = {
  width: "1px",
  backgroundColor: "var(--app-border)",
  margin: "4px 6px",
};

// Define block types we want to handle
const blockTypeToBlockName = {
  bullet: "Bulleted List",
  check: "Check List",
  code: "Code Block",
  h1: "Heading 1",
  h2: "Heading 2",
  h3: "Heading 3",
  number: "Numbered List",
  paragraph: "Normal",
  quote: "Quote",
};
// Explicitly define the BlockType union to ensure all keys are included
type BlockType =
  | "paragraph"
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "bullet"
  | "number"
  | "check"
  | "quote"
  | "code";

// Update component to accept props
export function ToolbarPlugin({
  onAutocomplete,
  isStreaming,
}: ToolbarPluginProps) {
  const [editor] = useLexicalComposerContext();
  const [activeEditor] = useState(editor);
  const [blockType, setBlockType] = useState<BlockType>("paragraph");
  const [elementFormat, setElementFormat] = useState<ElementFormatType>("left"); // Add state for element format
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [isStrikethrough, setIsStrikethrough] = useState(false);
  const [isCode, setIsCode] = useState(false);

  const updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      // Update text format states
      setIsBold(selection.hasFormat("bold"));
      setIsItalic(selection.hasFormat("italic"));
      setIsUnderline(selection.hasFormat("underline"));
      setIsStrikethrough(selection.hasFormat("strikethrough"));
      setIsCode(selection.hasFormat("code"));

      // Update block type and element format state
      const anchorNode = selection.anchor.getNode();
      let element =
        anchorNode.getKey() === "root"
          ? anchorNode
          : $findMatchingParent(anchorNode, (e: LexicalNode) => {
              const parent = e.getParent();
              return parent !== null && $isRootOrShadowRoot(parent);
            });

      if (element === null) {
        element = anchorNode.getTopLevelElementOrThrow();
      }

      const elementKey = element.getKey();
      const elementDOM = activeEditor.getElementByKey(elementKey);

      if (elementDOM !== null) {
        if ($isElementNode(element)) {
          setElementFormat(element.getFormatType());
        }

        // Update block type
        if ($isListNode(element)) {
          const parentList = $getNearestNodeOfType<ListNode>(
            anchorNode,
            ListNode
          );
          const type = parentList
            ? parentList.getListType()
            : element.getListType();
          setBlockType(type);
        } else {
          const type = $isHeadingNode(element)
            ? element.getTag()
            : element.getType();
          if (type in blockTypeToBlockName) {
            setBlockType(type as BlockType);
          } else {
            setBlockType("paragraph");
          }
          if ($isCodeNode(element)) {
            setBlockType("code");
          }
        }
      }
    }
  }, [activeEditor]);

  useEffect(() => {
    return mergeRegister(
      activeEditor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updateToolbar();
        });
      }),
      activeEditor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        () => {
          updateToolbar();
          return false;
        },
        COMMAND_PRIORITY_CRITICAL
      ),
      activeEditor.registerCommand(
        FORMAT_TEXT_COMMAND,
        () => {
          updateToolbar();
          return false;
        },
        COMMAND_PRIORITY_LOW
      ),
      // Listen for element format changes too
      activeEditor.registerCommand(
        FORMAT_ELEMENT_COMMAND,
        () => {
          updateToolbar();
          return false;
        },
        COMMAND_PRIORITY_LOW
      )
    );
  }, [activeEditor, updateToolbar]);

  // --- Format Handlers with Optimistic Update ---

  const formatText = (format: TextFormatType) => {
    if (format === "bold") setIsBold(!isBold);
    if (format === "italic") setIsItalic(!isItalic);
    if (format === "underline") setIsUnderline(!isUnderline);
    if (format === "strikethrough") setIsStrikethrough(!isStrikethrough);
    if (format === "code") setIsCode(!isCode);
    activeEditor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  };

  const formatElement = (format: ElementFormatType) => {
    // Optimistically update state
    setElementFormat(format);
    // Dispatch command
    activeEditor.dispatchCommand(FORMAT_ELEMENT_COMMAND, format);
  };

  const formatHeading = (tag: HeadingTagType) => {
    if (blockType !== tag) {
      setBlockType(tag);
      activeEditor.update(() => {
        const selection = $getSelection();
        // Log the selection state just before applying block type
        console.log(
          "[ToolbarPlugin] Applying heading. Selection state:",
          selection ? JSON.stringify(selection) : "null"
        );
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, () => $createHeadingNode(tag));
        }
      });
    } else {
      setBlockType("paragraph");
      activeEditor.update(() => {
        const selection = $getSelection();
        // Log the selection state just before applying block type
        console.log(
          "[ToolbarPlugin] Toggling heading to paragraph. Selection state:",
          selection ? JSON.stringify(selection) : "null"
        );
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, $createParagraphNode);
        }
      });
    }
  };

  const formatBulletList = () => {
    if (blockType !== "bullet") {
      setBlockType("bullet");
      activeEditor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
    } else {
      setBlockType("paragraph");
      activeEditor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }
  };

  const formatNumberedList = () => {
    if (blockType !== "number") {
      setBlockType("number");
      activeEditor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
    } else {
      setBlockType("paragraph");
      activeEditor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }
  };

  const formatQuote = () => {
    if (blockType !== "quote") {
      setBlockType("quote");
      activeEditor.update(() => {
        const selection = $getSelection();
        // Log the selection state just before applying block type
        console.log(
          "[ToolbarPlugin] Applying quote. Selection state:",
          selection ? JSON.stringify(selection) : "null"
        );
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, $createQuoteNode);
        }
      });
    } else {
      setBlockType("paragraph");
      activeEditor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, $createParagraphNode);
        }
      });
    }
  };

  const formatCode = () => {
    if (blockType !== "code") {
      setBlockType("code");
      activeEditor.update(() => {
        const selection = $getSelection();
        // Log the selection state just before applying block type
        console.log(
          "[ToolbarPlugin] Applying code block. Selection state:",
          selection ? JSON.stringify(selection) : "null"
        );
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, $createCodeNode);
        }
      });
    } else {
      setBlockType("paragraph");
      activeEditor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, $createParagraphNode);
        }
      });
    }
  };

  return (
    // Add className for CSS targeting
    <div style={toolbarStyles} className="editor-toolbar">
      {/* Text Formats */}
      <button
        onClick={() => formatText("bold")}
        style={isBold ? buttonActiveStyles : buttonStyles}
        title="Bold (Ctrl+B)"
        aria-label="Format text as bold"
      >
        <Icon name="Bold" size={18} />
      </button>
      <button
        onClick={() => formatText("italic")}
        style={isItalic ? buttonActiveStyles : buttonStyles}
        title="Italic (Ctrl+I)"
        aria-label="Format text as italics"
      >
        <Icon name="Italic" size={18} />
      </button>
      <button
        onClick={() => formatText("underline")}
        style={isUnderline ? buttonActiveStyles : buttonStyles}
        title="Underline (Ctrl+U)"
        aria-label="Format text to underlined"
      >
        <Icon name="Underline" size={18} />
      </button>
      <button
        onClick={() => formatText("strikethrough")}
        style={isStrikethrough ? buttonActiveStyles : buttonStyles}
        title="Strikethrough"
        aria-label="Format text with a strikethrough"
      >
        <Icon name="Strikethrough" size={18} />
      </button>
      <button
        onClick={() => formatText("code")}
        style={isCode ? buttonActiveStyles : buttonStyles}
        title="Inline Code"
        aria-label="Format text as inline code"
      >
        <Icon name="Code" size={18} />
      </button>

      {/* Separator */}
      <div style={separatorStyles}></div>

      {/* Block Formats */}
      <button
        onClick={() => formatHeading("h1")}
        style={blockType === "h1" ? buttonActiveStyles : buttonStyles}
        title="Heading 1"
        aria-label="Format block as Heading 1"
      >
        <Icon name="Heading1" size={18} />
      </button>
      <button
        onClick={() => formatHeading("h2")}
        style={blockType === "h2" ? buttonActiveStyles : buttonStyles}
        title="Heading 2"
        aria-label="Format block as Heading 2"
      >
        <Icon name="Heading2" size={18} />
      </button>
      <button
        onClick={() => formatHeading("h3")}
        style={blockType === "h3" ? buttonActiveStyles : buttonStyles}
        title="Heading 3"
        aria-label="Format block as Heading 3"
      >
        <Icon name="Heading3" size={18} />
      </button>
      <button
        onClick={() => formatHeading("h4")}
        style={blockType === "h4" ? buttonActiveStyles : buttonStyles}
        title="Heading 4" // Corrected title
        aria-label="Format block as Heading 4" // Corrected label
      >
        <Icon name="Heading4" size={18} />
      </button>
      <button
        onClick={formatQuote}
        style={blockType === "quote" ? buttonActiveStyles : buttonStyles}
        title="Quote"
        aria-label="Format block as quote"
      >
        <Icon name="Quote" size={18} />
      </button>
      <button
        onClick={formatCode}
        style={blockType === "code" ? buttonActiveStyles : buttonStyles}
        title="Code Block"
        aria-label="Format block as code block"
      >
        <Icon name="SquareCode" size={18} />
      </button>

      {/* Separator */}
      <div style={separatorStyles}></div>

      {/* List Formats */}
      <button
        onClick={formatBulletList}
        style={blockType === "bullet" ? buttonActiveStyles : buttonStyles}
        title="Bulleted List"
        aria-label="Format text as bulleted list"
      >
        <Icon name="List" size={18} />
      </button>
      <button
        onClick={formatNumberedList}
        style={blockType === "number" ? buttonActiveStyles : buttonStyles}
        title="Numbered List"
        aria-label="Format text as numbered list"
      >
        <Icon name="ListOrdered" size={18} />
      </button>

      {/* Separator */}
      <div style={separatorStyles}></div>

      {/* Element Alignment */}
      <button
        onClick={() => formatElement("left")}
        style={elementFormat === "left" ? buttonActiveStyles : buttonStyles}
        title="Align Left"
        aria-label="Align text to the left"
      >
        <Icon name="AlignLeft" size={18} />
      </button>
      <button
        onClick={() => formatElement("center")}
        style={elementFormat === "center" ? buttonActiveStyles : buttonStyles}
        title="Align Center"
        aria-label="Align text to the center"
      >
        <Icon name="AlignCenter" size={18} />
      </button>
      <button
        onClick={() => formatElement("right")}
        style={elementFormat === "right" ? buttonActiveStyles : buttonStyles}
        title="Align Right"
        aria-label="Align text to the right"
      >
        <Icon name="AlignRight" size={18} />
      </button>
      <button
        onClick={() => formatElement("justify")}
        style={elementFormat === "justify" ? buttonActiveStyles : buttonStyles}
        title="Justify"
        aria-label="Justify text alignment"
      >
        <Icon name="AlignJustify" size={18} />
      </button>

      {/* Separator */}
      <div style={separatorStyles}></div>

      {/* AI Autocomplete Button */}
      <button
        onClick={onAutocomplete} // Call the passed callback
        disabled={isStreaming} // Disable when streaming
        style={buttonStyles}
        title="Autocomplete Text"
        aria-label="Autocomplete text using AI"
      >
        <Icon name="Wand" size={18} /> {/* Corrected icon name */}
        {isStreaming ? " Thinking..." : ""} {/* Show thinking state */}
      </button>

      {/* TODO: Add Indent/Outdent buttons */}
    </div>
  );
}
