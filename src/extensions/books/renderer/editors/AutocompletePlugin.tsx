import React, { useEffect, useCallback, useRef } from "react";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  $getSelection,
  $isRangeSelection,
  $getRoot,
  $getNodeBy<PERSON>ey,
  NodeKey,
  RangeSelection,
  COMMAND_PRIORITY_NORMAL,
  LexicalNode,
} from "lexical";
import {
  $createAISuggestionNode,
  $isAISuggestionNode,
  AISuggestionNode,
} from "./nodes/AISuggestionNode";
import { RETRY_AI_SUGGESTION_COMMAND } from "./components/AISuggestionActions";
import type { IpcErrorData } from "@shared/types/ipc";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import * as ipcChannels from "../../shared/constants/ipc-channels";
import { mergeRegister } from "@lexical/utils";

// --- Types for Streaming ---
interface TokenStreamPayload {
  requestId: string;
  token?: string;
  error?: IpcErrorData;
  isDone: boolean;
}

// --- Autocomplete Plugin ---
interface AutocompletePluginProps {
  sceneId: string | null;
  setIsStreaming: React.Dispatch<React.SetStateAction<boolean>>;
  setStreamError: React.Dispatch<React.SetStateAction<string | null>>;
  triggerRef: React.MutableRefObject<(() => Promise<void>) | null>;
}

export function AutocompletePlugin({
  sceneId,
  setIsStreaming,
  setStreamError,
  triggerRef,
}: AutocompletePluginProps): null {
  const [editor] = useLexicalComposerContext();
  const currentRequestId = useRef<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const activeSuggestionNodeKey = useRef<NodeKey | null>(null);

  // Modified triggerAutocomplete to accept optional nodeKey for retry
  const triggerAutocomplete = useCallback(
    async (retryNodeKey?: NodeKey) => {
      if (!sceneId) return;

      let textBeforeCursor = "";
      let initialSelection: RangeSelection | null = null; // Used for inserting NEW node

      // Calculate textBeforeCursor differently depending on whether it's a retry
      if (retryNodeKey) {
        // --- Calculate textBeforeCursor for RETRY ---
        let calculatedTextBeforeRetry = "";
        editor.getEditorState().read(() => {
          const suggestionNode = $getNodeByKey(retryNodeKey);
          if ($isAISuggestionNode(suggestionNode)) {
            const root = $getRoot();
            const allTextNodes = root.getAllTextNodes(); // Get all text nodes in order
            let accumulatedText = "";
            for (const textNode of allTextNodes) {
              // Check if the current text node is the suggestion node itself or inside it
              let isInsideSuggestion = false;
              let parent: LexicalNode | null = textNode; // Start with the node itself
              while (parent) {
                if (parent.getKey() === retryNodeKey) {
                  isInsideSuggestion = true;
                  break;
                }
                parent = parent.getParent();
              }

              if (isInsideSuggestion) {
                break; // Stop accumulating when we hit the suggestion node or its contents
              }
              accumulatedText += textNode.getTextContent();
            }
            calculatedTextBeforeRetry = accumulatedText;

            if (calculatedTextBeforeRetry.length > 2000) {
              // Limit context length
              calculatedTextBeforeRetry = calculatedTextBeforeRetry.substring(
                calculatedTextBeforeRetry.length - 2000
              );
            }
            console.log(
              `[AutocompletePlugin] Recalculated textBeforeCursor for retry: "...${calculatedTextBeforeRetry.slice(
                -50
              )}"`
            );
          } else {
            console.error(
              `[AutocompletePlugin] Node for retry key ${retryNodeKey} not found or invalid.`
            );
            calculatedTextBeforeRetry = ""; // Set to empty if node not found
          }
        });
        textBeforeCursor = calculatedTextBeforeRetry; // Update the main variable
        // --- End Calculate textBeforeCursor for RETRY ---
      } else {
        // --- Calculate textBeforeCursor for INITIAL trigger ---
        editor.getEditorState().read(() => {
          const selection = $getSelection();
          if ($isRangeSelection(selection) && selection.isCollapsed()) {
            initialSelection = selection.clone(); // Store selection only for new node insertion
            const anchor = selection.anchor;
            const root = $getRoot();
            let text = "";
            // const anchorNode = anchor.getNode(); // Not used currently
            const nodes = root.getAllTextNodes();
            for (const node of nodes) {
              text += node.getTextContent();
              if (node.getKey() === anchor.key) {
                text = text.substring(
                  0,
                  text.length - node.getTextContentSize() + anchor.offset
                );
                break;
              }
            }
            textBeforeCursor = text;
            if (textBeforeCursor.length > 2000) {
              // Limit context length
              textBeforeCursor = textBeforeCursor.substring(
                textBeforeCursor.length - 2000
              );
            }
          } else {
            return; // Exit if selection is not valid
          }
        });
        // --- End Calculate textBeforeCursor for INITIAL trigger ---
      }

      // If retrying, use the provided node key, otherwise insert a new one
      let suggestionNodeKey: NodeKey | null = retryNodeKey ?? null;

      if (!retryNodeKey) {
        // --- Insert New Suggestion Node ---
        if (!textBeforeCursor || !initialSelection) {
          console.warn(
            "[AutocompletePlugin] No text before cursor or invalid selection for new suggestion."
          );
          return;
        }
        editor.update(() => {
          if (!initialSelection) return;
          const currentSelection = $getSelection();
          if (
            !$isRangeSelection(currentSelection) ||
            !currentSelection.isCollapsed()
          ) {
            console.warn(
              "[AutocompletePlugin] Selection changed before new suggestion node insertion."
            );
            return;
          }
          const suggestionNode = $createAISuggestionNode(); // Create empty
          currentSelection.insertNodes([suggestionNode]);
          suggestionNodeKey = suggestionNode.getKey();
          console.log(
            `[AutocompletePlugin] Inserted NEW AISuggestionNode with key: ${suggestionNodeKey}`
          );
        });

        if (!suggestionNodeKey) {
          console.error(
            "[AutocompletePlugin] Failed to insert NEW AISuggestionNode."
          );
          return;
        }
        // --- End Insert New Suggestion Node ---
      } else {
        console.log(
          `[AutocompletePlugin] Retrying on existing node key: ${retryNodeKey}`
        );
        // Verify the node still exists before proceeding (already done implicitly by text calc)
        if (!textBeforeCursor && retryNodeKey) {
          // Check if text calc failed for retry
          console.warn(
            "[AutocompletePlugin] Could not determine text before cursor for retry."
          );
          return;
        }
      }

      // Ensure we have a node key to work with
      if (!suggestionNodeKey) {
        console.error(
          "[AutocompletePlugin] No valid suggestion node key to proceed."
        );
        return;
      }
      activeSuggestionNodeKey.current = suggestionNodeKey; // Set the active key

      setIsStreaming(true);
      setStreamError(null);
      console.log(
        `[AutocompletePlugin] Requesting autocomplete for scene ${sceneId} with context ending: "...${textBeforeCursor.slice(
          -50
        )}"`
      );

      // Clean up previous listener if any
      unsubscribeRef.current?.();
      unsubscribeRef.current = null;

      try {
        const result = await ipcRendererService.invoke<
          { requestId: string } | IpcErrorData
        >(ipcChannels.AUTOCOMPLETE_SCENE_STREAM_START, {
          textBeforeCursor,
        });

        if (
          result &&
          typeof result === "object" &&
          "code" in result &&
          "message" in result
        ) {
          setStreamError(`Failed to start AI: ${result.message}`);
          setIsStreaming(false);
          editor.update(() => {
            if (activeSuggestionNodeKey.current) {
              const node = $getNodeByKey(activeSuggestionNodeKey.current);
              if ($isAISuggestionNode(node)) {
                node.remove();
              }
              activeSuggestionNodeKey.current = null;
            }
          });
          return;
        }

        if (result && "requestId" in result) {
          currentRequestId.current = result.requestId;
          const streamChannel = `ai:tokenStream:${result.requestId}`;

          unsubscribeRef.current = ipcRendererService.on<TokenStreamPayload>(
            streamChannel,
            (payload) => {
              if (payload.requestId !== currentRequestId.current) return;

              editor.update(() => {
                const suggestionNode = activeSuggestionNodeKey.current
                  ? $getNodeByKey<AISuggestionNode>(
                      activeSuggestionNodeKey.current
                    )
                  : null;

                if (!$isAISuggestionNode(suggestionNode)) {
                  console.warn(
                    `[AutocompletePlugin] AISuggestionNode ${activeSuggestionNodeKey.current} not found or invalid during stream.`
                  );
                  setIsStreaming(false);
                  unsubscribeRef.current?.();
                  unsubscribeRef.current = null;
                  activeSuggestionNodeKey.current = null;
                  return;
                }

                if (payload.error) {
                  setStreamError(`AI Error: ${payload.error.message}`);
                  suggestionNode.remove();
                  setIsStreaming(false);
                  unsubscribeRef.current?.();
                  unsubscribeRef.current = null;
                  activeSuggestionNodeKey.current = null;
                } else if (payload.token !== undefined) {
                  suggestionNode.appendText(payload.token);
                }

                if (payload.isDone) {
                  suggestionNode.setComplete(true);
                  setIsStreaming(false);
                  unsubscribeRef.current?.();
                  unsubscribeRef.current = null;
                  console.log(
                    `[AutocompletePlugin] Streaming done for node ${activeSuggestionNodeKey.current}`
                  );
                }
              }); // end editor.update
            }
          );
        } else {
          setStreamError(
            "Failed to start AI: Invalid response from main process."
          );
          setIsStreaming(false);
          editor.update(() => {
            if (activeSuggestionNodeKey.current) {
              const node = $getNodeByKey(activeSuggestionNodeKey.current);
              if ($isAISuggestionNode(node)) {
                node.remove();
              }
              activeSuggestionNodeKey.current = null;
            }
          });
        }
      } catch (error) {
        const ipcError = error as IpcErrorData;
        console.error(
          "[AutocompletePlugin] IPC Error invoking stream start:",
          error
        );
        setStreamError(`IPC Error: ${ipcError.message || "Unknown IPC error"}`);
        setIsStreaming(false);
        editor.update(() => {
          if (activeSuggestionNodeKey.current) {
            const node = $getNodeByKey(activeSuggestionNodeKey.current);
            if ($isAISuggestionNode(node)) {
              node.remove();
            }
            activeSuggestionNodeKey.current = null;
          }
        });
      }
    },
    [sceneId, editor, setIsStreaming, setStreamError]
  );

  // Expose the trigger function via ref
  useEffect(() => {
    triggerRef.current = triggerAutocomplete;
    return () => {
      triggerRef.current = null;
    };
  }, [triggerAutocomplete, triggerRef]);

  // Cleanup listener on unmount
  useEffect(() => {
    return () => {
      unsubscribeRef.current?.();
    };
  }, []);

  // Register command listener for retry
  useEffect(() => {
    return mergeRegister(
      editor.registerCommand<NodeKey>(
        RETRY_AI_SUGGESTION_COMMAND,
        (payloadNodeKey) => {
          console.log(
            `[AutocompletePlugin] Received RETRY_AI_SUGGESTION_COMMAND for nodeKey: ${payloadNodeKey}`
          );
          triggerAutocomplete(payloadNodeKey); // Pass the key for retry
          return true;
        },
        COMMAND_PRIORITY_NORMAL
      )
    );
  }, [editor, triggerAutocomplete]); // Depend on triggerAutocomplete now

  return null;
}
