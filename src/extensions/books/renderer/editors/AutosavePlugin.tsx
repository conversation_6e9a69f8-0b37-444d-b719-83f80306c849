import React, {useCallback, useEffect, useRef, useState} from "react";
import {useLexicalComposerContext} from "@lexical/react/LexicalComposerContext";
import {debounce} from "lodash-es";
import type {IpcErrorData} from "@shared/types/ipc";
import {ipcRendererService} from "@renderer/core/services/ipcRendererService";
import {rendererEventBus} from "@renderer/core/services/RendererEventBus";
import * as ipcChannels from "../../shared/constants/ipc-channels";
import {SCENE_EDITOR} from "../../shared/constants/editors";

// Define default empty state constant
const DEFAULT_EMPTY_LEXICAL_STATE =
  '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}';

interface AutosavePluginProps {
  sceneId: string | null;
  initialJsonState: string | null; // Expecting initial JSON state string
}

export function AutosavePlugin({
                                 sceneId,
                                 initialJsonState,
                               }: AutosavePluginProps): React.JSX.Element {
  const [editor] = useLexicalComposerContext();
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const lastSavedJsonState = useRef<string | null>(null); // Store last saved JSON
  const isDirtyRef = useRef(false);
  const tabId = sceneId ? `${SCENE_EDITOR}:${sceneId}` : null;
  
  // Effect to initialize lastSavedJsonState and reset dirty state when initial state loads/changes
  useEffect(() => {
    console.log(
      `[Autosave Plugin] Initializing/Resetting for ${tabId} with initialJsonState:`,
      initialJsonState === null
        ? "null"
        : `"${initialJsonState.substring(0, 30)}..."`
    );
    // Initialize with the loaded JSON state, defaulting to empty state if null
    lastSavedJsonState.current =
      initialJsonState ?? DEFAULT_EMPTY_LEXICAL_STATE;
    isDirtyRef.current = false; // Start clean
    if (tabId) {
      // Ensure the workbench knows the initial state is clean
      rendererEventBus.emit("editorDirtyStateChanged", {
        tabId,
        isDirty: false,
      });
    }
  }, [initialJsonState, tabId]); // Depend on initialJsonState and tabId
  
  // Callback to perform the actual save operation
  const performSave = useCallback(
    async (force = false) => {
      if (!sceneId || !tabId) return;
      
      let currentJsonState = "";
      const editorState = editor.getEditorState();
      const stateJson = editorState.toJSON(); // Get the plain JS object
      
      // Log the raw state before filtering/saving for debugging structure issues
      console.log(
        "[Autosave Plugin] Current raw editor state JSON:",
        JSON.stringify(stateJson, null, 2)
      );
      
      // Filter out AISuggestionNode before saving
      const filterSuggestions = (node: { type: string; children: never[]; }): boolean => {
        if (node.type === "ai-suggestion") {
          return false; // Exclude this node
        }
        if (node.children) {
          // Recursively filter children
          node.children = node.children.filter(filterSuggestions);
        }
        return true;
      };
      
      // Create a deep copy to avoid modifying the original state object used by the editor
      // Using stringify/parse is a common way for deep cloning simple JSON structures
      const stateToSave = JSON.parse(JSON.stringify(stateJson));
      if (stateToSave.root && stateToSave.root.children) {
        // Filter nodes at the root level
        stateToSave.root.children =
          stateToSave.root.children.filter(filterSuggestions);
      }
      
      currentJsonState = JSON.stringify(stateToSave); // Stringify the filtered state
      
      const hasChanged = currentJsonState !== lastSavedJsonState.current;
      
      // Exit if not forcing save and content hasn't changed
      if (!force && !hasChanged) {
        if (isDirtyRef.current) {
          // If it was dirty but now matches saved, mark clean
          isDirtyRef.current = false;
          rendererEventBus.emit("editorDirtyStateChanged", {
            tabId,
            isDirty: false,
          });
        }
        return;
      }
      
      // Prevent saving default empty state unless forced or it was previously non-empty
      const isEmptyDefaultState =
        currentJsonState === DEFAULT_EMPTY_LEXICAL_STATE;
      const wasPreviouslyNonEmpty =
        lastSavedJsonState.current !== null &&
        lastSavedJsonState.current !== DEFAULT_EMPTY_LEXICAL_STATE;
      
      if (!force && isEmptyDefaultState && !wasPreviouslyNonEmpty) {
        console.log(
          `[Autosave Plugin] Skipping save for default empty state on scene ${sceneId}.`
        );
        if (isDirtyRef.current) {
          isDirtyRef.current = false;
          rendererEventBus.emit("editorDirtyStateChanged", {
            tabId,
            isDirty: false,
          });
        }
        return;
      }
      
      setIsSaving(true);
      setSaveError(null);
      console.log(
        `[Autosave Plugin] Saving scene ${sceneId} (Forced: ${force})...`
      );
      
      try {
        await ipcRendererService.invoke(ipcChannels.SAVE_SCENE_CONTENT, {
          sceneId,
          content: currentJsonState, // Send JSON string
        });
        console.log("[Autosave Plugin] JSON content saved via IPC");
        lastSavedJsonState.current = currentJsonState; // Update last saved JSON state
        isDirtyRef.current = false; // Mark as clean after successful save
        setSaveError(null);
        rendererEventBus.emit("editorDirtyStateChanged", {
          tabId,
          isDirty: false,
        }); // Emit clean state
      } catch (error) {
        const ipcError = error as IpcErrorData;
        console.error(
          "[Autosave Plugin] IPC Error during save:",
          ipcError.message,
          ipcError.details
        );
        setSaveError(`Ошибка сохранения: ${ipcError.message}`);
        // Keep dirty state true on save error
        if (!isDirtyRef.current) {
          isDirtyRef.current = true;
          rendererEventBus.emit("editorDirtyStateChanged", {
            tabId,
            isDirty: true,
          });
        }
      } finally {
        setIsSaving(false);
      }
    },
    [sceneId, editor, tabId]
  );
  
  // Ref to hold the latest performSave function for listeners
  const performSaveRef = useRef(performSave);
  useEffect(() => {
    performSaveRef.current = performSave;
  }, [performSave]);
  
  // Debounced save function for autosave
  const debouncedSave = useCallback(
    debounce(() => {
      performSaveRef.current(false); // Call non-forced save
    }, 2000), // 2-second debounce
    [] // No dependencies, relies on performSaveRef
  );
  
  // Listener for editor changes to track dirty state and trigger autosave
  useEffect(() => {
    if (!tabId) return;
    
    const removeUpdateListener = editor.registerUpdateListener(
      ({editorState, dirtyElements, dirtyLeaves}) => {
        // Only compare states if something actually changed or if we know we are dirty
        if (
          dirtyElements.size > 0 ||
          dirtyLeaves.size > 0 ||
          isDirtyRef.current
        ) {
          const currentJsonState = JSON.stringify(editorState.toJSON());
          
          // Avoid checking if initial content hasn't been set yet in the ref
          if (lastSavedJsonState.current === null) {
            // If initial state is null, the first valid state becomes the baseline
            if (currentJsonState !== DEFAULT_EMPTY_LEXICAL_STATE) {
              lastSavedJsonState.current = currentJsonState;
            }
            return;
          }
          
          const changed = currentJsonState !== lastSavedJsonState.current;
          
          if (changed && !isDirtyRef.current) {
            // Changed from clean to dirty
            isDirtyRef.current = true;
            rendererEventBus.emit("editorDirtyStateChanged", {
              tabId,
              isDirty: true,
            });
            console.log(`[Autosave Plugin] Scene ${tabId} marked as dirty.`);
            debouncedSave(); // Start debounce on first change
          } else if (!changed && isDirtyRef.current) {
            // Changed back to saved state (e.g., undo)
            isDirtyRef.current = false;
            rendererEventBus.emit("editorDirtyStateChanged", {
              tabId,
              isDirty: false,
            });
            console.log(
              `[Autosave Plugin] Scene ${tabId} marked as clean (reverted).`
            );
            debouncedSave.cancel(); // Cancel pending autosave if reverted
          } else if (changed && isDirtyRef.current) {
            // Content changed while already dirty, just restart debounce timer
            debouncedSave();
          }
        }
      }
    );
    
    return () => {
      removeUpdateListener(); // Clean up listener
      debouncedSave.cancel(); // Cancel any pending save on unmount
    };
  }, [editor, debouncedSave, tabId]); // Dependencies
  
  // Listener for forced save requests from main process
  useEffect(() => {
    if (!sceneId) return;
    const targetChannel = `editor:forceSave:${sceneId}`;
    const listener = () => {
      if (performSaveRef.current) {
        console.log(
          `[Autosave Plugin] Received force save trigger for ${sceneId} on channel ${targetChannel}`
        );
        debouncedSave.cancel(); // Cancel pending autosave
        performSaveRef.current(true); // Force immediate save
      } else {
        console.warn(
          `[Autosave Plugin] Received force save trigger for ${sceneId}, but performSaveRef is not available.`
        );
      }
    };
    const unsubscribe = ipcRendererService.on(targetChannel, listener);
    return () => {
      unsubscribe();
      debouncedSave.cancel(); // Cancel on unmount too
    };
  }, [sceneId, debouncedSave]); // Dependencies
  
  // Render a simple status indicator (optional)
  return (
    <div
      className="autosave-status"
      style={{
        position: "absolute",
        bottom: "5px",
        right: "10px",
        fontSize: "0.8em",
        opacity: 0.6,
      }}
    >
      {isSaving && <span>Saving...</span>}
      {saveError && <span style={{color: "red"}}>{saveError}</span>}
    </div>
  );
}
