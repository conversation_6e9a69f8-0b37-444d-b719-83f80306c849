import React, {
  useEffect,
  useState,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  LexicalComposer,
  InitialConfigType,
} from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin"; // Import AutoFocusPlugin
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { ListItemNode, ListNode } from "@lexical/list";
import { CodeNode, CodeHighlightNode } from "@lexical/code";
import { LinkNode } from "@lexical/link";
import { AICompletionNode } from "./nodes/AICompletionNode"; // Import the custom node
import { AISuggestionNode } from "./nodes/AISuggestionNode"; // Import the suggestion node
import type { IpcErrorData } from "@shared/types/ipc";
import { ipcRendererService } from "@renderer/core/services/ipcRendererService";
import * as ipcChannels from "../../shared/constants/ipc-channels";
import { ToolbarPlugin } from "./ToolbarPlugin";
import { AutosavePlugin } from "./AutosavePlugin";
import { AutocompletePlugin } from "./AutocompletePlugin";
import "./Editor.editor.css";

interface EditorProps {
  dataId: string | null; // The ID of the scene to edit, sent by the ActiveEditorArea
}
export const Editor: React.FC<EditorProps> = ({ dataId }) => {
  const sceneId = dataId;
  const [initialJsonState, setInitialJsonState] = useState<string | null>(null); // Changed state name
  const [isLoading, setIsLoading] = useState(false);
  const [loadError, setLoadError] = useState<IpcErrorData | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamError, setStreamError] = useState<string | null>(null);
  const triggerAutocompleteRef = useRef<(() => Promise<void>) | null>(null);

  useEffect(() => {
    const loadContent = async () => {
      if (!sceneId) {
        setInitialJsonState(null); // Changed state setter
        setIsLoading(false);
        setLoadError(null);
        return;
      }
      setIsLoading(true);
      setLoadError(null);
      setInitialJsonState(null); // Changed state setter
      console.log(
        `[Book Editor] Loading JSON content for scene ${sceneId}...` // Updated log
      );
      try {
        // Assuming GET_SCENE_CONTENT now returns JSON string or null
        const jsonContent = await ipcRendererService.invoke<string | null>(
          ipcChannels.GET_SCENE_CONTENT,
          { sceneId }
        );
        console.log(
          `[Book Editor] JSON content loaded for scene ${sceneId}` // Updated log
        );
        // Set the initial JSON state, default to empty state if null/empty
        const DEFAULT_EMPTY_LEXICAL_STATE =
          '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}';
        setInitialJsonState(jsonContent || DEFAULT_EMPTY_LEXICAL_STATE); // Changed state setter
      } catch (error) {
        const ipcError = error as IpcErrorData;
        console.error(
          `[Book Editor] IPC error loading scene ${sceneId}:`,
          ipcError.message,
          ipcError.details
        );
        setLoadError({
          code: ipcError.code || "ipc_call_failed",
          message: ipcError.message,
        });
      } finally {
        setIsLoading(false);
      }
    };
    loadContent();
  }, [sceneId]);
  
  const initialConfig: InitialConfigType = useMemo(
    () => ({
      namespace: "AIBooksEditor",
      theme: {
        paragraph: "editor-paragraph",
        heading: {
          h1: "editor-heading-h1",
          h2: "editor-heading-h2",
          h3: "editor-heading-h3",
          h4: "editor-heading-h4",
          h5: "editor-heading-h5",
          h6: "editor-heading-h6",
        },
        list: { ul: "editor-list-ul", ol: "editor-list-ol" },
        listItem: "editor-list-item",
        quote: "editor-quote",
        code: "editor-code",
      },
      onError(error: Error) {
        console.error("[Book Editor] Lexical Error:", error);
      },
      // Set initial state directly from the loaded JSON string
      editorState: initialJsonState, // Pass the loaded JSON string here
      editable: !!sceneId && !isLoading && !loadError,
      nodes: [
        HeadingNode,
        QuoteNode,
        ListNode,
        ListItemNode,
        CodeNode,
        CodeHighlightNode,
        LinkNode,
        AICompletionNode, // Register the custom node
        AISuggestionNode, // Register the suggestion node
      ],
      // Removed the duplicate editorState: null here
    }),
    [sceneId, isLoading, loadError, initialJsonState] // Add initialJsonState to dependencies
  );

  const handleAutocompleteTrigger = useCallback(() => {
    triggerAutocompleteRef.current?.();
  }, []);

  if (isLoading)
    return <div className="editor-loading">Загрузка контента сцены...</div>;
  if (loadError)
    return (
      <div className="editor-error">Ошибка загрузки: {loadError.message}</div>
    );

  return sceneId ? (
    <LexicalComposer initialConfig={initialConfig} key={sceneId}>
      <div className="editor-shell">
        <ToolbarPlugin
          onAutocomplete={handleAutocompleteTrigger}
          isStreaming={isStreaming}
        />
        {streamError && (
          <div className="editor-stream-error">AI Error: {streamError}</div>
        )}
        <div className="editor-content-wrapper">
          <RichTextPlugin
            contentEditable={
              <ContentEditable className="editor-content-editable" /> // Apply page styles here
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          {/* Placeholder is rendered by RichTextPlugin */}
        </div>
        <HistoryPlugin />
        <AutoFocusPlugin />
        <AutosavePlugin sceneId={sceneId} initialJsonState={initialJsonState} />
        <AutocompletePlugin
          sceneId={sceneId}
          setIsStreaming={setIsStreaming}
          setStreamError={setStreamError}
          triggerRef={triggerAutocompleteRef}
        />
      </div>
    </LexicalComposer>
  ) : null
};

export default Editor;
