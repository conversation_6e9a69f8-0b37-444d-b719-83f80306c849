/* Basic styles for the editor shell and content area */
.editor-shell {
  position: relative; /* Needed for absolute positioning */
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--app-editor-background); /* Shell background matches editor */
  overflow: hidden; /* Prevent shell from scrolling */
  box-sizing: border-box;
}

/* New wrapper for the scrollable content area below the toolbar */
.editor-content-wrapper {
  flex-grow: 1; /* Take remaining vertical space */
  overflow-y: auto; /* THIS is the scrollable container */
  position: relative; /* For placeholder positioning */
  padding: 20px 0; /* Add vertical padding around the page */
  background-color: var(--app-panel-background, var(--app-sideBar-background)); /* Background around the page */
  box-sizing: border-box;
}

.editor-content-editable {
  outline: none;
  /* Page styles */
  padding: 40px 50px; /* Padding inside the page */
  background-color: var(--app-editor-background); /* Page background */
  color: var(--app-foreground); /* Text color */
  max-width: 80ch; /* Limit width */
  margin: 0 auto; /* Center horizontally */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow */
  box-sizing: border-box;
  min-height: 80vh; /* Ensure it has a decent minimum height */
  caret-color: var(--app-foreground);
  position: relative; /* Needed for placeholder positioning inside */
  overflow-y: visible; /* Content itself shouldn't scroll */
}

/* Placeholder text style - position inside content-editable */
.editor-placeholder {
  position: absolute;
  top: 40px; /* Match content-editable padding */
  left: 50px; /* Match content-editable padding */
  right: 50px; /* Match content-editable padding */
  color: var(--app-disabledForeground, grey); /* Use a disabled/placeholder color */
  opacity: 1;
  pointer-events: none;
  user-select: none;
  font-style: italic;
  max-width: calc(80ch - 100px); /* max-width minus horizontal padding */
}


/* Loading/Error states */
.editor-loading,
.editor-error {
  padding: 20px;
  text-align: center;
  color: var(--app-foreground); /* Use theme variable */
  opacity: 0.7;
}
.editor-error {
  color: var(--app-errorForeground); /* Use theme variable */
  opacity: 1;
}

/* Autosave status */
.autosave-status {
  position: absolute;
  bottom: 5px; /* Adjust position */
  right: 10px;
  font-size: 0.8em;
  color: var(--app-foreground); /* Use theme variable */
  opacity: 0.6;
}

/* --- Toolbar Styles --- */
.editor-toolbar {
  /* Toolbar is a direct child of editor-shell, not sticky/absolute */
  flex-shrink: 0; /* Prevent shrinking */
  background-color: var(--app-editorGroupHeader-tabsBackground);
  padding: 4px;
  border-bottom: 1px solid var(--app-border);
  z-index: 10; /* Keep it above content just in case */
}


/* --- Lexical Theme Styles (Examples) --- */
.editor-paragraph {
  margin-bottom: 8px;
}

.editor-heading-h1 {
  font-size: 1.8em;
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 10px;
  border-bottom: 1px solid var(--app-border); /* Use theme variable */
  padding-bottom: 4px;
}

.editor-heading-h2 {
  font-size: 1.4em;
  font-weight: bold;
  margin-top: 15px;
  margin-bottom: 8px;
}

.editor-list-ul {
  list-style-position: inside;
  padding-left: 20px;
  margin-bottom: 8px;
}

.editor-list-ol {
  list-style-position: inside;
  padding-left: 20px;
  margin-bottom: 8px;
}

.editor-list-item {
  margin-bottom: 4px;
}

.editor-quote {
  border-left: 4px solid var(--app-border); /* Use theme variable */
  padding-left: 10px;
  margin-left: 5px;
  margin-bottom: 8px;
  color: var(--app-foreground); /* Use theme variable */
  opacity: 0.8;
}

/* Style for the temporary AI suggestion block */
.editor-ai-suggestion {
  display: inline-block; /* Or block depending on desired layout */
  border: 1px dashed var(--app-border); /* Dashed border to indicate suggestion */
  padding: 2px 4px;
  margin: 0 2px;
  border-radius: 3px;
  background-color: var(--app-inputOption-activeBackground, rgba(100, 100, 100, 0.2)); /* Subtle background */
  opacity: 0.8;
}

.editor-code {
  background-color: var(--app-sideBar-background); /* Use a slightly different background */
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 0.95em;
}

/* Add more theme styles as needed */
