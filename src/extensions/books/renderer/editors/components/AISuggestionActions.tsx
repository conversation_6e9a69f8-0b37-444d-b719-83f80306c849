import React, { useCallback } from "react";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $getNodeByKey, NodeKey } from "lexical";
import {
  $isAISuggestionNode,
  AISuggestionNode,
} from "../nodes/AISuggestionNode";
import { $createAICompletionNode } from "../nodes/AICompletionNode";
import { Check, X, RefreshCw } from "lucide-react"; // Import RefreshCw icon
import {
  createCommand,
  LexicalCommand,
  // COMMAND_PRIORITY_EDITOR, // Not used currently
} from "lexical"; // Import command utilities and priority

// Define the command type to accept NodeKey payload
export const RETRY_AI_SUGGESTION_COMMAND: LexicalCommand<NodeKey> =
  createCommand("RETRY_AI_SUGGESTION_COMMAND");

interface AISuggestionActionsProps {
  nodeKey: NodeKey;
}

// Basic button styling (can be moved to CSS)
const buttonStyle: React.CSSProperties = {
  marginLeft: "8px",
  padding: "2px 6px",
  cursor: "pointer",
  border: "1px solid var(--app-border)",
  borderRadius: "3px",
  backgroundColor: "var(--app-button-background)",
  color: "var(--app-button-foreground)",
  fontSize: "0.9em",
  display: "inline-flex", // To align icon and text
  alignItems: "center", // To align icon and text
};

const acceptButtonStyle: React.CSSProperties = {
  ...buttonStyle,
  // Add specific styles if needed
};

const rejectButtonStyle: React.CSSProperties = {
  ...buttonStyle,
  // Add specific styles if needed
};

export function AISuggestionActions({
  nodeKey,
}: AISuggestionActionsProps): React.JSX.Element {
  const [editor] = useLexicalComposerContext();

  const handleAccept = useCallback(() => {
    editor.update(() => {
      const suggestionNode = $getNodeByKey<AISuggestionNode>(nodeKey);
      if ($isAISuggestionNode(suggestionNode)) {
        const textContent = suggestionNode.getFullText();
        if (textContent) {
          // Only insert if there's content
          const completionNode = $createAICompletionNode(textContent);
          suggestionNode.replace(completionNode);
          // Optionally move selection after the new node
          completionNode.selectEnd();
        } else {
          suggestionNode.remove(); // Remove if empty
        }
      }
    });
  }, [editor, nodeKey]);

  const handleReject = useCallback(() => {
    editor.update(() => {
      const suggestionNode = $getNodeByKey(nodeKey);
      if ($isAISuggestionNode(suggestionNode)) {
        suggestionNode.remove();
      }
    });
  }, [editor, nodeKey]);

  const handleRetry = useCallback(() => {
    editor.update(() => {
      const suggestionNode = $getNodeByKey<AISuggestionNode>(nodeKey);
      if ($isAISuggestionNode(suggestionNode)) {
        // Clear content and reset state instead of removing
        suggestionNode.setTextContent("");
        suggestionNode.setComplete(false);
        // Dispatch command with the node key to retry on
        editor.dispatchCommand(RETRY_AI_SUGGESTION_COMMAND, nodeKey);
      }
    });
  }, [editor, nodeKey]);

  return (
    <span
      style={{
        marginLeft: "10px", // Spacing after the suggestion text
        display: "inline-flex",
        alignItems: "center",
        verticalAlign: "middle", // Align with text
      }}
      // Prevent clicks inside buttons from selecting the node
      onClick={(e) => e.stopPropagation()}
    >
      <button
        onClick={handleAccept}
        style={acceptButtonStyle}
        title="Accept Suggestion"
      >
        <Check size={14} style={{ marginRight: "4px" }} aria-hidden="true" />{" "}
        Accept
      </button>
      <button
        onClick={handleReject}
        style={rejectButtonStyle}
        title="Reject Suggestion"
      >
        <X size={14} style={{ marginRight: "4px" }} aria-hidden="true" /> Reject
      </button>
      <button
        onClick={handleRetry}
        style={rejectButtonStyle} // Use reject style or create a new one
        title="Retry Generation"
      >
        <RefreshCw
          size={14}
          style={{ marginRight: "4px" }}
          aria-hidden="true"
        />{" "}
        Retry
      </button>
    </span>
  );
}
