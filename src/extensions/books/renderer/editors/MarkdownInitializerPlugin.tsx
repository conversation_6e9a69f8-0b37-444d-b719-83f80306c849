import { useEffect } from "react";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $convertFromMarkdownString, TRANSFORMERS } from "@lexical/markdown";
import { $getRoot, $createParagraphNode, $createTextNode } from "lexical";

interface MarkdownInitializerPluginProps {
  initialMarkdown: string | null;
}

export function MarkdownInitializerPlugin({
  initialMarkdown,
}: MarkdownInitializerPluginProps): null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (initialMarkdown !== null) {
      editor.update(() => {
        try {
          $convertFromMarkdownString(initialMarkdown, TRANSFORMERS);
          console.log("[MarkdownInitializerPlugin] Initial markdown applied.");
        } catch (e) {
          console.error(
            "[MarkdownInitializerPlugin] Failed to parse initial markdown:",
            e
          );
          const root = $getRoot();
          root.clear();
          root.append(
            $createParagraphNode().append(
              $createTextNode("Error loading content.")
            )
          );
        }
      });
    } else {
      editor.update(() => {
        const root = $getRoot();
        if (root.getChildrenSize() > 0) {
          root.clear();
          root.append($createParagraphNode());
        }
      });
      console.log(
        "[MarkdownInitializerPlugin] No initial markdown to apply or scene closed."
      );
    }
  }, [editor, initialMarkdown]);

  return null;
}

// Optional: Add default export if needed by lazy loading mechanism
// export default MarkdownInitializerPlugin;
