import React from 'react'; // Import React for decorate
import {
  LexicalNode,
  NodeK<PERSON>,
  Spread,
  DecoratorNode, // Extend DecoratorNode
  SerializedLexicalNode, // Use for exportJSON return type if not null
} from 'lexical';
import { AISuggestionActions } from '../components/AISuggestionActions';
// import { $createAICompletionNode } from './AICompletionNode'; // Not used currently

// Serialized format might not be needed if we don't persist it,
// but define it for completeness if importJSON needs it.
export type SerializedAISuggestionNode = Spread<
  {
    type: 'ai-suggestion';
    version: 1;
    textContent: string;
    isComplete: boolean;
  },
  SerializedLexicalNode // Base type for serialization
>;

/**
 * Transient DecoratorNode to display streaming AI suggestions and actions.
 */
export class AISuggestionNode extends DecoratorNode<React.ReactNode> {
  __textContent: string;
  __isComplete: boolean;

  static getType(): string {
    return 'ai-suggestion';
  }

  static clone(node: AISuggestionNode): AISuggestionNode {
    return new AISuggestionNode(node.__textContent, node.__isComplete, node.__key);
  }

  // Make this node non-persistent
  static importJSON(serializedNode: SerializedAISuggestionNode): AISuggestionNode {
    // Normally, we wouldn't load these as they are transient.
    // If loaded, create a default or based on serialized data.
    // Returning a default empty one might be safest if encountered unexpectedly.
    console.warn("Attempted to import a transient AISuggestionNode.");
    return $createAISuggestionNode(serializedNode.textContent, serializedNode.isComplete);
  }

  constructor(textContent = '', isComplete = false, key?: NodeKey) {
    super(key);
    this.__textContent = textContent;
    this.__isComplete = isComplete;
  }

  // --- DOM ---
  createDOM(): HTMLElement {
    const span = document.createElement('span');
    span.classList.add('editor-ai-suggestion');
    span.style.whiteSpace = 'pre-wrap'; // Allow wrapping
    if (this.__isComplete) {
        span.classList.add('editor-ai-suggestion-complete');
    }
    return span;
  }

  // DecoratorNode relies on decorate method for rendering content,
  // so updateDOM often returns false unless attributes affecting the wrapper change.
  updateDOM(
    prevNode: AISuggestionNode,
    dom: HTMLElement,

  ): boolean {
    // Only update the 'complete' class if the state changed.
    const wasComplete = prevNode.__isComplete;
    const isNowComplete = this.__isComplete;
    let needsReconciliation = false;
    if (wasComplete !== isNowComplete) {
      dom.classList.toggle('editor-ai-suggestion-complete', isNowComplete);
      // Reconciliation needed because the decorated component will change
      needsReconciliation = true;
    }
    // Text content changes are handled by React via decorate, so return false for text.
    // If other wrapper attributes changed, return true.
    return needsReconciliation;
  }

  // --- Serialization ---
  // Mark as non-persistent by returning null or filtering during save.
  // Returning null might cause issues if not handled properly upstream.
  // Let's return a minimal valid structure but plan to filter it out.
  exportJSON(): SerializedAISuggestionNode {
    // This node should ideally be filtered out before serialization.
    // Returning its state might be useful for debugging but not for persistence.
    return {
      type: 'ai-suggestion',
      version: 1,
      textContent: this.__textContent,
      isComplete: this.__isComplete,
      // DecoratorNode doesn't have children in the same way, format, indent etc.
    };
  }

  // --- Node Logic ---
  setTextContent(text: string): void {
      const writable = this.getWritable();
      writable.__textContent = text;
  }

  appendText(text: string): void {
    const writable = this.getWritable();
    writable.setTextContent(writable.__textContent + text); // Use own method
  }

  getFullText(): string {
    return this.getLatest().__textContent;
  }

  setComplete(isComplete: boolean): void {
    const writable = this.getWritable();
    if (writable.__isComplete !== isComplete) {
        writable.__isComplete = isComplete;
    }
  }

  isComplete(): boolean {
    return this.getLatest().__isComplete;
  }

  // --- Decoration ---
  decorate(): React.ReactNode {
    // Render the text content and conditionally the action buttons
    return React.createElement(
      React.Fragment,
      null,
      this.__textContent,
      this.__isComplete ? React.createElement(AISuggestionActions, { nodeKey: this.getKey() }) : null
    );
  }

  // --- Behavior ---
  getTextContent(): string {
      // Required method for DecoratorNode if it represents text conceptually
      return this.getFullText();
  }

  isInline(): boolean {
    // Treat suggestion as inline content
    return true;
  }

  isKeyboardSelectable(): boolean {
    return true;
  }

  // Decorator nodes are typically leafs in terms of selection/insertion behavior
  isLeaf(): boolean {
      return false; // It can contain text conceptually, but rendered via decorate
  }
}

export function $createAISuggestionNode(textContent = '', isComplete = false): AISuggestionNode {
  return new AISuggestionNode(textContent, isComplete);
}

export function $isAISuggestionNode(
  node: LexicalNode | null | undefined,
): node is AISuggestionNode {
  return node instanceof AISuggestionNode;
}
