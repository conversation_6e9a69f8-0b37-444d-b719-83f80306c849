import {
  EditorConfig,
  LexicalEditor,
  LexicalNode, // Added import
  SerializedTextNode,
  Spread,
  TextNode,
} from 'lexical';

export type SerializedAICompletionNode = Spread<
  {
    type: 'ai-completion';
    // Add any custom properties you want to serialize, e.g., requestId
    // requestId?: string;
  },
  SerializedTextNode
>;

/**
 * Custom node to represent text generated by AI.
 */
export class AICompletionNode extends TextNode {
  // Add custom properties if needed
  // __requestId?: string;

  static getType(): string {
    return 'ai-completion';
  }

  static clone(node: AICompletionNode): AICompletionNode {
    return new AICompletionNode(node.__text, node.__key);
    // If you add custom properties, clone them too:
    // const clone = new AICompletionNode(node.__text, node.__key);
    // clone.__requestId = node.__requestId;
    // return clone;
  }

  createDOM(config: EditorConfig, editor: LexicalEditor): HTMLElement {
    // Call super to get the base span element
    const dom = super.createDOM(config, editor);
    // Add your custom class
    dom.classList.add('editor-ai-completion');
    return dom;
  }

  // updateDOM is handled by TextNode if only text content changes.
  // Override if you need to update based on custom properties.
  // updateDOM(
  //   prevNode: AICompletionNode,
  //   dom: HTMLElement,
  //   config: EditorConfig,
  // ): boolean {
  //   const isUpdated = super.updateDOM(prevNode, dom, config);
  //   // Update based on custom properties if needed
  //   // if (prevNode.__requestId !== this.__requestId) { ... }
  //   return isUpdated;
  // }

  static importJSON(serializedNode: SerializedAICompletionNode): AICompletionNode {
    const node = $createAICompletionNode(serializedNode.text);
    // Import custom properties if added
    // node.__requestId = serializedNode.requestId;
    // Import standard TextNode properties
    node.setFormat(serializedNode.format);
    node.setDetail(serializedNode.detail);
    node.setMode(serializedNode.mode);
    node.setStyle(serializedNode.style);
    return node;
  }

  exportJSON(): SerializedAICompletionNode {
    const baseSerialized = super.exportJSON();
    return {
      ...baseSerialized,
      type: 'ai-completion',
      version: 1,
      // Export custom properties if added
      // requestId: this.__requestId,
    };
  }

  // Optional: Prevent merging with regular TextNodes if desired
  // canMergeWith(node: TextNode): boolean {
  //   return node instanceof AICompletionNode;
  // }
}

/**
 * Creates a new AICompletionNode.
 */
export function $createAICompletionNode(text: string): AICompletionNode {
  return new AICompletionNode(text);
}

/**
 * Checks if a node is an AICompletionNode.
 */
export function $isAICompletionNode(
  node: LexicalNode | null | undefined,
): node is AICompletionNode {
  return node instanceof AICompletionNode;
}
