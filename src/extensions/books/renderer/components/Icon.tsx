import React from 'react';
import { icons, LucideProps } from 'lucide-react';

// Define the props for the Icon component
interface IconProps extends Omit<LucideProps, 'ref'> { // Omit ref as it's handled internally by lucide-react
  name: keyof typeof icons; // Ensure name is a valid Lucide icon name
  // Add other common props like size, color, strokeWidth if needed,
  // or allow them to be passed directly via ...props
}

/**
 * A dynamic icon component specific to the 'books' extension
 * that renders Lucide icons based on the name prop.
 */
export const Icon: React.FC<IconProps> = ({ name, size = 16, ...props }) => { // Default size
  const LucideIcon = icons[name];

  if (!LucideIcon) {
    console.warn(`Icon "${name}" not found in lucide-react`);
    // Return a default fallback icon or null
    return null; // Or return a default icon like icons['HelpCircle']
  }

  // Render the dynamically selected icon component
  return <LucideIcon size={size} {...props} />;
};

// No default export needed if only used internally within the extension
