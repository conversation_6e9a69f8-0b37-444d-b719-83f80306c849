import React from "react";
import { Icon } from "./Icon";

interface BooksExplorerHeaderProps {
  selectedBookId: string | null;
  onAddChapterClick: () => void;
  onNewBookClick: () => void;
  // onCollapseAllClick: () => void; // Future functionality
}

export const BooksExplorerHeader: React.FC<BooksExplorerHeaderProps> = ({
  selectedBookId,
  onAddChapterClick,
  onNewBookClick,
  // onCollapseAllClick
}) => {
  return (
    <div className="books-explorer-header">
      <span className="view-title">BOOKS EXPLORER</span>
      <div className="view-actions">
        {selectedBookId && (
          <button
            type="button"
            className="view-action-button"
            onClick={onAddChapterClick}
            title="Add Chapter"
          >
            <Icon name="FolderPlus" size={16} />
          </button>
        )}
        <button
          type="button"
          className="view-action-button"
          onClick={onNewBookClick}
          title="New Book"
        >
          <Icon name="Plus" size={16} />
        </button>
        {/* TODO: Add Collapse All button */}
        {/* <button className="view-action-button" onClick={onCollapseAllClick} title="Collapse All">
                    <Icon name="ChevronsDownUp" size={16} />
                </button> */}
      </div>
    </div>
  );
};
