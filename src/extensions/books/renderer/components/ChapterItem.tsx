import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"; // Import for scene context

// Corrected import path
import type { ChapterInfo, SceneInfo } from "../../shared/types";
import { SortableSceneItem } from "./SortableSceneItem";
import { Icon } from "./Icon"; // Import the Icon component

interface ChapterItemProps {
  chapter: ChapterInfo;
  id: string; // ID for dnd-kit (e.g., "chapter-xyz")
  activeSceneId: string | null; // ID of the active scene (already exists)
  onSceneClick: (scene: SceneInfo) => void;
  onAddSceneClick: (chapterId: string) => void;
  onContextMenu: (
    event: React.MouseEvent,
    itemType: "chapter" | "scene",
    itemId: string
  ) => void;
}

export const ChapterItem: React.FC<ChapterItemProps> = ({
  chapter,
  id,
  activeSceneId, // Receive activeSceneId
  onSceneClick,
  onAddSceneClick,
  onContextMenu,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging, // Optional: use for styling while dragging
    isOver, // Get isOver state for chapter highlighting
  } = useSortable({ id: id }); // Use the passed ID

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1, // Example: make item semi-transparent while dragging
    // Add other styles as needed, e.g., zIndex if necessary
    zIndex: isDragging ? 100 : "auto",
    marginBottom: "4px", // Add some space between chapters
  };

  // Prepare scene IDs for the nested SortableContext
  const sceneIds = chapter.scenes.map((scene) => `scene-${scene.id}`);

  return (
    <details
      ref={setNodeRef} // Connect the sortable node
      style={style}
      key={chapter.id} // Keep using real ID for React key if needed elsewhere, but dnd uses `id` prop
      open // Keep details open by default for now
      data-id={chapter.id} // Keep original data-id if used elsewhere
      {...attributes} // Spread DnD attributes (like aria roles)
      // Listeners are applied back to the summary
    >
      <summary
        onContextMenu={(e) => onContextMenu(e, "chapter", chapter.id)}
        {...listeners} // Apply listeners back to summary
        style={{
          // Removed cursor: grab style
          backgroundColor: isOver
            ? "var(--app-list-hoverBackground)"
            : "transparent", // Use theme variable for hover/over
          padding: "3px 0", // Keep padding
          borderRadius: "3px", // Keep border radius
          display: "flex", // Use flexbox for layout
          // Removed justify-content: space-between
          alignItems: "center", // Align items vertically
        }}
      >
        {/* Chapter Title (allow growing and shrinking) */}
        <span
          style={{
            flexGrow: 1,
            flexShrink: 1,
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            paddingLeft: "4px" /* Add small space after marker */,
          }}
        >
          {chapter.title || "Untitled Chapter"}
        </span>

        {/* Add Scene Icon Button (appears on hover - handled by CSS) */}
        <button
          type="button"
          className="add-scene-button" // Use class for styling
          onClick={(e) => {
            e.stopPropagation(); // Prevent details toggle
            e.preventDefault(); // Prevent default summary action
            onAddSceneClick(chapter.id);
          }}
          title="Add Scene"
        >
          <Icon name="Plus" size={14} /> {/* Use the Icon component */}
        </button>
      </summary>
      {/* Wrap scenes in their own SortableContext */}
      <SortableContext items={sceneIds} strategy={verticalListSortingStrategy}>
        <ul data-chapter-id={chapter.id}>
          {chapter.scenes.map((scene) => (
            // Use the new SortableSceneItem component
            <SortableSceneItem
              key={scene.id} // React key
              id={`scene-${scene.id}`} // dnd-kit ID
              scene={scene}
              chapterId={chapter.id}
              onSceneClick={onSceneClick}
              onContextMenu={onContextMenu}
              isActive={scene.id === activeSceneId} // Pass isActive prop
            />
          ))}
          {/* Updated style for "(No scenes)" text */}
          {chapter.scenes.length === 0 && (
            <li
              style={{
                fontStyle: "italic",
                color: "var(--app-foreground)",
                opacity: 0.7,
                paddingLeft: "10px",
              }}
            >
              (No scenes)
            </li>
          )}
          {/* Removed the old Add Scene button li */}
        </ul>
      </SortableContext>
    </details>
  );
};
