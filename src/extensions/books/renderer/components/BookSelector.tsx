import React from 'react';
import type { Book } from '../../shared/types';

interface BookSelectorProps {
    books: Book[];
    selectedBookId: string | null;
    isLoading: boolean;
    onChange: (bookId: string | null) => void;
    onContextMenu: (event: React.MouseEvent, bookId: string) => void;
}

export const BookSelector: React.FC<BookSelectorProps> = ({
    books,
    selectedBookId,
    isLoading,
    onChange,
    onContextMenu
}) => {
    return (
        <select
            value={selectedBookId ?? ''}
            onChange={(e) => onChange(e.target.value || null)}
            disabled={isLoading || books.length === 0}
            style={{ width: '100%', marginBottom: '10px', padding: '5px' }}
            onContextMenu={(e) => {
                if (selectedBookId) {
                    onContextMenu(e, selectedBookId);
                }
            }}
        >
            <option value="">-- Select a Book --</option>
            {books.map(book => (
                <option key={book.id} value={book.id}>
                    {book.title}
                </option>
            ))}
        </select>
    );
};
