import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { SceneInfo } from '../../shared/types';
import cn from 'classnames'; // Import classnames

interface SortableSceneItemProps {
    scene: SceneInfo;
    id: string; // ID for dnd-kit (e.g., "scene-abc")
    isActive: boolean; // Add isActive prop
    chapterId: string;
    onSceneClick: (scene: SceneInfo) => void;
    onContextMenu: (event: React.MouseEvent, itemType: 'scene', itemId: string) => void;
}

export const SortableSceneItem: React.FC<SortableSceneItemProps> = ({
    scene,
    id,
    isActive, // Receive isActive prop
    chapterId,
    onSceneClick,
    onContextMenu
}) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
        isOver, // Get the isOver state
    } = useSortable({ id: id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
        zIndex: isDragging ? 100 : 'auto',
        // Add padding or other base styles for li if needed
        padding: '3px 5px', // Slightly increase padding
        borderRadius: '3px',
        marginBottom: '3px', // Add space between scenes
        // Removed cursor: grab
        backgroundColor: isOver ? 'var(--background-secondary-alt)' : 'transparent', // Highlight when dragged over
    };

    return (
        <li
            ref={setNodeRef}
            style={style}
            key={scene.id} // React key
            data-id={scene.id}
            data-chapter-id={chapterId}
            onClick={() => onSceneClick(scene)}
            onContextMenu={(e) => onContextMenu(e, 'scene', scene.id)}
            className={cn('scene-item', { 'active-scene': isActive })} // Add conditional class
            {...attributes}
            {...listeners}
        >
            {scene.title || 'Untitled Scene'}
        </li>
    );
};
