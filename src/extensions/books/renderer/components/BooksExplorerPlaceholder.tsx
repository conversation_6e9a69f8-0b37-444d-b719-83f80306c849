import React from "react";

interface BooksExplorerPlaceholderProps {
  booksCount: number;
  selectedBookId: string | null;
  isLoadingStructure: boolean;
  structureError: Error | null;
  chaptersCount: number;
  onNewBookClick: () => void;
}

export const BooksExplorerPlaceholder: React.FC<
  BooksExplorerPlaceholderProps
> = ({
  booksCount,
  selectedBookId,
  isLoadingStructure,
  structureError,
  chaptersCount,
  onNewBookClick,
}) => {
  // Case 1: No book selected
  if (!selectedBookId) {
    return (
      <div className="books-explorer-placeholder">
        {booksCount === 0 ? (
          <>
            <p>No books yet.</p>
            <button
              type="button"
              onClick={onNewBookClick}
              className="placeholder-button"
            >
              Create New Book
            </button>
          </>
        ) : (
          <>
            <p>Select a book or create a new one.</p>
            <button
              type="button"
              onClick={onNewBookClick}
              className="placeholder-button"
            >
              Create New Book
            </button>
          </>
        )}
      </div>
    );
  }

  // Case 2: Book selected, but no chapters (and not loading/error)
  if (
    selectedBookId &&
    !isLoadingStructure &&
    !structureError &&
    chaptersCount === 0
  ) {
    return (
      <p className="books-explorer-placeholder">
        No chapters yet. Add one using the '+' icon above or the context menu.
      </p>
    );
  }

  // Case 3: Loading or error state (already handled in the main component, return null here)
  // Or if chapters exist, placeholder shouldn't render.
  return null;
};
