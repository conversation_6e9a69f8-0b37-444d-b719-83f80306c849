import {create} from 'zustand';
import {subscribeWithSelector} from 'zustand/middleware';
// Удаляем дубликаты импортов
import type {Book, BookStructure, ChapterInfo, SceneInfo} from '../../shared/types';
import {ipcRendererService} from '@renderer/core/services/ipcRendererService';
import * as ipcChannels from '../../shared/constants/ipc-channels'; // Import extension channels
import {IpcChannels} from '@shared/constants/ipc-channels'; // Import core channels

// Состояние для управления книгами и их структурой
interface BooksState {
  books: Book[];
  isLoadingBooks: boolean;
  booksError: null;
  selectedBookId: string | null;
  bookStructure: BookStructure | null;
  isLoadingStructure: boolean;
  structureError: null;
  lastOptimisticUpdateTimestamp: number | null; // Add timestamp
  
  // Действия
  loadBooks: () => Promise<void>;
  setSelectedBookId: (bookId: string | null) => void; // Теперь также загружает структуру
  loadStructure: (bookId: string, isBackgroundUpdate?: boolean) => Promise<void>; // Add optional flag
  optimisticallyReorderChapters: (orderedChapterIds: string[]) => void;
  optimisticallyReorderScenes: (chapterId: string, orderedSceneIds: string[]) => void;
  optimisticallyMoveScene: (sceneId: string, sourceChapterId: string, targetChapterId: string, newPosition: number) => void;
}

export const useBooksStore = create(
  subscribeWithSelector<BooksState>((set, get) => ({
    // Начальные значения
    books: [],
    isLoadingBooks: true,
    booksError: null,
    selectedBookId: null,
    bookStructure: null,
    isLoadingStructure: false,
    structureError: null,
    lastOptimisticUpdateTimestamp: null, // Initialize timestamp
    
    // --- Действия ---
    loadBooks: async () => {
      set({isLoadingBooks: true, booksError: null});
      try {
        const result: Book[] = await ipcRendererService.invoke(ipcChannels.GET_BOOKS); // Используем константу
        
        set({books: result, booksError: null});
        // Логика выбора первой книги при отсутствии выбранной
        const currentSelectedId = get().selectedBookId;
        const isValidSelection = result.some((b: Book) => b.id === currentSelectedId);
        if (result.length > 0 && (!currentSelectedId || !isValidSelection)) {
          const sortedBooks = [...result].sort((a: Book, b: Book) => a.createdAt - b.createdAt);
          // Не вызываем setSelectedBookId рекурсивно, просто устанавливаем ID
          set({selectedBookId: sortedBooks[0].id});
          get().loadStructure(sortedBooks[0].id); // Загружаем структуру для новой выбранной книги
        } else if (result.length === 0) {
          set({selectedBookId: null, bookStructure: null, structureError: null}); // Сбрасываем все, если книг нет
        }
      } catch (error: unknown) {
        console.error('Error loading books:', error);
      } finally {
        set({isLoadingBooks: false});
      }
    },
    
    setSelectedBookId: (bookId) => {
      // Проверяем, изменился ли ID
      if (get().selectedBookId !== bookId) {
        set({selectedBookId: bookId, bookStructure: null, structureError: null});
        if (bookId) {
          get().loadStructure(bookId); // Загружаем структуру для новой книги
        } else {
          set({isLoadingStructure: false}); // Убедимся, что загрузка остановлена
        }
      }
    },
    
    loadStructure: async (bookId: string, isBackgroundUpdate = false) => { // Accept and default the flag
      // Загружаем структуру для ПЕРЕДАННОГО bookId
      if (!bookId) {
        set({bookStructure: null, structureError: null, isLoadingStructure: false});
        return;
      }
      // Set isLoading only if it's a user-initiated load (not background) and it's the selected book
      if (!isBackgroundUpdate && get().selectedBookId === bookId) {
        set({isLoadingStructure: true, structureError: null});
      } else if (!isBackgroundUpdate) {
        // Clear error on user-initiated load for non-selected book, but don't show loading
        set({structureError: null});
      }
      // If it IS a background update, we don't touch isLoadingStructure here
      
      try {
        const result: BookStructure = await ipcRendererService.invoke(ipcChannels.GET_BOOK_STRUCTURE, {bookId});
        const currentState = get().bookStructure;
        const newState = result ?? null;
        if (JSON.stringify(currentState) !== JSON.stringify(newState)) {
          console.debug("[loadStructure] Structure changed, updating state.");
          set({bookStructure: newState, structureError: null});
        } else {
          console.debug("[loadStructure] Structure is identical, skipping state update.");
          if (get().structureError && !isBackgroundUpdate) {
            set({structureError: null});
          }
        }
      } catch (error: unknown) {
        console.error('Error loading structure:', error);
      } finally {
        // Снимаем isLoading только если ID совпадает с текущим выбранным
        if (get().selectedBookId === bookId) {
          set({isLoadingStructure: false});
        }
      }
    },
    
    // --- Optimistic Update Actions ---
    optimisticallyReorderChapters: (orderedChapterIds) => {
      set((state) => {
        if (!state.bookStructure) return {}; // No structure to update
        const currentChapters = state.bookStructure.chapters;
        const chapterMap = new Map(currentChapters.map(ch => [ch.id, ch]));
        const newChapters = orderedChapterIds
          .map(id => chapterMap.get(id))
          .filter((ch): ch is ChapterInfo => ch !== undefined); // Filter out potential undefined if IDs mismatch
        
        if (newChapters.length !== currentChapters.length) {
          console.warn("Optimistic chapter reorder skipped: ID mismatch or missing chapter.");
          return {}; // Avoid partial updates
        }
        
        return {
          bookStructure: {
            ...state.bookStructure,
            chapters: newChapters,
          },
          lastOptimisticUpdateTimestamp: Date.now(), // Set timestamp
        };
      });
    },
    
    optimisticallyReorderScenes: (chapterId, orderedSceneIds) => {
      set((state) => {
        if (!state.bookStructure) return {};
        const chapterIndex = state.bookStructure.chapters.findIndex(ch => ch.id === chapterId);
        if (chapterIndex === -1) {
          console.warn(`Optimistic scene reorder skipped: Chapter ${chapterId} not found.`);
          return {};
        }
        
        const chapter = state.bookStructure.chapters[chapterIndex];
        const sceneMap = new Map(chapter.scenes.map(sc => [sc.id, sc]));
        const newScenes = orderedSceneIds
          .map(id => sceneMap.get(id))
          .filter((sc): sc is SceneInfo => sc !== undefined);
        
        if (newScenes.length !== chapter.scenes.length) {
          console.warn(`Optimistic scene reorder skipped for chapter ${chapterId}: ID mismatch or missing scene.`);
          return {};
        }
        
        const newChapters = [...state.bookStructure.chapters];
        newChapters[chapterIndex] = {...chapter, scenes: newScenes};
        
        return {
          bookStructure: {
            ...state.bookStructure,
            chapters: newChapters,
          },
          lastOptimisticUpdateTimestamp: Date.now(), // Set timestamp
        };
      });
    },
    
    optimisticallyMoveScene: (sceneId, sourceChapterId, targetChapterId, newPosition) => {
      set((state) => {
        if (!state.bookStructure) return {};
        
        const sourceChapterIndex = state.bookStructure.chapters.findIndex(ch => ch.id === sourceChapterId);
        const targetChapterIndex = state.bookStructure.chapters.findIndex(ch => ch.id === targetChapterId);
        
        if (sourceChapterIndex === -1 || targetChapterIndex === -1) {
          console.warn("Optimistic scene move skipped: Source or target chapter not found.");
          return {};
        }
        
        const sourceChapter = state.bookStructure.chapters[sourceChapterIndex];
        // Not using targetChapter directly as we need to get the potentially updated version later
        
        const sceneIndex = sourceChapter.scenes.findIndex(sc => sc.id === sceneId);
        if (sceneIndex === -1) {
          console.warn(`Optimistic scene move skipped: Scene ${sceneId} not found in source chapter ${sourceChapterId}.`);
          return {};
        }
        
        const sceneToMove = sourceChapter.scenes[sceneIndex];
        
        const newChapters = [...state.bookStructure.chapters];
        
        // Remove from source
        const newSourceScenes = [...sourceChapter.scenes];
        newSourceScenes.splice(sceneIndex, 1);
        newChapters[sourceChapterIndex] = {...sourceChapter, scenes: newSourceScenes};
        
        // Add to target
        // Need to get the potentially updated target chapter from newChapters if source and target are the same
        const currentTargetChapter = newChapters[targetChapterIndex];
        const newTargetScenes = [...currentTargetChapter.scenes];
        // Clamp newPosition to valid range
        const clampedPosition = Math.max(0, Math.min(newTargetScenes.length, newPosition));
        newTargetScenes.splice(clampedPosition, 0, sceneToMove);
        newChapters[targetChapterIndex] = {...currentTargetChapter, scenes: newTargetScenes};
        
        
        return {
          bookStructure: {
            ...state.bookStructure,
            chapters: newChapters,
          },
          lastOptimisticUpdateTimestamp: Date.now(), // Set timestamp
        };
      });
    },
    
  }))
);

// --- Подписки на IPC события ---
const handleBooksUpdated = () => {
  console.log('[BooksStore] Received books-updated event, reloading books...');
  useBooksStore.getState().loadBooks();
};

interface StructureUpdatedPayload { bookId: string }
const handleStructureUpdated = (data: StructureUpdatedPayload) => { // Принимает только data
  const state = useBooksStore.getState();
  const currentSelectedBookId = state.selectedBookId;
  const lastOptimisticUpdate = state.lastOptimisticUpdateTimestamp;
  
  // Check if the event is for the current book
  if (data?.bookId === currentSelectedBookId) {
    const now = Date.now();
    const timeSinceLastOptimisticUpdate = lastOptimisticUpdate ? now - lastOptimisticUpdate : Infinity;
    
    // Check if the event arrived shortly after an optimistic update (e.g., within 750ms) - Increased threshold
    // We still use the timestamp check, but instead of skipping, we call loadStructure with the background flag
    const isLikelyOptimisticConfirmation = timeSinceLastOptimisticUpdate < 750; // Increased threshold
    
    if (isLikelyOptimisticConfirmation) {
      console.log(`[BooksStore] Received structure-updated event for ${currentSelectedBookId} ${timeSinceLastOptimisticUpdate}ms after optimistic update, performing background reload.`);
      // Reset the timestamp immediately before the background load
      useBooksStore.setState({lastOptimisticUpdateTimestamp: null});
      state.loadStructure(currentSelectedBookId, true); // Pass true for background update
    } else {
      // Otherwise, perform a normal reload (which will show loading indicator)
      console.log(`[BooksStore] Received structure-updated event for ${currentSelectedBookId}, performing foreground reload...`);
      state.loadStructure(currentSelectedBookId, false); // Pass false or omit for foreground update
    }
  }
};

// Выполняем подписку один раз при загрузке модуля
// Removed unused unsubscribe variables as the subscriptions remain active for the app lifetime

// Используем ipcRendererService для подписки
try {
  // These subscriptions will remain active for the lifetime of the application
  ipcRendererService.on(ipcChannels.BOOKS_UPDATED_EVENT, handleBooksUpdated);
  ipcRendererService.on<StructureUpdatedPayload>(ipcChannels.STRUCTURE_UPDATED_EVENT, handleStructureUpdated); // 
  // ipcRendererService.on<SettingsChangedEventData>(IpcChannels.SETTINGS_CHANGED, handleSettingsChanged); // Указываем тип данных
  console.log(`[BooksStore] Subscribed to ${ipcChannels.BOOKS_UPDATED_EVENT}, ${ipcChannels.STRUCTURE_UPDATED_EVENT}, and ${IpcChannels.SETTINGS_CHANGED} events via ipcRendererService.`);
} catch (error) {
  console.error("[BooksStore] Failed to subscribe to IPC events via ipcRendererService:", error);
}

// TODO: Нужен механизм для отписки при выходе из приложения или если стор уничтожается
// Возможно, стоит перенести подписки в React хук или сервис, который управляет своим жизненным циклом.

// Селекторы для удобства
export const useBooks = () => useBooksStore((state) => state.books);
export const useIsLoadingBooks = () => useBooksStore((state) => state.isLoadingBooks);
export const useBooksError = () => useBooksStore((state) => state.booksError);
export const useSelectedBookId = () => useBooksStore((state) => state.selectedBookId);
export const useBookStructure = () => useBooksStore((state) => state.bookStructure);
export const useIsLoadingStructure = () => useBooksStore((state) => state.isLoadingStructure);
export const useStructureError = () => useBooksStore((state) => state.structureError);

// Export actions for use in components
export const {
  optimisticallyReorderChapters,
  optimisticallyReorderScenes,
  optimisticallyMoveScene
} = useBooksStore.getState();
