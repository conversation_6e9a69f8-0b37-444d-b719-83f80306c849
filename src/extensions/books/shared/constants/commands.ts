/**
 * Константы для идентификаторов команд.
 */
import { EXTENSION_ID } from './extension';

// Команды для книг
export const CREATE_BOOK = `${EXTENSION_ID}:commands.createBook`;
export const RENAME_BOOK = `${EXTENSION_ID}:commands.renameBook`;
export const DELETE_BOOK = `${EXTENSION_ID}:commands.deleteBook`;

// Команды для глав
export const CREATE_CHAPTER = `${EXTENSION_ID}:commands.createChapter`;
export const RENAME_CHAPTER = `${EXTENSION_ID}:commands.renameChapter`;
export const DELETE_CHAPTER = `${EXTENSION_ID}:commands.deleteChapter`;
export const REORDER_CHAPTERS = `${EXTENSION_ID}:commands.reorderChapters`;

// Команды для сцен
export const CREATE_SCENE = `${EXTENSION_ID}:commands.createScene`;
export const RENAME_SCENE = `${EXTENSION_ID}:commands.renameScene`;
export const DELETE_SCENE = `${EXTENSION_ID}:commands.deleteScene`;
export const SAVE_SCENE = `${EXTENSION_ID}:commands.saveScene`;
export const MOVE_SCENE = `${EXTENSION_ID}:commands.moveScene`;
export const REORDER_SCENES = `${EXTENSION_ID}:commands.reorderScenes`;

// Тип для идентификаторов команд
export type CommandId = 
  | typeof CREATE_BOOK
  | typeof RENAME_BOOK
  | typeof DELETE_BOOK
  | typeof CREATE_CHAPTER
  | typeof RENAME_CHAPTER
  | typeof DELETE_CHAPTER
  | typeof REORDER_CHAPTERS
  | typeof CREATE_SCENE
  | typeof RENAME_SCENE
  | typeof DELETE_SCENE
  | typeof SAVE_SCENE
  | typeof MOVE_SCENE
  | typeof REORDER_SCENES;
