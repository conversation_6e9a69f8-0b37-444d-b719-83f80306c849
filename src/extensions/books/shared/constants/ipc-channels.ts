/**
 * Константы для идентификаторов каналов IPC.
 */
import { EXTENSION_ID } from './extension';

// Каналы IPC для сцен
export const GET_SCENE_CONTENT = `${EXTENSION_ID}:channels.getSceneContent`;
export const SAVE_SCENE_CONTENT = `${EXTENSION_ID}:channels.saveSceneContent`;

// Каналы IPC для книг
export const GET_BOOKS = `${EXTENSION_ID}:channels.getBooks`;
export const GET_BOOK_STRUCTURE = `${EXTENSION_ID}:channels.getBookStructure`;

// Каналы IPC для задач ИИ
export const AUTOCOMPLETE_SCENE_STREAM_START = `${EXTENSION_ID}:channels.autocompleteSceneStreamStart`;

// События (Main -> Renderer)
export const BOOKS_UPDATED_EVENT = `${EXTENSION_ID}:channels.booksUpdated`;
export const STRUCTURE_UPDATED_EVENT = `${EXTENSION_ID}:channels.structureUpdated`;

// Тип для идентификаторов каналов IPC
export type IpcChannelId = 
  | typeof GET_SCENE_CONTENT
  | typeof SAVE_SCENE_CONTENT
  | typeof GET_BOOKS
  | typeof GET_BOOK_STRUCTURE
  | typeof AUTOCOMPLETE_SCENE_STREAM_START
  | typeof BOOKS_UPDATED_EVENT
  | typeof STRUCTURE_UPDATED_EVENT;
