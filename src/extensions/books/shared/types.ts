// Types specific to the 'ai-books.books' extension

// --- Core Entities (as stored in DB/used internally) ---

export interface Book {
  id: string;
  title: string;
  createdAt: number;
}

export interface Scene {
  id: string;
  title: string;
  jsonFilePath: string; // Path to the JSON state file
  chapterId: string;
  position: number;
  // other fields...
}

export interface Chapter {
  id: string;
  title: string;
  bookId: string;
  position: number;
  // other fields...
}

// --- Data Structures for UI/IPC ---

// Simplified scene info for the explorer view
export interface SceneInfo {
  id: string;
  title: string;
  position: number;
  jsonFilePath: string; // Include JSON file path
}

// Chapter info including its scenes for the explorer view
export interface ChapterInfo {
  id: string;
  title: string;
  position: number;
  scenes: SceneInfo[];
}

// Represents the hierarchical structure of a book for the explorer view
export interface BookStructure {
  chapters: ChapterInfo[];
}

// Add other book-specific types here if needed in the future
export interface ReorderScenesArgs {
  chapterId: string;
  orderedSceneIds: string[];
}
