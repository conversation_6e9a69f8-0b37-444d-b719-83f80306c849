/* Общие стили контейнера */
.settings-editor.layout { /* Добавляем .layout */
  display: flex; /* Используем flexbox для сайдбара и контента */
  height: 100%;
  overflow: hidden; /* Убираем скролл с основного контейнера */
  box-sizing: border-box;
  background-color: var(--app-editor-background); /* Используем --app- */
  color: var(--app-foreground);
}

/* Стили сайдбара */
.settings-sidebar {
  width: 220px; /* Фиксированная ширина сайдбара */
  flex-shrink: 0;
  background-color: var(--app-sideBar-background); /* Фон как у основного сайдбара */
  padding: 15px 0px 15px 15px; /* Убираем правый padding */
  box-sizing: border-box;
  border-right: 1px solid var(--app-border);
  display: flex;
  flex-direction: column;
}

.settings-search-input { /* Отдельный стиль для поиска в сайдбаре */
  width: calc(100% - 15px); /* Учитываем правый отступ родителя */
  padding: 8px 10px;
  box-sizing: border-box;
  background-color: var(--app-input-background); /* Используем --app- */
  color: var(--app-input-foreground);
  border: 1px solid var(--app-border);
  border-radius: 3px;
  outline: none;
  margin-bottom: 15px; /* Отступ снизу */
  margin-right: 15px; /* Добавляем правый отступ */
}
.settings-search-input:focus {
   border-color: var(--app-statusBar-background);
}

.settings-categories {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto; /* Скролл для категорий */
  padding-right: 15px; /* Добавляем правый отступ для скроллбара */
}

.settings-categories li {
  padding: 8px 10px;
  cursor: pointer;
  border-radius: 3px;
  margin-bottom: 2px;
  color: var(--app-tab-inactiveForeground); /* Цвет неактивной категории */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.settings-categories li:hover {
  background-color: var(--app-list-hoverBackground);
  color: var(--app-foreground); /* Цвет текста при наведении */
}

.settings-categories li.active {
  background-color: var(--app-list-activeSelectionBackground);
  color: var(--app-button-foreground); /* Цвет активной категории */
}

/* Стили для области контента */
.settings-content {
  flex-grow: 1;
  padding: 20px 30px; /* Увеличим отступы */
  overflow-y: auto; /* Скролл для настроек */
  box-sizing: border-box;
}

.settings-content h1 {
  margin-top: 0;
  margin-bottom: 25px; /* Увеличим отступ */
  border-bottom: 1px solid var(--app-border);
  padding-bottom: 15px; /* Увеличим отступ */
  font-weight: 600;
  font-size: 1.4em; /* Крупнее заголовок */
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 20px; /* Увеличим расстояние между настройками */
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 20px; /* Увеличим отступ */
  border-bottom: 1px solid var(--app-border);
}
/* Убираем рамку у последнего элемента */
.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}


.setting-details {
  flex: 1;
  margin-right: 30px; /* Увеличим отступ */
}

.setting-details label {
  display: block;
  font-weight: 600; /* Сделаем жирнее */
  margin-bottom: 5px; /* Увеличим отступ */
  color: var(--app-foreground);
  font-size: 1.05em; /* Немного увеличим */
}

.setting-details p {
  margin: 0;
  font-size: 0.9em;
  color: var(--app-tab-inactiveForeground);
}

.setting-control {
  min-width: 200px; /* Увеличим минимальную ширину */
  display: flex;
  align-items: center;
  /* Можно добавить выравнивание по правому краю, если нужно */
  /* justify-content: flex-end; */
}

/* Стили для элементов управления */
.setting-control input[type="checkbox"] {
  height: 16px; /* Стандартный размер */
  width: 16px;
  accent-color: var(--app-button-background);
}

.setting-control input[type="number"],
.setting-control input[type="text"],
.setting-control select {
  padding: 6px 10px; /* Увеличим padding */
  background-color: var(--app-input-background);
  color: var(--app-input-foreground);
  border: 1px solid var(--app-border);
  border-radius: 3px;
  min-width: 120px; /* Увеличим */
  box-sizing: border-box; /* Добавляем */
}

.setting-control input:focus,
.setting-control select:focus {
   border-color: var(--app-statusBar-background);
   outline: none;
}

/* Стиль для измененных настроек */
.setting-control input.modified,
.setting-control select.modified {
  /* Используем рамку слева, как в VS Code */
  border-left: 3px solid var(--app-statusBar-background);
  padding-left: 7px; /* Сдвигаем текст правее */
}

/* Стили для неподдерживаемого типа */
.setting-control .unsupported-type {
    font-style: italic;
    color: var(--app-tab-inactiveForeground);
}


.settings-editor.loading,
.settings-editor.error,
.settings-editor.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--app-tab-inactiveForeground); /* Пример */
}

.settings-editor.error {
  color: var(--app-errorForeground); /* Используем --app- */
}