import React from 'react';
import { useNotificationStore } from '@renderer/state/notificationStore';
import type { Notification } from '@renderer/components/Notifications/NotificationToast'; // Adjust import path if NotificationToast moves
import { Icon } from '@renderer/components/Icon/Icon'; // Adjust import path
import './NotificationsView.panel.view.css'; // CSS file for styling

export const NotificationsView: React.FC = () => {
  // Use the correct state properties and actions from the updated store
  const notificationHistory = useNotificationStore((state) => state.notificationHistory);
  const removeNotificationFromHistory = useNotificationStore((state) => state.removeNotificationFromHistory);
  const clearNotificationHistory = useNotificationStore((state) => state.clearNotificationHistory);

  return (
    <div className="notifications-view panel-view-base"> {/* Add common panel class */}
      <div className="notifications-view-header view-header-base"> {/* Add common header class */}
        <span>Notifications</span>
        {/* Add Clear All button */}
        {notificationHistory.length > 0 && ( // Show button only if there are notifications
            <button
                onClick={clearNotificationHistory}
                title="Clear All Notifications"
                className="view-action-button" // Use common button style
            >
               <Icon name="Trash2" size={16} />
            </button>
        )}
      </div>
      <div className="notifications-list content-base"> {/* Add common content class */}
        {notificationHistory.length === 0 ? (
          <p className="no-notifications">No notifications.</p>
        ) : (
          // Use notificationHistory for mapping
          notificationHistory.map((notification: Notification) => ( // Add type annotation
            <div key={notification.id} className={`notification-item notification-item-${notification.type}`}>
              <span className="notification-message">{notification.message}</span>
              {/* Use removeNotificationFromHistory for the dismiss button */}
              <button
                className="notification-dismiss-button"
                onClick={() => removeNotificationFromHistory(notification.id)}
                title="Dismiss"
              >
               <Icon name="X" size={14} /> {/* Use Icon component */}
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

// Default export is needed for lazy loading
export default NotificationsView;