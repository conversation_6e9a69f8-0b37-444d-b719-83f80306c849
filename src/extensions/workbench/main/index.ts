import type { ExtensionContext } from '@main/core/extensions/extension.registry'; // Correct import path
import type { IDisposable } from '@shared/types/lifecycle';

/**
 * Activates the core Workbench extension.
 * This extension registers core UI components like the Settings Editor and Notifications View.
 * @param context The extension context provided by the core.
 */
export function activate(context: ExtensionContext): void {
    context.logger.info('Activating Core Workbench Extension...');

    const disposables: IDisposable[] = [];

    // Register core UI components (moved from MainApplication)
    const editorSvc = context.editors; // Get services from context
    const viewSvc = context.views;

    if (editorSvc && viewSvc) {
        // Register Settings Editor
        // Check if already registered? Maybe not necessary if core guarantees order
        disposables.push(editorSvc.registerEditorProvider({
            editorType: 'workbench:settings-editor',
            extensionId: 'ai-books.workbench', // Use this extension's ID
            componentName: 'SettingsEditor',
            title: 'Settings',
            icon: 'Settings'
        }));

        // Register Notifications View
        disposables.push(viewSvc.registerView({
            id: 'workbench.views.notifications',
            name: 'Notifications',
            componentName: 'NotificationsView',
            extensionId: 'ai-books.workbench', // Use this extension's ID
            location: 'panel',
            icon: 'Bell',
        }));
    } else {
        context.logger.error('EditorService or ViewService not available in context for Workbench extension.');
    }

    // Add all disposables created during registration to the context's subscriptions
    disposables.forEach(disposable => context.subscriptions.push(disposable));

    context.logger.info('Core Workbench Extension activated.');
}

/**
 * Deactivates the core Workbench extension.
 * Currently, no specific cleanup is needed beyond what the ExtensionContext handles.
 */
export function deactivate(): void {
    // No specific deactivation logic needed for now
}