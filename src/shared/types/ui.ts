/**
 * UI-related type definitions.
 */
import {Dictionary, JsonObject, JsonValue} from './common';
import {CSSProperties, ReactNode} from "react";
import {NotificationType} from "@services/notification.service";

/**
 * Component props with children
 */
export interface ComponentProps {
  children?: ReactNode;
  className?: string;
  style?: CSSProperties;
  // [key: string]: unknown; // Removed index signature
  // Consider adding 'data?: Dictionary<unknown>' or similar if arbitrary props are needed
}

/**
 * View component props
 */
export interface ViewProps extends ComponentProps {
  /** Unique ID of the view. @readonly */
  readonly viewId: string; // Consider branding ViewId
  /** ID of the extension providing the view. @readonly */
  readonly extensionId?: string; // Consider branding ExtensionId
}

/**
 * Panel component props
 */
export interface PanelProps extends ComponentProps {
  /** Unique ID of the panel. @readonly */
  readonly panelId: string; // Consider branding PanelId
  /** Display title of the panel. @readonly */
  readonly title?: string;
  /** Optional icon identifier. @readonly */
  readonly icon?: string;
}

/**
 * Editor component props
 */
export interface EditorProps extends ComponentProps {
  /** Unique ID of the editor instance. @readonly */
  readonly editorId: string;
  /** ID of the data being edited. @readonly */
  readonly dataId: string;
  /** Initial content (optional). @readonly */
  readonly content?: string;
  /** Whether the editor is read-only. @readonly */
  readonly readOnly?: boolean;
  /** Callback for content changes. */
  onChange?: (content: string) => void; // Remains mutable for component interaction
}

/**
 * Tab item data
 */
export interface TabData {
  /** Unique identifier for the tab. @readonly */
  readonly id: string;
  /** Display title of the tab. @readonly */
  readonly title: string;
  /** Optional icon identifier. @readonly */
  readonly icon?: string;
  /** ID of the extension providing the tab content (if applicable). @readonly */
  readonly extensionId?: string; // Consider branding ExtensionId
  /** Name of the React component to render. @readonly */
  readonly componentName: string;
  /** ID of the data associated with the tab. @readonly */
  readonly dataId: string;
  /** Whether the tab has unsaved changes. */
  dirty?: boolean; // Mutable state
  /** Optional additional data associated with the tab. @readonly */
  readonly data?: Readonly<Dictionary<unknown>>;
}

/**
 * Serializable data representing an editor tab, suitable for storage and IPC.
 */
export interface EditorTabData {
  /** Unique identifier for the tab. @readonly */
  readonly id: string;
  /** Display title of the tab. @readonly */
  readonly title: string;
  /** Type ID of the editor associated with this tab. @readonly */
  readonly editorType: string;
  /** ID of the extension providing the editor. @readonly */
  readonly extensionId: string; // Consider branding ExtensionId
  /** Name of the React component to render (often derived from editorType). @readonly */
  readonly componentName: string;
  /** ID of the data being edited (e.g., sceneId, characterId). @readonly */
  readonly dataId: string;
  /** Optional icon identifier. @readonly */
  readonly icon?: string;
  /** Whether the tab has unsaved changes. @readonly */
  readonly isDirty?: boolean;
  // Add other serializable properties as needed, e.g., scroll position, selection state
}


export interface EditorTab extends JsonObject {
  id: string;
  editorType: string;
  extensionId: string;
  componentName: string;
  title: string;
  dataId: string;
  icon: string;
  isDirty: boolean;
}


/**
 * View data
 */
export interface ViewData {
  /** Unique ID of the view. @readonly */
  readonly id: string; // Consider branding ViewId
  /** Display name of the view. @readonly */
  readonly name: string;
  /** Optional icon identifier. @readonly */
  readonly icon?: string;
  /** ID of the extension providing the view. @readonly */
  readonly extensionId?: string; // Consider branding ExtensionId
  /** Name of the React component to render. @readonly */
  readonly componentName: string;
  /** Default position in the workbench. @readonly */
  readonly position?: 'left' | 'right' | 'bottom';
  /** Optional additional data associated with the view. @readonly */
  readonly data?: Readonly<Dictionary<unknown>>;
}

/**
 * Theme data
 */
export interface ThemeData {
  /** Unique ID of the theme. @readonly */
  readonly id: string; // Consider branding ThemeId
  /** Display name of the theme. @readonly */
  readonly name: string;
  /** Base type of the theme. @readonly */
  readonly type: 'light' | 'dark';
  /** Map of color tokens to their values. @readonly */
  readonly colors: Readonly<Dictionary<string>>;
  /** Optional syntax highlighting rules. @readonly */
  readonly tokenColors?: readonly {
    readonly name?: string;
    readonly scope: string | readonly string[];
    readonly settings: {
      readonly foreground?: string;
      readonly background?: string;
      readonly fontStyle?: string; // e.g., "italic", "bold", "underline"
    };
  }[];
}

/**
 * Status bar item data
 */
export interface StatusBarItemData {
  /** Unique ID of the status bar item. @readonly */
  readonly id: string; // Consider branding StatusBarItemId
  /** Text content to display. @readonly */
  readonly text: string;
  /** Tooltip shown on hover. @readonly */
  readonly tooltip?: string;
  /** Command ID to execute on click. @readonly */
  readonly command?: string;
  /** Priority for ordering (higher numbers are further left/right). @readonly */
  readonly priority?: number;
  /** Alignment within the status bar. @readonly */
  readonly position?: 'left' | 'right';
  /** Optional icon identifier. @readonly */
  readonly icon?: string;
}

/**
 * Data structure representing a menu item, primarily for use within the UI (Renderer process).
 * Suitable for defining context menus or other UI-driven menu elements.
 * Compare with AppMenuItem (in menu.ts) which is used for IPC transfer of the main app menu.
 */
export interface MenuItemData {
  /** Optional unique ID for the menu item within its context. @readonly */
  readonly id?: string; // Consider branding MenuItemId - Made optional as not always needed
  /** Display label. @readonly */
  readonly label: string;
  /** Type of menu item. @readonly */
  readonly type?: 'normal' | 'separator' | 'submenu' | 'checkbox' | 'radio';
  /** Checked state for checkbox/radio. @readonly */
  readonly checked?: boolean;
  /** Enabled state. @readonly */
  readonly enabled?: boolean;
  /** Visibility state. @readonly */
  readonly visible?: boolean;
  /** Keyboard shortcut string. @readonly */
  readonly accelerator?: string;
  /** Submenu items. @readonly */
  readonly submenu?: readonly MenuItemData[];
  /** Click handler (less common in pure data). */
  readonly click?: () => void;
  /** Command ID to execute. @readonly */
  readonly commandId?: string;
  /** Arguments for the command. @readonly */
  readonly commandArgs?: Readonly<Dictionary<JsonValue>>;
}

/**
 * Context menu data
 */
export interface ContextMenuData {
  /** Unique ID for the context menu instance (if needed). @readonly */
  readonly id: string; // Consider branding ContextMenuId
  /** Items in the menu. @readonly */
  readonly items: readonly MenuItemData[];
  /** X coordinate for display. @readonly */
  readonly x?: number;
  /** Y coordinate for display. @readonly */
  readonly y?: number;
}

/**
 * Dialog options
 */
export interface DialogOptions {
  /** Dialog window title. @readonly */
  readonly title?: string;
  /** Main message content. @readonly */
  readonly message?: string;
  /** Additional details. @readonly */
  readonly detail?: string;
  /** Dialog type (influences icon). @readonly */
  readonly type?: 'none' | 'info' | 'error' | 'question' | 'warning';
  /** Custom button labels. @readonly */
  readonly buttons?: readonly string[];
  /** Index of the default button. @readonly */
  readonly defaultId?: number;
  /** Index of the cancel button. @readonly */
  readonly cancelId?: number;
  /** Label for an optional checkbox. @readonly */
  readonly checkboxLabel?: string;
  /** Initial state of the checkbox. @readonly */
  readonly checkboxChecked?: boolean;
}

/**
 * Dialog result
 */
export interface DialogResult {
  /** Index of the button clicked. @readonly */
  readonly response: number;
  /** State of the checkbox when closed. @readonly */
  readonly checkboxChecked?: boolean;
}

/**
 * File dialog options
 */
export interface FileDialogOptions {
  /** Dialog window title. @readonly */
  readonly title?: string;
  /** Default path to open. @readonly */
  readonly defaultPath?: string;
  /** Custom label for the confirmation button. @readonly */
  readonly buttonLabel?: string;
  /** File extension filters. @readonly */
  readonly filters?: readonly {
    readonly name: string;
    readonly extensions: readonly string[];
  }[];
  /** Dialog properties. @readonly */
  readonly properties?: readonly (| 'openFile'
    | 'openDirectory'
    | 'multiSelections'
    | 'showHiddenFiles'
    | 'createDirectory'
    | 'promptToCreate'
    | 'noResolveAliases'
    | 'treatPackageAsDirectory'
    | 'dontAddToRecent')[];
  /** Message displayed above input fields (macOS). @readonly */
  readonly message?: string;
}

/**
 * Notification options
 */
export interface NotificationOptions {
  /** Notification type. @readonly */
  readonly type: NotificationType;
  /** Notification body content. @readonly */
  readonly message: string;
  /** Optional path to an icon. @readonly */
  readonly icon?: string;
  /** Whether to play a sound. @readonly */
  readonly silent?: boolean;
  /** Urgency level (Linux). @readonly */
  readonly urgency?: 'normal' | 'critical' | 'low';
  /** Timeout duration in milliseconds. @readonly */
  readonly duration: number;
  /** Custom actions (buttons). @readonly */
  readonly actions?: readonly {
    readonly type: 'button';
    readonly text: string;
  }[];
}
