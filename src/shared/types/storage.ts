/**
 * Storage-related type definitions to replace 'any' types
 */
import { Dictionary, JsonValue } from './common';
// export type { JsonValue } from './common'; // Removed redundant export

/**
 * Storage options
 */
export interface StorageOptions {
  basePath?: string;
  encoding?: BufferEncoding;
  pretty?: boolean;
  extension?: string;
}

/**
 * Storage item
 */
export interface StorageItem<T = JsonValue> {
  id: string;
  data: T;
  path?: string;
  lastModified?: number;
}

/**
 * Storage query options
 */
export interface StorageQueryOptions {
  limit?: number;
  offset?: number;
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
  filter?: Dictionary<unknown>;
}

/**
 * Storage query result
 */
export interface StorageQueryResult<T = JsonValue> {
  items: StorageItem<T>[];
  total: number;
  limit?: number;
  offset?: number;
}

/**
 * Storage event data
 */
export interface StorageEventData<T = JsonValue> {
  type: 'create' | 'update' | 'delete';
  item: StorageItem<T>;
}

/**
 * Book storage item
 */
export interface BookStorageItem {
  id: string;
  title: string;
  description?: string;
  path: string;
  lastModified: number;
  created: number;
  metadata?: Dictionary<JsonValue>;
}

/**
 * Chapter storage item
 */
export interface ChapterStorageItem {
  id: string;
  bookId: string;
  title: string;
  position: number;
  lastModified: number;
  created: number;
  metadata?: Dictionary<JsonValue>;
}

/**
 * Scene storage item
 */
export interface SceneStorageItem {
  id: string;
  bookId: string;
  chapterId: string;
  title: string;
  content: string;
  position: number;
  lastModified: number;
  created: number;
  metadata?: Dictionary<JsonValue>;
}

/**
 * Character storage item
 */
export interface CharacterStorageItem {
  id: string;
  name: string;
  description?: string;
  lastModified: number;
  created: number;
  metadata?: Dictionary<JsonValue>;
}

/**
 * Settings storage item
 */
export interface SettingsStorageItem {
  key: string;
  value: JsonValue;
  scope: 'user' | 'workspace';
  lastModified: number;
}
