/**
 * Menu-related type definitions.
 */
import {Dictionary, JsonValue} from './common';

/**
 * Menu item type
 */
export type MenuItemType = 'normal' | 'separator' | 'submenu' | 'checkbox' | 'radio';

/**
 * Menu item role
 */
export type MenuItemRole =
  | 'undo'
  | 'redo'
  | 'cut'
  | 'copy'
  | 'paste'
  | 'pasteAndMatchStyle'
  | 'delete'
  | 'selectAll'
  | 'reload'
  | 'forceReload'
  | 'toggleDevTools'
  | 'resetZoom'
  | 'zoomIn'
  | 'zoomOut'
  | 'togglefullscreen'
  | 'window'
  | 'minimize'
  | 'close'
  | 'help'
  | 'about'
  | 'services'
  | 'hide'
  | 'hideOthers'
  | 'unhide'
  | 'quit'
  | 'startSpeaking'
  | 'stopSpeaking'
  | 'zoom'
  | 'front'
  | 'appMenu'
  | 'fileMenu'
  | 'editMenu'
  | 'viewMenu'
  | 'windowMenu';

/**
 * Menu item accelerator
 */
export type MenuItemAccelerator = string;

/**
 * Options for defining a menu item, similar to Electron's MenuItemConstructorOptions
 * but with added properties like commandId and when.
 */
export interface MenuItemOptions {
  /** Optional unique ID for the menu item within its menu. @readonly */
  readonly id?: string;
  /** Display label. @readonly */
  readonly label?: string;
  /** Type of menu item. @readonly */
  readonly type?: MenuItemType;
  /** Standard Electron menu item role. @readonly */
  readonly role?: MenuItemRole;
  /** Keyboard shortcut. @readonly */
  readonly accelerator?: MenuItemAccelerator;
  /** Submenu items. @readonly */
  readonly submenu?: readonly MenuItemOptions[];
  /** Click handler (primarily for Electron main process). */
  readonly click?: () => void;
  /** Whether the item is enabled. @readonly */
  readonly enabled?: boolean;
  /** Whether the item is visible. @readonly */
  readonly visible?: boolean;
  /** Checked state for checkbox/radio items. @readonly */
  readonly checked?: boolean;
  /** Whether to register the accelerator with Electron. @readonly */
  readonly registerAccelerator?: boolean;
  /** Command ID to execute (used by our command service). @readonly */
  readonly commandId?: string;
  /** Arguments to pass to the command. @readonly */
  readonly commandArgs?: Readonly<Dictionary<JsonValue>>;
  /** Context key expression for visibility/enabled state. @readonly */
  readonly when?: string;
}

/**
 * Represents a menu item contribution from an extension or core.
 */
export interface MenuItemContribution { // Removed export
  /** The command ID to execute when the item is clicked. */
  command: string; // Keep as string here, will be cast to CommandId when used
  /** An alternative command ID (e.g., for Shift+Click). */
  alt?: string;
  /** Context key expression for visibility/enabled state. */
  when?: string;
  /** Sorting group (e.g., 'navigation', '1_modification', '9_cutcopypaste'). Lower numbers/strings come first. */
  group?: string;
}

/**
 * Internal representation of a registered menu item, including its origin.
 */
export interface RegisteredMenuItem extends MenuItemContribution {
  /** ID of the extension that registered this item. */
  extensionId: string; // Track which extension registered the item
}

/**
 * Context menu options
 */
export interface ContextMenuOptions {
  /** X coordinate for popup. */
  readonly x?: number;
  /** Y coordinate for popup. */
  readonly y?: number;
  /** Menu items to display. @readonly */
  readonly items: readonly MenuItemOptions[];
  /** Identifier for the context where the menu is shown. @readonly */
  readonly contextId?: string;
}

/**
 * Menu template
 */
export type MenuTemplate = readonly MenuItemOptions[];

/**
 * Menu builder options
 */
export interface MenuBuilderOptions {
  /** Optional template to build from. @readonly */
  readonly template?: MenuTemplate;
  /** Optional context data for 'when' clauses. @readonly */
  readonly context?: Readonly<Dictionary<JsonValue>>;
  
  /** Allow other properties if needed. */
  readonly [key: string]: unknown;
}

/**
 * Structure of an application menu item specifically for transferring the main
 * application menu (File, Edit, View, etc.) structure from Main to Renderer via IPC.
 * This is a simplified, serializable version of MenuItemOptions.
 */
export interface AppMenuItem {
  /** Display label (optional for separators). @readonly */
  readonly label?: string;
  /** Submenu items. @readonly */
  readonly submenu?: readonly AppMenuItem[];
  /** Command ID to execute. @readonly */
  readonly commandId?: string;
  /** Standard Electron menu item role. @readonly */
  readonly role?: MenuItemRole;
  /** Type of menu item. @readonly */
  readonly type?: MenuItemType;
  /** Checked state for checkbox/radio items. @readonly */
  readonly checked?: boolean;
  /** Keyboard shortcut string to display. @readonly */
  readonly accelerator?: string; // Added accelerator for display
}
