/**
 * Command-related type definitions.
 */
import {Dictionary, JsonValue} from './common';
import type {SettingKey} from './ipc'; // Import SettingKey as type-only

/**
 * Base type for command arguments. Prefer specific argument interfaces where possible.
 */
export type CommandArgs = Record<string, unknown>;

/**
 * Type for the result of a command handler.
 */
export type CommandResult = unknown;

// CommandContext was removed as it's not passed via CommandService.executeCommand

/**
 * Represents a command registered in the system.
 */
export interface Command {
  /** Unique identifier for the command. */
  readonly id: string;
  /** User-facing title for the command palette and menus. @readonly */
  readonly title: string;
  /** Category for grouping in the command palette. @readonly */
  readonly category?: string;
  /** Optional icon identifier. @readonly */
  readonly icon?: string;
  /** The function executed when the command is invoked. Receives arguments object. */
  readonly handler: (args?: CommandArgs) => Promise<CommandResult> | CommandResult; // args are optional
  /** Context key expression for enabling/disabling the command. @readonly */
  readonly when?: string;
  /** Default keybinding(s). @readonly */
  readonly keybinding?: string | readonly string[];
  /** Whether to show in the command palette (default: true). @readonly */
  readonly showInPalette?: boolean;
}

/**
 * Arguments for opening an editor tab.
 */
export interface EditorOpenTabArgs {
  editorType: string;
  /** ID of the data to load (e.g., SceneId, CharacterId). @readonly */
  readonly dataId: string; // Use the branded DataId type
  /** Optional title override for the tab. @readonly */
  readonly title?: string;
  /** Optional additional options for the editor. @readonly */
  readonly options?: Readonly<Record<string, unknown>>;
}

/**
 * Arguments for book-related commands.
 */
export interface BookCommandArgs {
  readonly bookId?: string;
  readonly title?: string;
  readonly description?: string;
  readonly path?: string; // File path if applicable
}

/**
 * Arguments for chapter-related commands.
 */
export interface ChapterCommandArgs {
  readonly bookId?: string;
  readonly chapterId?: string;
  readonly title?: string;
  readonly position?: number; // For ordering
}

/**
 * Arguments for scene-related commands.
 */
export interface SceneCommandArgs {
  readonly bookId?: string;
  readonly chapterId?: string;
  readonly sceneId?: string;
  readonly title?: string;
  readonly content?: string; // Scene content
  readonly position?: number; // For ordering
}

/**
 * Arguments for character-related commands.
 */
export interface CharacterCommandArgs {
  readonly characterId?: string;
  readonly name?: string;
  readonly description?: string;
  readonly metadata?: Readonly<Dictionary<JsonValue>>; // Make metadata readonly
}

/**
 * Arguments for editor-specific commands (e.g., save, format).
 */
export interface EditorCommandArgs {
  /** ID of the specific editor instance/tab. @readonly */
  readonly editorId?: string; // Ensure readonly is present
  /** ID of the data being edited (e.g., SceneId). @readonly */
  readonly dataId?: string; // Use the branded DataId type
  /** Type of the editor (relevant if editorId is not provided). @readonly */
  readonly editorType?: string;
  /** Optional content payload. @readonly */
  readonly content?: string;
}

/**
 * Arguments for UI-related commands (e.g., toggle view visibility).
 */
export interface UICommandArgs {
  readonly viewId?: string;
  readonly panelId?: string;
  readonly visible?: boolean;
  readonly position?: string; // e.g., 'left', 'right', 'bottom'
}

/**
 * Arguments for menu-related commands (e.g., show context menu).
 */
export interface MenuCommandArgs {
  readonly menuId?: string; // ID of the menu to show
  readonly x?: number; // Screen coordinate
  readonly y?: number; // Screen coordinate
  readonly contextId?: string; // Optional identifier for the context item clicked
}

/**
 * Arguments for settings-related commands.
 */
export interface SettingsCommandArgs {
  readonly key?: SettingKey; // Use branded SettingKey and add readonly
  readonly value?: unknown; // Add readonly
  readonly scope?: string; // Add readonly
}
