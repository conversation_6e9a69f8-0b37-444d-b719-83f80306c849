import { Disposable } from './common';

/**
 * Represents the unique identifier for an extension.
 * Uses branding to prevent accidental assignment of other string types.
 */
export type ExtensionId = string & { readonly __brand: 'ExtensionId' };

/**
 * Defines extension loading states
 */
export enum ExtensionState {
    DISCOVERED = 'discovered',
    ACTIVATING = 'activating',
    ACTIVE = 'active',
    DEACTIVATING = 'deactivating',
    DEACTIVATED = 'deactivated',
    ERROR = 'error'
}

/**
 * Extension manifest information
 */
export interface ExtensionManifest {
    /**
     * Unique identifier for the extension
     */
    readonly id: ExtensionId;

    /**
     * Human-readable name
     * @readonly
     */
    readonly name: string;

    /**
     * Extension description
     * @readonly
     */
    readonly description?: string;

    /**
     * Extension version
     * @readonly
     */
    readonly version: string;

    /**
     * Main entry point
     * @readonly
     */
    readonly main?: string;

    /**
     * Extensions this extension depends on (by their ExtensionId)
     * @readonly
     */
    readonly dependencies?: ExtensionId[];

    /**
     * Extension activation priority (0-100, higher is loaded earlier)
     * @readonly
     */
    readonly activationPriority?: number;

    /**
     * Whether this extension should be lazy-loaded (only activated when needed)
     * @readonly
     */
    readonly lazyLoad?: boolean;

    /**
     * Activation events that can trigger lazy-loaded extension
     * @readonly
     */
    readonly activationEvents?: string[];

    /**
     * Extension capabilities and features
     * @readonly
     */
    readonly contributes?: {
        readonly views?: ExtensionViewContribution[];
        readonly editors?: ExtensionEditorContribution[];
        readonly commands?: ExtensionCommandContribution[];
        readonly menus?: ExtensionMenuContribution[];
        readonly [key: string]: unknown; // Keep index signature mutable if needed, or make readonly too
    }
}

/**
 * Extension implementation interface
 */
export interface Extension extends Disposable { // Renamed from IExtension
    /**
     * Unique identifier matching manifest
     */
    readonly id: ExtensionId;

    /**
     * Current extension state
     */
    readonly state: ExtensionState;

    /**
     * Activates the extension
     * @returns Promise that resolves when activation is complete
     */
    activate(): Promise<void>;

    /**
     * Deactivates the extension
     * @returns Promise that resolves when deactivation is complete
     */
    deactivate(): Promise<void>;

    /**
     * Tests if extension is activated
     */
    isActivated(): boolean;
}

/**
 * Base interface for all extension contributions
 */
export interface ExtensionContribution {
    /**
     * ID of the extension providing this contribution
     */
    readonly extensionId: ExtensionId;
}

/**
 * View contribution from an extension
 */
export interface ExtensionViewContribution extends ExtensionContribution {
    readonly id: string; // ID of the view itself
    readonly name: string;
    readonly componentName: string;
    readonly location: string; // Target view container ID
    readonly when?: string; // Context key expression
    readonly icon?: string;
}

/**
 * Editor contribution from an extension
 */
export interface ExtensionEditorContribution extends ExtensionContribution {
    readonly editorType: string; // Unique type identifier for the editor
    readonly componentName: string;
    readonly title?: string;
    readonly icon?: string;
    readonly when?: string; // Context key expression
}

/**
 * Command contribution from an extension
 */
export interface ExtensionCommandContribution extends ExtensionContribution {
    readonly id: string; // ID of the command itself
    readonly title: string;
    readonly category?: string;
    readonly keybinding?: string | string[];
    readonly when?: string; // Context key expression
}

/**
 * Menu contribution from an extension
 */
export interface ExtensionMenuContribution extends ExtensionContribution {
    readonly id: string; // ID of the menu item itself
    readonly command: string; // Command ID to execute
    readonly group?: string; // Sorting group
    readonly when?: string; // Context key expression
    readonly location: string; // Target menu ID
}

/**
 * Performance data for extension activation
 */
export interface ExtensionActivationTiming {
    readonly extensionId: ExtensionId;
    readonly startTime: number;
    readonly endTime: number;
    readonly duration: number;
}

/**
 * Extension activation metrics
 */
export interface ExtensionMetrics {
    /**
     * Time (ms) taken to activate the extension
     */
    readonly activationTime: number;

    /**
     * Time when the extension was activated
     * @readonly
     */
    readonly activatedAt: Date;

    /**
     * Memory usage of the extension (if available)
     * @readonly
     */
    readonly memoryUsage?: number;
}

/**
 * Error information for extension loading/activation failures
 */
export interface ExtensionError {
    readonly extensionId: ExtensionId;
    readonly error: Error;
    readonly phase: 'discovery' | 'load' | 'activate';
    readonly timestamp: Date;
}

/**
 * API to track extension performance and state
 */
export interface ExtensionTrackingService { // Renamed from IExtensionTrackingService
    /**
     * Records the start of extension activation
     */
    recordActivationStart(extensionId: ExtensionId): void;

    /**
     * Records the completion of extension activation
     */
    recordActivationEnd(extensionId: ExtensionId, error?: Error): void;

    /**
     * Gets activation metrics for all extensions
     */
    getActivationMetrics(): Map<string, ExtensionMetrics>;

    /**
     * Gets errors that occurred during extension loading/activation
     */
    getErrors(): ExtensionError[];

    /**
     * Records an error during extension discovery or loading phase
     */
    recordError(extensionId: ExtensionId, error: Error, phase: 'discovery' | 'load'): void;
}
