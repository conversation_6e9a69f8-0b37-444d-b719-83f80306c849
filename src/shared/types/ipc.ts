/**
 * @file Defines common interfaces for Inter-Process Communication (IPC) payloads
 * and standardized error structures, following the guidelines in
 * docs/architecture/communication-model.md.
 */

import type {EditorProvider} from "@services/editor.service"; // Keep as type-only import if possible
import type {AppSettings, SettingDeclaration} from './settings';
import type {AppMenuItem} from './menu'; // Import from shared types
import type {EditorTabData} from './ui'; // Import shared types
import type {KeybindingInfo} from './keybindings';
import type {LogLevel} from './logging';
import {Command, CommandArgs} from './commands'; // Import Command and relevant IDs
import type {JsonValue} from './common';
import type {ViewContainerDescriptor, ViewDescriptor} from "@services/view.service"; // Keep as type-only import if possible

// --- Branded Types for IPC ---
export type SettingKey = keyof AppSettings
export type ContextKey = string & { readonly __brand: 'ContextKey' };
export type NotificationId = string & { readonly __brand: 'NotificationId' };
export type DialogId = string & { readonly __brand: 'DialogId' };

/**
 * Generic structure for data sent with ipcRenderer.invoke() or ipcMain.handle().
 * @template T The type of the payload.
 */
export interface IpcInvokeArgs<T = unknown> {
  /** The data being sent. @readonly */
  readonly payload: T;
}

/**
 * Generic structure for the result returned by ipcMain.handle().
 * Can be data or a standardized error.
 * @template T The type of the successful data.
 */
export type IpcInvokeResult<T = unknown> =
  | { readonly success: true; readonly data: T }
  | { readonly success: false; readonly error: IpcErrorData };

/**
 * Generic structure for data sent with webContents.send() or ipcRenderer.on().
 * @template T The type of the payload.
 */
export interface IpcEventArgs<T = unknown> {
  /** The data being sent. @readonly */
  readonly payload: T;
}

/**
 * Standardized structure for errors propagated via IPC.
 */
export interface IpcErrorData {
  /** A unique code identifying the type of error (e.g., 'STORAGE_ERROR', 'NOT_FOUND'). @readonly */
  readonly code: string;
  /** A human-readable error message. @readonly */
  readonly message: string;
  /** Optional additional details about the error. @readonly */
  readonly details?: unknown;
}


// --- Core Service Specific Types ---

// core:settings
export interface SettingsGetValueArgs {
  /** The key of the setting to retrieve. @readonly */
  readonly key: SettingKey;
}

export type SettingsGetValueResult = unknown; // Could be AppSettings[SettingKey] but validation is complex

export interface SettingsSetValueArgs {
  /** The key of the setting to set. @readonly */
  readonly key: SettingKey;
  /** The new value for the setting. @readonly */
  readonly value: unknown; // Value type depends on the key, validation happens in service
}

export interface SettingsDeclarationsAndValuesResult {
  /** Array of setting declarations. @readonly */
  readonly declarations: readonly SettingDeclaration[];
  /** Current values of all settings. @readonly */
  readonly values: Readonly<AppSettings>;
}

export interface SettingsChangedEventData {
  /** The key of the setting that changed. @readonly */
  readonly key: SettingKey;
  /** The new value of the setting. @readonly */
  readonly newValue: unknown;
}

// core:commands
// Description of a command sent to the renderer (e.g., for Command Palette)
// Matches the return type of CommandService.getCommands()
export type CommandDescription = Omit<Command, 'handler' | 'when' | 'keybinding'> & {
  /** Active keybindings for the command in the current context. @readonly */
  readonly keybindings?: readonly string[];
};

export interface CommandsExecuteArgs {
  /** The ID of the command to execute. @readonly */
  readonly commandId: string;
  /** Arguments to pass to the command handler. @readonly */
  readonly args: CommandArgs;
}

export type CommandsExecuteResult = unknown;
// Result type for getting all commands, matches the updated CommandDescription
export type CommandsGetAllResult = readonly CommandDescription[];

// core:context
export interface ContextGetValueArgs {
  /** The key of the context value to retrieve. @readonly */
  readonly key: ContextKey;
}

export type ContextGetValueResult = unknown; // Could be more specific if context values are better defined

export interface ContextSetValueArgs {
  /** The key of the context value to set. @readonly */
  readonly key: ContextKey;
  /** The new value for the context key. @readonly */
  readonly value: JsonValue; // Use JsonValue
}

export type ContextSetValueResult = undefined;

export interface ContextChangedEventData {
  /** The key of the context value that changed. @readonly */
  readonly key: ContextKey;
  /** The new value of the context key. @readonly */
  readonly value: unknown;
}

/** Expected structure for the 'activeEditorContext' context value. */
export interface ActiveEditorContextValue {
  readonly editorType: string;
  readonly dataId: string; // Using string for now, consider DataId if appropriate everywhere
}

// core:app
export type AppGetVersionResult = string;

// core:window
export type WindowIsMaximizedResult = boolean;

// core:editor
export interface EditorGetProviderDetailsArgs {
  /** The type ID of the editor provider. @readonly */
  readonly editorType: string;
}

// Editor provider details result (Consider if this needs to be distinct from EditorProvider)
// For now, let's assume EditorProvider itself is serializable or we use a subset
// export interface EditorProviderDetails {
//   readonly id: string;
//   readonly name: string;
//   readonly supportedFileTypes?: ReadonlyArray<string>;
// }

// Use the actual EditorProvider type if it's suitable for IPC, otherwise define a specific subset
export type EditorGetProviderDetailsResult = Readonly<EditorProvider> | null;

export interface EditorGetContentArgs {
  /** The ID of the specific editor instance. @readonly */
  readonly editorId: string;
}

export type EditorGetContentResult = string | null;

export interface EditorRequestSaveEventData {
  /** The ID of the specific editor instance requesting save. @readonly */
  readonly editorId: string;
  /** The ID of the data being edited (e.g., SceneId). @readonly */
  readonly sceneId: string; // Example, adjust as needed (might be more generic dataId)
}

export type ViewsGetRegisteredResult = readonly ViewDescriptor[];
export type ViewContainersGetRegisteredResult = readonly ViewContainerDescriptor[];

export type KeybindingsGetActiveResult = readonly KeybindingInfo[];


// core:workbench
// Use EditorTabData for the event data type
export type WorkbenchOpenTabEventData = Readonly<EditorTabData>; // Use shared type, make readonly

export interface WorkbenchCloseTabEventData {
  /** The ID of the tab to close. @readonly */
  readonly tabId: string;
}

/** Options for showing an in-app notification within the workbench UI (e.g., a toast). */
export interface WorkbenchNotificationOptions {
  /** Optional unique ID for the notification. @readonly */
  readonly id?: NotificationId;
  /** Optional title for the notification. @readonly */
  readonly title?: string;
  /** The main message content. @readonly */
  readonly message: string;
  /** Type of notification for styling/icon. @readonly */
  readonly type: 'info' | 'warning' | 'error' | 'success';
  /** Optional duration in milliseconds before auto-closing. @readonly */
  readonly duration?: number;
  // readonly actions?: ReadonlyArray<NotificationAction>; // Define NotificationAction if needed
}

export type WorkbenchShowNotificationEventData = Readonly<WorkbenchNotificationOptions>; // Use renamed type

export interface InputDialogOptions {
  /** Unique ID to correlate the response. @readonly */
  readonly dialogId: string;
  /** Title of the input dialog window. @readonly */
  readonly title: string;
  /** Prompt message displayed to the user. @readonly */
  readonly prompt: string;
  /** Optional default value for the input field. @readonly */
  readonly defaultValue?: string;
  // readonly validation?: ... // Define validation rules if needed
}

export type WorkbenchShowInputDialogEventData = Readonly<InputDialogOptions>;

export interface DialogInputResponseData {
  /** The ID of the dialog this response corresponds to. @readonly */
  readonly dialogId: string;
  /** The value entered by the user, or null if cancelled. @readonly */
  readonly value: string | null;
}

// core:menu
export interface MenuShowContextMenuArgs {
  /** The identifier for the context menu to show. @readonly */
  readonly contextIdentifier: string;
  /** Optional arguments specific to the context. @readonly */
  readonly menuArgs?: Readonly<Record<string, unknown>>;
}

export type MenuShowContextMenuResult = undefined // Or boolean indicating success? Let's use void.
export type MenuGetAppMenuResult = readonly AppMenuItem[]; // Use shared type, make readonly

// core:log
export interface LogMessageData {
  /** Severity level of the log message. @readonly */
  readonly level: LogLevel;
  /** The main log message. @readonly */
  readonly message: string;
  /** Optional additional details. @readonly */
  readonly details?: unknown;
}

// Define LogEntry structure used internally by LoggingService and potentially sent via IPC
export interface LogEntry {
  /** Timestamp string (e.g., ISO format). @readonly */
  readonly timestamp: string;
  /** Severity level. @readonly */
  readonly level: LogLevel;
  /** Main log message. @readonly */
  readonly message: string;
  /** Optional structured metadata. @readonly */
  readonly meta?: Readonly<Record<string, unknown>>;
  /** Serializable error information, if applicable. @readonly */
  readonly error?: {
    readonly name?: string;
    readonly message: string;
    readonly stack?: string;
  };
}

/**
 * Type guard to check if an IpcInvokeResult represents an error.
 * @param result The result object from ipcRenderer.invoke.
 * @returns True if the result is an error, false otherwise.
 */
export function isIpcError<T>(result: IpcInvokeResult<T>): result is {
  readonly success: false;
  readonly error: IpcErrorData
} {
  // Ensure result and result.error are not null/undefined before checking success flag
  return result != null && !result.success && result.error != null;
}

// --- Save Confirmation Dialog Types ---
export interface DialogShowSaveConfirmationArgs {
  /** Optional title override for the dialog window. @readonly */
  readonly title?: string;
  /** Main message (e.g., "Save changes to 'Character X'?"). @readonly */
  readonly message: string;
  /** Optional extra details displayed below the main message. @readonly */
  readonly detail?: string;
}

export type DialogShowSaveConfirmationResult = 'save' | 'dontsave' | 'cancel';
