/**
 * AI-related type definitions to replace 'any' types
 */
import {Dictionary} from './common';
import type {JSONSchema7} from 'json-schema';
import type {IpcErrorData} from '@shared/types/ipc';

export interface AIProviderClient {
  /**
   * Generates a structured output based on the prompt and schema.
   * @param prompt The formatted prompt string.
   * @param outputSchema The JSON Schema the output should conform to.
   * @param options Provider-specific options (model, temperature, etc.).
   * @returns A promise resolving to the parsed JSON object or throwing an error.
   */
  generateStructuredOutput(
    prompt: string,
    outputSchema: JSONSchema7,
    options?: AIProviderConfig
  ): Promise<AICompletionResponse | IpcErrorData>;
  
  /**
   * (Placeholder for Post-MVP) Generates output as a stream of tokens.
   * @param prompt The formatted prompt string.
   * @param options Provider-specific options.
   * @returns An async generator yielding string tokens or an IpcErrorData.
   */
  streamOutput?(
    prompt: string,
    options?: AIProviderConfig
  ): AsyncGenerator<string | IpcErrorData, void, unknown>;
}

/**
 * AI provider configuration
 */
export interface AIProviderConfig {
  readonly apiKey?: string;
  readonly model?: string;
  readonly baseUrl?: string;
  readonly organization?: string;
  readonly maxTokens?: number;
  readonly temperature?: number;
  readonly options?: Readonly<Dictionary<unknown>>; // Make options dictionary readonly
}

export interface AITaskDefinition {
  /** Unique identifier for the task (e.g., 'characters:consistencyCheck') */
  taskId: string;
  /** Human-readable description for logging or potential UI */
  description: string;
  /** The prompt template string with placeholders like {{placeholder}} */
  promptTemplate: string;
  /** The JSON Schema object describing the expected structured output */
  outputSchema: JSONSchema7;
  /** Optional array of keys indicating the context data AIService should try to gather */
  requiredContext?: string[];
  // Optional: Function provided by extension to gather specific context? (More complex, maybe Post-MVP)
  // gatherContext?: (baseContext: any, coreServices: CoreServicesAPI) => Promise<any>;
}

/**
 * AI message role
 */
export type AIMessageRole = 'system' | 'user' | 'assistant' | 'function';

/**
 * AI message
 */
export interface AIMessage {
  readonly role: AIMessageRole;
  readonly content: string;
  readonly name?: string; // Often used for function calls/responses
}

/**
 * AI completion response
 */
export interface AICompletionResponse {
  readonly id: string;
  readonly choices: readonly {
    readonly index: number;
    readonly message: AIMessage;
    readonly finishReason: string; // e.g., 'stop', 'length', 'function_call'
  }[];
  readonly usage?: {
    readonly promptTokens: number;
    readonly completionTokens: number;
    readonly totalTokens: number;
  };
}

/**
 * AI completion stream response
 */
export interface AICompletionStreamResponse {
  readonly id: string;
  readonly choices: readonly {
    readonly index: number;
    readonly delta: Partial<Readonly<AIMessage>>; // Delta contains partial readonly AIMessage
    readonly finishReason: string | null;
  }[];
}

/**
 * AI error
 */
export interface AIError {
  readonly message: string;
  readonly type: string;
  readonly param?: string;
  readonly code?: string;
}
