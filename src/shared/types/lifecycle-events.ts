/**
 * Перечисление, представляющее основные события жизненного цикла приложения.
 */
export enum AppLifecycleEvent {
    // События инициализации
    SERVICES_INSTANTIATED = 'services-instantiated',
    CORE_SETTINGS_LOADED = 'core-settings-loaded',
    EXTENSIONS_DISCOVERED = 'extensions-discovered',
    WINDOW_CREATED = 'window-created',
    SERVICES_INITIALIZED = 'services-initialized',
    GLOBAL_HANDLERS_REGISTERED = 'global-handlers-registered',
    UI_SETUP_COMPLETE = 'ui-setup-complete',
    EXTENSIONS_ACTIVATED = 'extensions-activated',
    APP_READY = 'app-ready',

    // События активности
    WINDOW_FOCUS = 'window-focus',
    WINDOW_BLUR = 'window-blur',

    // События восстановления
    SERVICE_RESTARTED = 'service-restarted',

    // События завершения работы
    SHUTDOWN_STARTED = 'shutdown-started',
    SAVE_ALL_TRIGGERED = 'save-all-triggered',
    SAVE_ALL_COMPLETE = 'save-all-complete',
    EXTENSIONS_DEACTIVATED = 'extensions-deactivated',
    SERVICES_DISPOSED = 'services-disposed',
    APP_QUIT = 'app-quit'
}

/**
 * Интерфейс для данных о состоянии жизненного цикла приложения.
 */
export interface AppLifecycleEventData {
    timestamp: number;
    details?: Record<string, unknown>;
}

/**
 * Тип для обработчиков событий жизненного цикла.
 */
export type AppLifecycleEventHandler = (data: AppLifecycleEventData) => void | Promise<void>;

/**
 * Интерфейс для системы событий жизненного цикла приложения.
 */
import { Disposable } from './common';

export interface AppLifecycle { // Renamed from IAppLifecycle
    /**
     * Подписаться на событие жизненного цикла.
     */
    on(event: AppLifecycleEvent, handler: AppLifecycleEventHandler): Disposable;

    /**
     * Отправить событие жизненного цикла.
     */
    emit(event: AppLifecycleEvent, details?: Record<string, unknown>): Promise<void>;

    /**
     * Получить текущее состояние жизненного цикла.
     */
    getCurrentState(): Map<AppLifecycleEvent, AppLifecycleEventData>;

    /**
     * Проверить, произошло ли определенное событие.
     */
    hasEventOccurred(event: AppLifecycleEvent): boolean;
}
