import type { JsonRectangle } from './common'; // Import JsonRectangle
import { EditorTab } from './ui';

// Define the structure for AI settings
interface AISettings {
  provider: 'openai' | 'anthropic' | 'none'; // Default provider
  apiKey: string; // API Key for the selected provider
  model: string; // Default model to use
  // Add other AI settings here later (e.g., temperature, maxTokens)
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  editorFontSize: number;
  editorLineHeight: number; // Новая настройка
  autosaveInterval: number; // в миллисекундах

  ai: AISettings;

  workbench: {
    tabs: {
      openTabs: EditorTab[]; // Use shared type
      activeTabId: string | null;
    };
    layout: {
      isSidebarVisible: boolean;
      isPanelVisible: boolean;
      sidebarWidth: number;
      panelHeight: number;
    }
    windowBounds: JsonRectangle; // Use JsonRectangle
    projectsRoot: string;
  };
}

// Define allowed control types for settings UI
export type SettingControlType =
  | 'text'
  | 'number'
  | 'checkbox'
  | 'select'
  | 'themeSelect'
  | 'path'
  | 'password'; // Added 'password'

// Интерфейс для декларации настроек (для регистрации расширениями)
export interface SettingDeclaration {
  id: string; // Уникальный ID (e.g., 'books.editor.fontSize', 'ai.apiKey')
  label?: string; // название (опционально, можно генерировать из ID)
  description: string; // Описание для UI
  type: 'string' | 'number' | 'boolean' | 'enum' | 'array' | 'object'; // Тип данных
  scope: 'user'; // Currently only user scope is supported
  default: unknown; // Default value for the setting
  controlType?: SettingControlType; // Optional: Explicitly define UI control type
  enum?: string[]; // For controlType: 'select' or type: 'enum'
  enumDescriptions?: string[]; // Optional descriptions for enum values
  minimum?: number; // For type: 'number'
  maximum?: number; // For type: 'number'
  format?: 'path'; // Optional format hint (e.g., for path input)
  markdownDescription?: string; // Optional longer description supporting markdown
}
