/**
 * Logging-related type definitions to replace 'any' types
 */
import { Dictionary } from './common';

/**
 * Log level
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Log entry
 */
export interface LogEntry {
  timestamp: number;
  level: LogLevel;
  message: string;
  source?: string; // Keep source for scoped loggers
  details?: unknown[]; // Changed from data to details array
}

/**
 * Logger interface
 */
export interface Logger {
  debug(message: string, ...args: unknown[]): void; // Use variadic args
  info(message: string, ...args: unknown[]): void; // Use variadic args
  warn(message: string, ...args: unknown[]): void; // Use variadic args
  error(message: string, ...args: unknown[]): void; // Use variadic args
  log(level: LogLevel, message: string, ...args: unknown[]): void; // Use variadic args
}

/**
 * Log filter
 */
export interface LogFilter {
  level?: LogLevel;
  source?: string;
  search?: string;
  from?: number;
  to?: number;
}

/**
 * Log storage
 */
export interface LogStorage {
  add(entry: LogEntry): void;
  get(filter?: LogFilter): LogEntry[];
  clear(): void;
}

/**
 * Log transport
 */
export interface LogTransport {
  name: string;
  log(entry: LogEntry): void;
  configure(options: Dictionary<unknown>): void;
}

/**
 * Console log transport options
 */
export interface ConsoleLogTransportOptions {
  colorize?: boolean;
  showTimestamp?: boolean;
  showLevel?: boolean;
  showSource?: boolean;
}

/**
 * File log transport options
 */
export interface FileLogTransportOptions {
  filename?: string;
  directory?: string;
  maxSize?: number;
  maxFiles?: number;
  format?: 'json' | 'text';
}

/**
 * IPC log transport options
 */
export interface IpcLogTransportOptions {
  channel?: string;
  bufferSize?: number;
  sendInterval?: number;
}
