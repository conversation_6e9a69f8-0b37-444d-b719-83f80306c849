/**
 * Git-related type definitions.
 */
import { Dictionary } from './common';

/**
 * Represents a Git commit hash.
 */
export type CommitHash = string & { readonly __brand: 'CommitHash' };

/**
 * Git repository info
 */
export interface GitRepositoryInfo {
  readonly path: string; // Path to the repository root
  readonly branch: string; // Current branch name
  readonly remote?: string; // Default remote name (e.g., 'origin')
  hasChanges: boolean; // Whether there are uncommitted changes (mutable state)
}

/**
 * Git status
 */
export interface GitStatus {
  readonly branch: string;
  readonly ahead: number; // Commits ahead of upstream
  readonly behind: number; // Commits behind upstream
  readonly staged: readonly GitFileStatus[];
  readonly unstaged: readonly GitFileStatus[];
  readonly untracked: readonly string[];
}

/**
 * Git file status
 */
export interface GitFileStatus {
  readonly path: string;
  readonly status: GitFileStatusCode;
}

/**
 * Git file status code
 */
export type GitFileStatusCode = 
  | 'added'
  | 'modified'
  | 'deleted'
  | 'renamed'
  | 'copied'
  | 'untracked'
  | 'unmodified'
  | 'ignored'
  | 'conflicted';

/**
 * Git commit
 */
export interface GitCommit {
  readonly hash: CommitHash;
  readonly shortHash: string; // Typically first 7 chars of hash
  readonly message: string;
  readonly author: string;
  readonly email: string;
  readonly date: Date;
}

/**
 * Git branch
 */
export interface GitBranch {
  readonly name: string;
  current: boolean; // Is this the currently checked out branch? (mutable state)
  readonly remote?: string; // Name of the remote it tracks (if any)
  readonly upstream?: string; // Full name of the upstream branch (e.g., 'origin/main')
  ahead?: number; // Commits ahead of upstream (mutable state)
  behind?: number; // Commits behind upstream (mutable state)
}

/**
 * Git remote
 */
export interface GitRemote {
  readonly name: string;
  readonly url: string;
}

/**
 * Git tag
 */
export interface GitTag {
  readonly name: string;
  readonly hash: CommitHash; // Hash the tag points to
  readonly message?: string; // Annotation message if it's an annotated tag
}

/**
 * Git diff
 */
export interface GitDiff {
  readonly path: string;
  readonly oldPath?: string; // For renamed/copied files
  readonly status: GitFileStatusCode;
  readonly additions: number;
  readonly deletions: number;
  readonly hunks: readonly GitDiffHunk[];
}

/**
 * Git diff hunk
 */
export interface GitDiffHunk {
  readonly oldStart: number;
  readonly oldLines: number;
  readonly newStart: number;
  readonly newLines: number;
  readonly lines: readonly string[]; // Lines prefixed with '+', '-', or ' '
}

/**
 * Git stash
 */
export interface GitStash {
  readonly index: number; // Stash index (e.g., stash@{0})
  readonly message: string;
  readonly date: Date;
}

/**
 * Git command options
 */
export interface GitCommandOptions {
  cwd?: string;
  env?: Dictionary<string>;
  timeout?: number;
  maxBuffer?: number;
}

/**
 * Git clone options
 */
export interface GitCloneOptions extends GitCommandOptions {
  branch?: string;
  depth?: number;
  recursive?: boolean;
  progress?: (progress: string) => void;
}

/**
 * Git commit options
 */
export interface GitCommitOptions extends GitCommandOptions {
  author?: string;
  email?: string;
  amend?: boolean;
  noVerify?: boolean;
  allowEmpty?: boolean;
}

/**
 * Git push options
 */
export interface GitPushOptions extends GitCommandOptions {
  remote?: string;
  branch?: string;
  setUpstream?: boolean;
  force?: boolean;
  tags?: boolean;
}

/**
 * Git pull options
 */
export interface GitPullOptions extends GitCommandOptions {
  remote?: string;
  branch?: string;
  rebase?: boolean;
  fastForward?: 'ff-only' | 'no-ff' | 'ff';
}
