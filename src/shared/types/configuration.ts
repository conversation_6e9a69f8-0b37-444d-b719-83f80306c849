import type { Event } from './utility-types'; // Assuming Event type exists or needs creation
import type { JsonValue } from './common';

/**
 * Detailed information about a configuration setting's value and scope.
 */
export interface ConfigurationInspect<T> {
  key: string;
  defaultValue?: T; // Value from default declaration
  userValue?: T; // Value from user settings
  // workspaceValue?: T; // Placeholder for future workspace scope
  // effectiveValue: T; // The final value after considering scopes
}

/**
 * Service interface for accessing and managing application configuration.
 */
export interface IConfigurationService {
  /**
   * Retrieves the effective value of a configuration setting.
   * @param key The configuration key (e.g., 'editor.fontSize').
   * @param defaultValue An optional default value to return if the setting is not found.
   * @returns A promise resolving to the effective value of the setting, strongly typed.
   */
  getValue<T extends JsonValue>(key: string, defaultValue?: T): Promise<T>;

  /**
   * Updates the value of a configuration setting in the user scope.
   * @param key The configuration key to update.
   * @param value The new value for the setting.
   * @returns A promise that resolves when the update is complete.
   */
  updateValue(key: string, value: JsonValue): Promise<void>;

  /**
   * An event that fires when a configuration setting changes.
   * The event payload contains the key and the new effective value.
   */
  onDidChangeConfiguration: Event<{ key: string; newValue: JsonValue }>;

  /**
   * Inspects a configuration setting, providing details about its default,
   * user-specific, and potentially workspace-specific values.
   * @param key The configuration key to inspect.
   * @returns A promise resolving to an object containing detailed information about the setting's values.
   */
  inspect<T extends JsonValue>(key: string): Promise<ConfigurationInspect<T>>;

  // Potential future methods:
  // has(key: string): boolean;
  // reloadConfiguration(): Promise<void>;
}

// Define a basic Event type if it doesn't exist elsewhere
// This is a simplified version. A more robust implementation might be needed.
// export interface Disposable {
//   dispose(): void;
// }
// export interface Event<T> {
//   (listener: (e: T) => any, thisArgs?: any, disposables?: Disposable[]): Disposable;
// }