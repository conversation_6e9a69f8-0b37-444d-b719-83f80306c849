/**
 * Common type definitions to replace 'any' types throughout the codebase
 */

/**
 * Generic JSON value type
 */
export type JsonValue =
  | string
  | number
  | boolean
  | null
  | JsonObject
  | JsonArray;

/**
 * Generic JSON object type
 */
export interface JsonObject {
  [key: string]: JsonValue;
}

/**
 * Generic JSON array type
 */
export type JsonArray = JsonValue[];

/**
 * Type for a rectangle compatible with JSON serialization.
 * Defined as an intersection to satisfy JsonObject requirements.
 */
export type JsonRectangle = JsonObject & {
  x: number;
  y: number;
  width: number;
  height: number;
};

/**
 * Generic dictionary type
 */
export type Dictionary<T> = Record<string, T>;

/**
 * Generic callback function type
 */
export type Callback<T = void, Args extends unknown[] = unknown[]> = (...args: Args) => T;

/**
 * Generic error handler type
 */
export type ErrorHandler = (error: Error) => void;

/**
 * Generic success handler type
 */
export type SuccessHandler<T = void> = (result: T) => void;

/**
 * Generic event handler type
 */
export type EventHandler<T = unknown> = (event: T) => void;

/**
 * Generic command handler type
 */
export type CommandHandler<T = unknown, R = unknown> = (args: T) => Promise<R> | R;

/**
 * Generic IPC handler type
 */
export type IpcHandler<T = unknown, R = unknown> = (args: T) => Promise<R> | R;

/**
 * Generic configuration type
 */
export type Config = Dictionary<JsonValue>;

/**
 * Generic options type
 */
export type Options = Dictionary<JsonValue>;

/**
 * Generic metadata type
 */
export type Metadata = Dictionary<JsonValue>;

/**
 * Generic record ID type
 */
export type RecordId = string;

/**
 * Generic result type
 */
export type Result<T, E = Error> =
  | { readonly success: true; readonly data: T }
  | { readonly success: false; readonly error: E };

/**
 * Generic disposable interface
 */
export interface Disposable {
  dispose(): Promise<void>;
}
