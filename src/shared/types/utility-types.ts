/**
 * Utility types to replace 'any' with more specific types
 */

/**
 * Use for node-like structures in editors
 */
export interface EditorNode {
  type: string;
  children?: EditorNode[];
  [key: string]: unknown;
}

/**
 * Represents a disposable resource.
 */
export interface Disposable {
  dispose(): void;
}

/**
 * Represents a typed event emitter.
 * Listeners can be registered and will be called with the event payload.
 * Registration returns a Disposable to unregister the listener.
 */
export type Event<T> = (
  listener: (e: T) => void,
  thisArgs?: unknown,
  disposables?: Disposable[]
) => Disposable;
