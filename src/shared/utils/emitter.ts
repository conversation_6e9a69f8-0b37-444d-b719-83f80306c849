import type { Disposable, Event } from '@shared/types/utility-types';

/**
 * A simple event emitter implementation.
 */
export class Emitter<T> {
  private _listeners: Set<(e: T) => void> = new Set<(e: T) => void>();
  private _event?: Event<T>;

  /**
   * The event listeners can subscribe to.
   */
  get event(): Event<T> {
    if (!this._event) {
      this._event = (listener: (e: T) => void): Disposable => {
        this._listeners.add(listener);
        const disposable: Disposable = {
          dispose: () => {
            this._listeners.delete(listener);
          }
        };
        return disposable;
      };
    }
    return this._event;
  }

  /**
   * Fires the event with the given payload to all listeners.
   * @param event The event payload.
   */
  fire(event: T): void {
    this._listeners.forEach(listener => {
      try {
        listener(event);
      } catch (e) {
        // Log or handle listener errors if necessary
        console.error('Error in Emitter listener:', e);
      }
    });
  }

  /**
   * Disposes the emitter, removing all listeners.
   */
  dispose(): void {
    this._listeners.clear();
    this._event = undefined; // Allow GC
  }
}