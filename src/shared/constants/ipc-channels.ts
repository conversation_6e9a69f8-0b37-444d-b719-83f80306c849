// src/shared/constants/ipc-channels.ts

/**
 * Определяет константы для имен каналов IPC, используемых в приложении.
 * Помогает обеспечить согласованность между основным и рендерер-процессами.
 */
export const IpcChannels = {
  SETTINGS_GET: 'core:settings.get',
  SETTINGS_SET: 'core:settings.set',
  SETTINGS_GET_DECLARATIONS_AND_VALUES: 'core:settings.getDeclarationsAndValues',

  // Configuration Service (New)
  CONFIGURATION_GET_VALUE: 'core:config.getValue',
  CONFIGURATION_UPDATE_VALUE: 'core:config.updateValue',
  CONFIGURATION_INSPECT: 'core:config.inspect',
  CONFIGURATION_DID_CHANGE: 'core:config.didChange', // Main -> Renderer
  SETTINGS_GET_DECLARATIONS: 'core:settings.getDeclarations', // Replaces GET_DECLARATIONS_AND_VALUES

  // Editor Service
  EDITOR_GET_PROVIDER_DETAILS: 'core:editor.getProviderDetails',
  EDITOR_PROVIDER_REGISTERED: 'core:editor.providerRegistered',
  EDITOR_PROVIDER_UNREGISTERED: 'core:editor.providerUnregistered',

  // View Service
  VIEWS_GET_REGISTERED: 'core:views.getRegistered',
  VIEWS_GET_REGISTERED_CONTAINERS: 'core:views.getRegisteredContainers',
  VIEW_REGISTERED: 'core:view.registered',
  VIEW_UNREGISTERED: 'core:view.unregistered',

  // Keybinding Service
  KEYBINDINGS_GET_ACTIVE: 'core:keybindings.getActive',
  KEYBINDINGS_UPDATED: 'core:keybindings.updated',

  // Command Service
  COMMANDS_EXECUTE: 'core:commands.execute',
  COMMANDS_GET_ALL: 'core:commands.getAll',
  COMMAND_REGISTERED: 'core:command.registered',
  COMMAND_UNREGISTERED: 'core:command.unregistered',

  // Context Service
  CONTEXT_GET: 'core:context.get',
  CONTEXT_SET: 'core:context.set',

  // Logging Service
  LOG_MESSAGE: 'core:log.message',

  // Menu Service
  MENU_SHOW_CONTEXT_MENU: 'core:menu.showContextMenu',
  MENU_GET_APP_MENU: 'core:menu.getAppMenu',

  // Window Controls
  WINDOW_GET_PLATFORM: 'core:window.getPlatform',
  WINDOW_IS_MAXIMIZED: 'core:window.isMaximized',
  WINDOW_MINIMIZE: 'core:window.minimize',
  WINDOW_MAXIMIZE: 'core:window.maximize',
  WINDOW_UNMAXIMIZE: 'core:window.unmaximize',
  WINDOW_CLOSE: 'core:window.close',
  WINDOW_TOGGLE_FULLSCREEN: "core:window.toggleFullScreen",

  // App Info
  APP_GET_VERSION: 'core:app.getVersion',

  // Dialog Service
  DIALOG_INPUT_RESPONSE: 'core:dialog.inputResponse',
  DIALOG_SHOW_OPEN_DIALOG: 'core:dialog.showOpenDialog',
  DIALOG_SHOW_MESSAGE_BOX: 'core:dialog.showMessageBox',

  // Workbench Events (Main -> Renderer)
  WORKBENCH_TOGGLE_PALETTE: 'core:workbench.togglePalette',
  WORKBENCH_TOGGLE_SIDEBAR: 'core:workbench.toggle-sidebar',
  WORKBENCH_TOGGLE_PANEL: 'core:workbench.toggle-panel',
  WORKBENCH_OPEN_TAB: 'core:workbench.open-tab',
  WORKBENCH_REQUEST_SAVE_ALL: 'core:workbench.request-save-all',
  WORKBENCH_SHOW_INPUT_DIALOG: 'core:workbench.showInputDialog',
  WORKBENCH_SHOW_NOTIFICATION: 'core:workbench.showNotification',

  // Context/Settings Change Events (Main -> Renderer)
  CONTEXT_CHANGED: 'core:context.changed',
  SETTINGS_CHANGED: 'core:settings.changed',

  // Window State Events (Main -> Renderer)
  WINDOW_MAXIMIZED_EVENT: 'core:window.maximized',
  WINDOW_UNMAXIMIZED_EVENT: 'core:window.unmaximized',

  // Dialog for save confirmation
  DIALOG_SHOW_SAVE_CONFIRMATION: 'core:dialog.showSaveConfirmation',

  // Error Handling Service
  ERROR_GET_STATS: 'core:error.getStats',
  ERROR_GET_HEALTH: 'core:error.getHealth',
  ERROR_UPDATE: 'core:error.update',
  ERROR_HEALTH_UPDATE: 'core:error.healthUpdate',
  WORKBENCH_TOGGLE_DEVTOOLS: "core:toggleDevTools",
} as const;

export type IpcChannelName = typeof IpcChannels[keyof typeof IpcChannels];
