// src/shared/constants/command-ids.ts

/**
 * Определяет константы для идентификаторов команд, используемых в приложении.
 */
export const CommandIds = {
  // Core Workbench Actions
  OPEN_SETTINGS: 'core:workbench.action.openSettings',
  OPEN_EDITOR: 'core:workbench.action.openEditor', // Generic editor opener
  TOGGLE_DEV_TOOLS: 'core:workbench.action.toggleDevTools',
  TOGGLE_FULL_SCREEN: 'core:workbench.action.toggleFullScreen',
  TOGGLE_COMMAND_PALETTE: 'core:workbench.action.toggleCommandPalette',
  TOGGLE_SIDEBAR_VISIBILITY: 'core:workbench.action.toggleSidebarVisibility',
  TOGGLE_PANEL_VISIBILITY: 'core:workbench.action.togglePanelVisibility',
  SAVE_ALL: 'core:workbench.action.saveAll',
  TRIGGER_SAVE_ACTIVE_EDITOR: 'core:workbench:triggerSaveActiveEditor', // Internal trigger

  // Core App Actions
  APP_ABOUT: 'core:app.about',
  APP_QUIT: 'core:app.quit',

  // Core Service Actions
  CORE_SETTINGS_SET: 'core.settings.set',
  CORE_CONTEXT_SET: 'core.context.set',
  // CORE_DIALOG_SHOW_OPEN: 'core.dialog.showOpen', // Example if needed later

} as const;

// Optional: Define a type for command IDs if needed elsewhere
export type CommandId = typeof CommandIds[keyof typeof CommandIds];
