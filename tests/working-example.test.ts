import { describe, it, expect, jest } from '@jest/globals';

// Test a simple utility function to demonstrate the testing infrastructure works
describe('Working Test Example', () => {
  describe('Basic Functionality', () => {
    it('should run basic assertions', () => {
      expect(1 + 1).toBe(2);
      expect('hello').toBe('hello');
      expect([1, 2, 3]).toHaveLength(3);
    });

    it('should handle async operations', async () => {
      const asyncFunction = async (value: string) => {
        return new Promise<string>((resolve) => {
          setTimeout(() => resolve(`processed: ${value}`), 10);
        });
      };

      const result = await asyncFunction('test');
      expect(result).toBe('processed: test');
    });

    it('should work with mocks', () => {
      const mockFunction = jest.fn();
      mockFunction.mockReturnValue('mocked result');

      const result = mockFunction('input');

      expect(mockFunction).toHaveBeenCalledWith('input');
      expect(result).toBe('mocked result');
    });
  });

  describe('Error Handling', () => {
    it('should handle thrown errors', () => {
      const errorFunction = () => {
        throw new Error('Test error');
      };

      expect(errorFunction).toThrow('Test error');
    });

    it('should handle async errors', async () => {
      const asyncErrorFunction = async () => {
        throw new Error('Async test error');
      };

      await expect(asyncErrorFunction()).rejects.toThrow('Async test error');
    });
  });

  describe('Object and Array Testing', () => {
    it('should test objects', () => {
      const testObject = {
        id: 1,
        name: 'test',
        active: true,
      };

      expect(testObject).toHaveProperty('id', 1);
      expect(testObject).toHaveProperty('name');
      expect(testObject.active).toBe(true);
    });

    it('should test arrays', () => {
      const testArray = ['apple', 'banana', 'cherry'];

      expect(testArray).toContain('banana');
      expect(testArray).toHaveLength(3);
      expect(testArray[0]).toBe('apple');
    });
  });

  describe('Mock Implementation', () => {
    it('should create and use mock implementations', () => {
      interface TestService {
        getData(id: string): Promise<{ id: string; name: string }>;
        saveData(data: any): Promise<boolean>;
      }

      const mockService: jest.Mocked<TestService> = {
        getData: jest.fn(),
        saveData: jest.fn(),
      };

      // Setup mock implementations
      mockService.getData.mockResolvedValue({ id: 'test-id', name: 'test-name' });
      mockService.saveData.mockResolvedValue(true);

      // Test the mocks
      return Promise.all([
        mockService.getData('test-id').then(result => {
          expect(result).toEqual({ id: 'test-id', name: 'test-name' });
          expect(mockService.getData).toHaveBeenCalledWith('test-id');
        }),
        mockService.saveData({ test: 'data' }).then(result => {
          expect(result).toBe(true);
          expect(mockService.saveData).toHaveBeenCalledWith({ test: 'data' });
        })
      ]);
    });
  });

  describe('Database-like Operations', () => {
    it('should simulate database operations', async () => {
      // Simulate a simple in-memory database
      const mockDatabase = {
        data: new Map<string, any>(),
        
        async create(id: string, data: any) {
          this.data.set(id, { ...data, id, createdAt: Date.now() });
          return { changes: 1, lastInsertRowid: id };
        },
        
        async get(id: string) {
          return this.data.get(id) || null;
        },
        
        async getAll() {
          return Array.from(this.data.values());
        },
        
        async update(id: string, updates: any) {
          const existing = this.data.get(id);
          if (!existing) return { changes: 0 };
          
          this.data.set(id, { ...existing, ...updates, updatedAt: Date.now() });
          return { changes: 1 };
        },
        
        async delete(id: string) {
          const existed = this.data.has(id);
          this.data.delete(id);
          return { changes: existed ? 1 : 0 };
        }
      };

      // Test create
      const createResult = await mockDatabase.create('test-1', { name: 'Test Item' });
      expect(createResult.changes).toBe(1);

      // Test get
      const item = await mockDatabase.get('test-1');
      expect(item).toMatchObject({
        id: 'test-1',
        name: 'Test Item',
      });
      expect(item.createdAt).toBeGreaterThan(0);

      // Test update
      const updateResult = await mockDatabase.update('test-1', { name: 'Updated Item' });
      expect(updateResult.changes).toBe(1);

      const updatedItem = await mockDatabase.get('test-1');
      expect(updatedItem.name).toBe('Updated Item');
      expect(updatedItem.updatedAt).toBeGreaterThan(0);

      // Test getAll
      await mockDatabase.create('test-2', { name: 'Second Item' });
      const allItems = await mockDatabase.getAll();
      expect(allItems).toHaveLength(2);

      // Test delete
      const deleteResult = await mockDatabase.delete('test-1');
      expect(deleteResult.changes).toBe(1);

      const deletedItem = await mockDatabase.get('test-1');
      expect(deletedItem).toBeNull();
    });
  });

  describe('Service-like Pattern', () => {
    it('should test service-like patterns', async () => {
      // Simulate a service with dependencies
      class MockCharacterService {
        constructor(
          private storage: any,
          private logger: any
        ) {}

        async createCharacter(name: string, description?: string) {
          this.logger.info(`Creating character: ${name}`);
          
          const character = {
            id: `char-${Date.now()}`,
            name,
            description,
            createdAt: Date.now(),
          };

          await this.storage.create(character.id, character);
          return character;
        }

        async getCharacter(id: string) {
          this.logger.info(`Getting character: ${id}`);
          return await this.storage.get(id);
        }
      }

      // Create mocks
      const mockStorage = {
        create: jest.fn().mockResolvedValue({ changes: 1 }),
        get: jest.fn(),
      };

      const mockLogger = {
        info: jest.fn(),
        error: jest.fn(),
      };

      // Create service instance
      const characterService = new MockCharacterService(mockStorage, mockLogger);

      // Test character creation
      const character = await characterService.createCharacter('John Snow', 'Bastard of Winterfell');

      expect(character).toMatchObject({
        name: 'John Snow',
        description: 'Bastard of Winterfell',
      });
      expect(character.id).toMatch(/^char-\d+$/);
      expect(mockStorage.create).toHaveBeenCalledWith(character.id, character);
      expect(mockLogger.info).toHaveBeenCalledWith(`Creating character: John Snow`);

      // Test character retrieval
      mockStorage.get.mockResolvedValue(character);
      
      const retrievedCharacter = await characterService.getCharacter(character.id);
      
      expect(retrievedCharacter).toEqual(character);
      expect(mockStorage.get).toHaveBeenCalledWith(character.id);
      expect(mockLogger.info).toHaveBeenCalledWith(`Getting character: ${character.id}`);
    });
  });
});
