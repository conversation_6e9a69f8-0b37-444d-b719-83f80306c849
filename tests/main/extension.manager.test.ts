import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ExtensionManager } from '../../src/main/extension.manager';
import { ServiceManager } from '../../src/main/service.manager';
import type { LoggingServiceAPI } from '../../src/main/services/logging.service';

// Mock the ExtensionRegistry
jest.mock('../../src/main/extensions/extension.registry');

describe('ExtensionManager', () => {
  let extensionManager: ExtensionManager;
  let mockLogger: jest.Mocked<LoggingServiceAPI>;
  let mockServiceManager: jest.Mocked<ServiceManager>;
  let mockSendRendererEvent: jest.Mock;

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      createScopedLogger: jest.fn(() => mockLogger),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<LoggingServiceAPI>;

    // Create mock service manager
    mockServiceManager = {
      getCoreServicesAPI: jest.fn(() => ({
        commands: {},
        views: {},
        storage: {},
        configuration: {},
        ipc: {},
        notifications: {},
        logger: mockLogger,
        dialogs: {},
        context: {},
        menus: {},
        editors: {},
        git: {},
        ai: {},
        lifecycle: {},
      })),
      logger: mockLogger,
    } as unknown as jest.Mocked<ServiceManager>;

    mockSendRendererEvent = jest.fn();

    extensionManager = new ExtensionManager(mockServiceManager, () => mockSendRendererEvent);
  });

  describe('Initialization', () => {
    it('should create extension manager with service manager', () => {
      expect(extensionManager).toBeDefined();
      expect(mockServiceManager.getCoreServicesAPI).toHaveBeenCalled();
    });

    it('should create scoped logger', () => {
      expect(mockLogger.createScopedLogger).toHaveBeenCalledWith('ExtensionManager');
    });
  });

  describe('Extension Discovery', () => {
    it('should discover extensions successfully', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      MockedExtensionRegistry.prototype.discoverExtensions = jest.fn().mockResolvedValue(undefined);

      await extensionManager.discoverExtensions();

      expect(MockedExtensionRegistry.prototype.discoverExtensions).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('Поиск расширений...');
    });

    it('should handle discovery errors', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      const error = new Error('Discovery failed');
      MockedExtensionRegistry.prototype.discoverExtensions = jest.fn().mockRejectedValue(error);

      await expect(extensionManager.discoverExtensions()).rejects.toThrow('Discovery failed');

      expect(mockLogger.error).toHaveBeenCalledWith('Ошибка при поиске расширений:', error);
    });

    it('should not discover extensions when disposed', async () => {
      await extensionManager.dispose();

      await extensionManager.discoverExtensions();

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Попытка поиска расширений после уничтожения сервиса.'
      );
    });
  });

  describe('Extension Activation', () => {
    it('should activate all extensions successfully', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      MockedExtensionRegistry.prototype.activateAll = jest.fn().mockResolvedValue(undefined);

      await extensionManager.activateAll();

      expect(MockedExtensionRegistry.prototype.activateAll).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('Активация расширений...');
      expect(mockLogger.info).toHaveBeenCalledWith('Активация расширений завершена.');
    });

    it('should handle activation errors gracefully', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      const error = new Error('Activation failed');
      MockedExtensionRegistry.prototype.activateAll = jest.fn().mockRejectedValue(error);

      // Should not throw, but should log the error
      await expect(extensionManager.activateAll()).resolves.not.toThrow();

      expect(mockLogger.error).toHaveBeenCalledWith('Ошибка при активации расширений:', error);
    });

    it('should not activate extensions when disposed', async () => {
      await extensionManager.dispose();

      await extensionManager.activateAll();

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Попытка активации расширений после уничтожения сервиса.'
      );
    });
  });

  describe('Extension Deactivation', () => {
    it('should deactivate all extensions', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      MockedExtensionRegistry.prototype.deactivateAll = jest.fn().mockResolvedValue(undefined);

      await extensionManager.deactivateAll();

      expect(MockedExtensionRegistry.prototype.deactivateAll).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('Деактивация расширений...');
    });

    it('should not deactivate when already disposed', async () => {
      await extensionManager.dispose();

      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      MockedExtensionRegistry.prototype.deactivateAll = jest.fn();

      await extensionManager.deactivateAll();

      expect(MockedExtensionRegistry.prototype.deactivateAll).not.toHaveBeenCalled();
    });
  });

  describe('Extension API Access', () => {
    it('should get extension API successfully', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      const mockAPI = { someMethod: jest.fn() };
      MockedExtensionRegistry.prototype.getApi = jest.fn().mockResolvedValue(mockAPI);

      const result = await extensionManager.getApi('test.extension');

      expect(result).toBe(mockAPI);
      expect(MockedExtensionRegistry.prototype.getApi).toHaveBeenCalledWith('test.extension');
    });

    it('should return undefined for non-existent extension API', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      MockedExtensionRegistry.prototype.getApi = jest.fn().mockResolvedValue(undefined);

      const result = await extensionManager.getApi('nonexistent.extension');

      expect(result).toBeUndefined();
    });

    it('should not get API when disposed', async () => {
      await extensionManager.dispose();

      const result = await extensionManager.getApi('test.extension');

      expect(result).toBeUndefined();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        "Попытка получить API для 'test.extension' после уничтожения сервиса."
      );
    });
  });

  describe('Disposal', () => {
    it('should dispose extension manager and deactivate extensions', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      MockedExtensionRegistry.prototype.deactivateAll = jest.fn().mockResolvedValue(undefined);

      await extensionManager.dispose();

      expect(mockLogger.info).toHaveBeenCalledWith('Уничтожение ExtensionManager...');
      expect(MockedExtensionRegistry.prototype.deactivateAll).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('ExtensionManager уничтожен.');
    });

    it('should handle disposal errors gracefully', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      const error = new Error('Deactivation failed');
      MockedExtensionRegistry.prototype.deactivateAll = jest.fn().mockRejectedValue(error);

      // Should not throw, but should complete disposal
      await expect(extensionManager.dispose()).resolves.not.toThrow();

      expect(mockLogger.info).toHaveBeenCalledWith('ExtensionManager уничтожен.');
    });

    it('should not dispose twice', async () => {
      await extensionManager.dispose();

      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      MockedExtensionRegistry.prototype.deactivateAll = jest.fn();

      await extensionManager.dispose();

      // Should not call deactivateAll again
      expect(MockedExtensionRegistry.prototype.deactivateAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('Integration with ServiceManager', () => {
    it('should use core services API from service manager', () => {
      expect(mockServiceManager.getCoreServicesAPI).toHaveBeenCalled();
    });

    it('should pass send renderer event function correctly', () => {
      // The send renderer event function should be available to extensions
      const sendFn = mockSendRendererEvent;
      sendFn('test-event', { data: 'test' });

      expect(mockSendRendererEvent).toHaveBeenCalledWith('test-event', { data: 'test' });
    });
  });

  describe('Error Recovery', () => {
    it('should continue operation after discovery error', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      
      // First call fails
      MockedExtensionRegistry.prototype.discoverExtensions = jest.fn()
        .mockRejectedValueOnce(new Error('First discovery failed'))
        .mockResolvedValueOnce(undefined);

      // First discovery should fail
      await expect(extensionManager.discoverExtensions()).rejects.toThrow('First discovery failed');

      // Second discovery should succeed
      await expect(extensionManager.discoverExtensions()).resolves.not.toThrow();
    });

    it('should continue operation after activation error', async () => {
      const { ExtensionRegistry } = await import('../../src/main/extensions/extension.registry');
      const MockedExtensionRegistry = ExtensionRegistry as jest.MockedClass<typeof ExtensionRegistry>;
      
      // First call fails
      MockedExtensionRegistry.prototype.activateAll = jest.fn()
        .mockRejectedValueOnce(new Error('First activation failed'))
        .mockResolvedValueOnce(undefined);

      // First activation should not throw but should log error
      await expect(extensionManager.activateAll()).resolves.not.toThrow();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Ошибка при активации расширений:',
        expect.any(Error)
      );

      // Second activation should succeed
      await expect(extensionManager.activateAll()).resolves.not.toThrow();
    });
  });
});
