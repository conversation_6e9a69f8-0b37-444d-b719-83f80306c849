import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { CommandService } from '../../../src/main/services/command.service';
import type { LoggingServiceAPI } from '../../../src/main/services/logging.service';
import type { ContextServiceAPI } from '../../../src/main/services/context.service';
import type { KeybindingServiceAPI } from '../../../src/main/services/keybinding.service';
import type { IpcServiceAPI } from '../../../src/main/services/ipc.service';
import type { ConfigurationServiceAPI } from '../../../src/main/services/configuration.service';
import type { EditorServiceAPI } from '../../../src/main/services/editor.service';
import type { Command } from '../../../src/shared/types/commands';

describe('CommandService', () => {
  let commandService: CommandService;
  let mockLogger: jest.Mocked<LoggingServiceAPI>;
  let mockContextService: jest.Mocked<ContextServiceAPI>;
  let mockKeybindingService: jest.Mocked<KeybindingServiceAPI>;
  let mockIpcService: jest.Mocked<IpcServiceAPI>;
  let mockConfigurationService: jest.Mocked<ConfigurationServiceAPI>;
  let mockEditorService: jest.Mocked<EditorServiceAPI>;

  beforeEach(() => {
    // Create mock services
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      createScopedLogger: jest.fn(() => mockLogger),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<LoggingServiceAPI>;

    mockContextService = {
      evaluate: jest.fn(() => true), // Default to true for context evaluation
      setValue: jest.fn(),
      getValue: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<ContextServiceAPI>;

    mockKeybindingService = {
      getActiveKeybindingsForCommand: jest.fn(() => []),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<KeybindingServiceAPI>;

    mockIpcService = {
      handle: jest.fn(),
      send: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<IpcServiceAPI>;

    mockConfigurationService = {
      get: jest.fn(),
      set: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<ConfigurationServiceAPI>;

    mockEditorService = {
      openEditor: jest.fn(),
      closeEditor: jest.fn(),
      getActiveEditor: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<EditorServiceAPI>;

    commandService = new CommandService(
      mockLogger,
      mockContextService,
      mockKeybindingService,
      mockIpcService,
      mockConfigurationService,
      mockEditorService
    );
  });

  describe('Command Registration', () => {
    it('should register a command successfully', () => {
      const command: Command = {
        id: 'test.command',
        title: 'Test Command',
        category: 'Test',
        handler: jest.fn(),
        when: 'always',
      };

      const disposable = commandService.registerCommand(command);

      expect(disposable).toBeDefined();
      expect(typeof disposable.dispose).toBe('function');
      expect(mockLogger.info).toHaveBeenCalledWith(
        "Registering command 'test.command' (Test Command)"
      );
    });

    it('should warn when registering duplicate command', () => {
      const command: Command = {
        id: 'test.command',
        title: 'Test Command',
        category: 'Test',
        handler: jest.fn(),
        when: 'always',
      };

      commandService.registerCommand(command);
      commandService.registerCommand(command); // Register again

      expect(mockLogger.warn).toHaveBeenCalledWith(
        "Command 'test.command' is already registered. Overwriting."
      );
    });

    it('should dispose command registration', async () => {
      const command: Command = {
        id: 'test.command',
        title: 'Test Command',
        category: 'Test',
        handler: jest.fn(),
        when: 'always',
      };

      const disposable = commandService.registerCommand(command);
      await disposable.dispose();

      expect(mockLogger.info).toHaveBeenCalledWith(
        "Unregistering command 'test.command'"
      );
    });
  });

  describe('Command Execution', () => {
    it('should execute registered command successfully', async () => {
      const mockHandler = jest.fn().mockResolvedValue('command result');
      const command: Command = {
        id: 'test.command',
        title: 'Test Command',
        category: 'Test',
        handler: mockHandler,
        when: 'always',
      };

      commandService.registerCommand(command);
      mockContextService.evaluate.mockReturnValue(true);

      const result = await commandService.executeCommand({
        commandId: 'test.command',
        args: { param: 'value' },
      });

      expect(mockHandler).toHaveBeenCalledWith({ param: 'value' });
      expect(result).toBe('command result');
      expect(mockLogger.info).toHaveBeenCalledWith(
        "Executing command 'test.command' with args:",
        { param: 'value' }
      );
    });

    it('should execute command with no args', async () => {
      const mockHandler = jest.fn().mockResolvedValue('result');
      const command: Command = {
        id: 'test.command',
        title: 'Test Command',
        category: 'Test',
        handler: mockHandler,
        when: 'always',
      };

      commandService.registerCommand(command);

      const result = await commandService.executeCommand({
        commandId: 'test.command',
      });

      expect(mockHandler).toHaveBeenCalledWith({});
      expect(result).toBe('result');
    });

    it('should throw error when command not found', async () => {
      await expect(
        commandService.executeCommand({
          commandId: 'nonexistent.command',
        })
      ).rejects.toThrow("Command 'nonexistent.command' not found.");

      expect(mockLogger.error).toHaveBeenCalledWith(
        "Command 'nonexistent.command' not found."
      );
    });

    it('should throw error when command context is not active', async () => {
      const command: Command = {
        id: 'test.command',
        title: 'Test Command',
        category: 'Test',
        handler: jest.fn(),
        when: 'editorFocus',
      };

      commandService.registerCommand(command);
      mockContextService.evaluate.mockReturnValue(false);

      await expect(
        commandService.executeCommand({
          commandId: 'test.command',
        })
      ).rejects.toThrow("Command 'test.command' is not active in the current context.");

      expect(mockLogger.warn).toHaveBeenCalledWith(
        "Command 'test.command' is not active in the current context ('editorFocus')."
      );
    });

    it('should handle command execution errors', async () => {
      const error = new Error('Command execution failed');
      const mockHandler = jest.fn().mockRejectedValue(error);
      const command: Command = {
        id: 'test.command',
        title: 'Test Command',
        category: 'Test',
        handler: mockHandler,
        when: 'always',
      };

      commandService.registerCommand(command);

      await expect(
        commandService.executeCommand({
          commandId: 'test.command',
        })
      ).rejects.toThrow('Command execution failed');

      expect(mockLogger.error).toHaveBeenCalledWith(
        "Error executing command 'test.command':",
        error
      );
    });
  });

  describe('Command Retrieval', () => {
    it('should return all active commands for palette', () => {
      const command1: Command = {
        id: 'test.command1',
        title: 'Test Command 1',
        category: 'Test',
        handler: jest.fn(),
        when: 'always',
        showInPalette: true,
      };

      const command2: Command = {
        id: 'test.command2',
        title: 'Test Command 2',
        category: 'Test',
        handler: jest.fn(),
        when: 'editorFocus',
        showInPalette: true,
      };

      const command3: Command = {
        id: 'test.command3',
        title: 'Hidden Command',
        category: 'Test',
        handler: jest.fn(),
        when: 'always',
        showInPalette: false,
      };

      commandService.registerCommand(command1);
      commandService.registerCommand(command2);
      commandService.registerCommand(command3);

      // Mock context evaluation
      mockContextService.evaluate.mockImplementation((when) => {
        return when === 'always';
      });

      mockKeybindingService.getActiveKeybindingsForCommand.mockReturnValue(['Ctrl+T']);

      const commands = commandService.getCommands();

      expect(commands).toHaveLength(1); // Only command1 should be returned
      expect(commands[0]).toMatchObject({
        id: 'test.command1',
        title: 'Test Command 1',
        category: 'Test',
        keybindings: ['Ctrl+T'],
      });
      expect(commands[0]).not.toHaveProperty('handler');
      expect(commands[0]).not.toHaveProperty('when');
    });

    it('should get specific command by ID', () => {
      const command: Command = {
        id: 'test.command',
        title: 'Test Command',
        category: 'Test',
        handler: jest.fn(),
        when: 'always',
      };

      commandService.registerCommand(command);

      const retrievedCommand = commandService.getCommand('test.command');

      expect(retrievedCommand).toEqual(command);
    });

    it('should return undefined for non-existent command', () => {
      const retrievedCommand = commandService.getCommand('nonexistent.command');

      expect(retrievedCommand).toBeUndefined();
    });
  });

  describe('Built-in Commands', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await commandService.initialize(mockWindowGetter);
    });

    it('should register built-in commands during initialization', () => {
      const commands = commandService.getCommands();
      
      // Check that some built-in commands are registered
      const commandIds = commands.map(cmd => cmd.id);
      expect(commandIds).toContain('workbench.action.showCommands');
      expect(commandIds).toContain('workbench.action.openSettings');
    });

    it('should execute context.setValue command', async () => {
      await commandService.executeCommand({
        commandId: 'context.setValue',
        args: { key: 'testKey', value: 'testValue' },
      });

      expect(mockContextService.setValue).toHaveBeenCalledWith('testKey', 'testValue');
    });

    it('should execute settings.setValue command', async () => {
      await commandService.executeCommand({
        commandId: 'settings.setValue',
        args: { key: 'testSetting', value: 'testValue' },
      });

      expect(mockConfigurationService.set).toHaveBeenCalledWith('testSetting', 'testValue');
    });

    it('should execute editor.openTab command', async () => {
      await commandService.executeCommand({
        commandId: 'editor.openTab',
        args: { 
          editorId: 'test.editor',
          resourceId: 'resource-123',
          title: 'Test Tab'
        },
      });

      expect(mockEditorService.openEditor).toHaveBeenCalledWith({
        editorId: 'test.editor',
        resourceId: 'resource-123',
        title: 'Test Tab',
      });
    });
  });

  describe('IPC Integration', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await commandService.initialize(mockWindowGetter);
    });

    it('should register IPC handlers during initialization', () => {
      expect(mockIpcService.handle).toHaveBeenCalledWith(
        'commands:execute',
        expect.any(Function)
      );
      expect(mockIpcService.handle).toHaveBeenCalledWith(
        'commands:getAll',
        expect.any(Function)
      );
    });
  });

  describe('Disposal', () => {
    it('should dispose all registered commands', async () => {
      const command1: Command = {
        id: 'test.command1',
        title: 'Test Command 1',
        category: 'Test',
        handler: jest.fn(),
        when: 'always',
      };

      const command2: Command = {
        id: 'test.command2',
        title: 'Test Command 2',
        category: 'Test',
        handler: jest.fn(),
        when: 'always',
      };

      commandService.registerCommand(command1);
      commandService.registerCommand(command2);

      await commandService.dispose();

      expect(mockLogger.info).toHaveBeenCalledWith(
        "Unregistering command 'test.command1'"
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        "Unregistering command 'test.command2'"
      );
    });
  });
});
