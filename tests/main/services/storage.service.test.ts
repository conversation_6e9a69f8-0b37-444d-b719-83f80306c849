import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { StorageService } from '../../../src/main/services/storage.service';
import type { LoggingServiceAPI } from '../../../src/main/services/logging.service';
import type { IpcServiceAPI } from '../../../src/main/services/ipc.service';
import Database from 'better-sqlite3';

// Mock the dependencies
jest.mock('better-sqlite3');
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn(() => '/tmp/test-app-data'),
  },
}));
jest.mock('fs', () => ({
  existsSync: jest.fn(() => true),
  mkdirSync: jest.fn(),
}));

describe('StorageService', () => {
  let storageService: StorageService;
  let mockLogger: jest.Mocked<LoggingServiceAPI>;
  let mockIpcService: jest.Mocked<IpcServiceAPI>;
  let mockDatabase: jest.Mocked<Database>;

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      createScopedLogger: jest.fn(() => mockLogger),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<LoggingServiceAPI>;

    // Create mock IPC service
    mockIpcService = {
      handle: jest.fn(),
      send: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<IpcServiceAPI>;

    // Create mock database
    mockDatabase = {
      prepare: jest.fn(),
      pragma: jest.fn(),
      close: jest.fn(),
    } as unknown as jest.Mocked<Database>;

    // Mock Database constructor
    (Database as jest.MockedClass<typeof Database>).mockImplementation(() => mockDatabase);

    storageService = new StorageService(mockLogger, mockIpcService);
  });

  describe('Initialization', () => {
    it('should initialize database connection successfully', async () => {
      const mockWindowGetter = jest.fn(() => null);

      await storageService.initialize(mockWindowGetter);

      expect(Database).toHaveBeenCalledWith(expect.stringContaining('ai_books_data.sqlite'));
      expect(mockDatabase.pragma).toHaveBeenCalledWith('journal_mode = WAL');
      expect(mockDatabase.pragma).toHaveBeenCalledWith('foreign_keys = ON');
      expect(mockLogger.info).toHaveBeenCalledWith('Database connection opened successfully.');
    });

    it('should handle database initialization errors', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const error = new Error('Database connection failed');
      (Database as jest.MockedClass<typeof Database>).mockImplementation(() => {
        throw error;
      });

      await expect(storageService.initialize(mockWindowGetter)).rejects.toThrow(
        'Failed to initialize database: Database connection failed'
      );

      expect(mockLogger.error).toHaveBeenCalledWith('Failed to initialize database', error);
    });
  });

  describe('Database Operations', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);
    });

    describe('run', () => {
      it('should execute SQL statement successfully', async () => {
        const mockStatement = {
          run: jest.fn(() => ({ changes: 1, lastInsertRowid: 1 })),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.run(
          'INSERT INTO test (name) VALUES (?)',
          'test-name'
        );

        expect(mockDatabase.prepare).toHaveBeenCalledWith('INSERT INTO test (name) VALUES (?)');
        expect(mockStatement.run).toHaveBeenCalledWith('test-name');
        expect(result).toEqual({ changes: 1, lastInsertRowid: 1 });
      });

      it('should handle SQL execution errors', async () => {
        const error = new Error('SQL error');
        const mockStatement = {
          run: jest.fn(() => {
            throw error;
          }),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        await expect(
          storageService.run('INVALID SQL', 'param')
        ).rejects.toThrow('Failed to run statement: SQL error');

        expect(mockLogger.error).toHaveBeenCalledWith(
          'RUN Error: INVALID SQL',
          expect.objectContaining({ params: ['param'], error })
        );
      });
    });

    describe('get', () => {
      it('should retrieve single row successfully', async () => {
        const mockRow = { id: 1, name: 'test' };
        const mockStatement = {
          get: jest.fn(() => mockRow),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.get('SELECT * FROM test WHERE id = ?', 1);

        expect(mockDatabase.prepare).toHaveBeenCalledWith('SELECT * FROM test WHERE id = ?');
        expect(mockStatement.get).toHaveBeenCalledWith(1);
        expect(result).toEqual(mockRow);
      });

      it('should return undefined when no row found', async () => {
        const mockStatement = {
          get: jest.fn(() => undefined),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.get('SELECT * FROM test WHERE id = ?', 999);

        expect(result).toBeUndefined();
      });

      it('should handle query errors', async () => {
        const error = new Error('Query error');
        const mockStatement = {
          get: jest.fn(() => {
            throw error;
          }),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        await expect(
          storageService.get('INVALID SQL', 'param')
        ).rejects.toThrow('Failed to get row: Query error');
      });
    });

    describe('all', () => {
      it('should retrieve all rows successfully', async () => {
        const mockRows = [
          { id: 1, name: 'test1' },
          { id: 2, name: 'test2' },
        ];
        const mockStatement = {
          all: jest.fn(() => mockRows),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.all('SELECT * FROM test');

        expect(mockDatabase.prepare).toHaveBeenCalledWith('SELECT * FROM test');
        expect(mockStatement.all).toHaveBeenCalledWith();
        expect(result).toEqual(mockRows);
      });

      it('should return empty array when no rows found', async () => {
        const mockStatement = {
          all: jest.fn(() => []),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.all('SELECT * FROM empty_table');

        expect(result).toEqual([]);
      });

      it('should handle query errors', async () => {
        const error = new Error('Query error');
        const mockStatement = {
          all: jest.fn(() => {
            throw error;
          }),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        await expect(
          storageService.all('INVALID SQL')
        ).rejects.toThrow('Failed to get rows: Query error');
      });
    });

    describe('transaction', () => {
      it('should execute transaction successfully', async () => {
        const mockTransaction = jest.fn((callback) => callback());
        mockDatabase.transaction = mockTransaction;

        const callback = jest.fn(() => 'result');
        const result = await storageService.transaction(callback);

        expect(mockTransaction).toHaveBeenCalledWith(callback);
        expect(callback).toHaveBeenCalled();
        expect(result).toBe('result');
      });

      it('should handle transaction errors', async () => {
        const error = new Error('Transaction error');
        const mockTransaction = jest.fn(() => {
          throw error;
        });
        mockDatabase.transaction = mockTransaction;

        const callback = jest.fn();

        await expect(
          storageService.transaction(callback)
        ).rejects.toThrow('Failed to execute transaction: Transaction error');

        expect(mockLogger.error).toHaveBeenCalledWith(
          'TRANSACTION Error',
          expect.objectContaining({ error })
        );
      });
    });
  });

  describe('Disposal', () => {
    it('should close database connection on disposal', async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);

      await storageService.dispose();

      expect(mockDatabase.pragma).toHaveBeenCalledWith('wal_checkpoint(RESTART)');
      expect(mockDatabase.close).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('Database connection closed.');
    });

    it('should handle WAL checkpoint errors gracefully', async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);

      const checkpointError = new Error('Checkpoint failed');
      mockDatabase.pragma.mockImplementation((pragma) => {
        if (pragma === 'wal_checkpoint(RESTART)') {
          throw checkpointError;
        }
      });

      await storageService.dispose();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error during WAL checkpoint:',
        checkpointError
      );
      expect(mockDatabase.close).toHaveBeenCalled();
    });

    it('should handle database close errors', async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);

      const closeError = new Error('Close failed');
      mockDatabase.close.mockImplementation(() => {
        throw closeError;
      });

      await storageService.dispose();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error closing database connection:',
        closeError
      );
    });
  });

  describe('Error Handling', () => {
    it('should throw error when database not initialized', async () => {
      // Don't initialize the service
      const mockStatement = {
        run: jest.fn(),
      };
      mockDatabase.prepare.mockReturnValue(mockStatement as any);

      await expect(
        storageService.run('SELECT 1')
      ).rejects.toThrow('Database not initialized. Call initialize() first.');
    });
  });
});
