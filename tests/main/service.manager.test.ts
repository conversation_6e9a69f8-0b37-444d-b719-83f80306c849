import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ServiceManager } from '../../src/main/service.manager';
import type { LoggingServiceAPI } from '../../src/main/services/logging.service';

// Mock all the services
jest.mock('../../src/main/services/storage.service');
jest.mock('../../src/main/services/command.service');
jest.mock('../../src/main/services/view.service');
jest.mock('../../src/main/services/configuration.service');
jest.mock('../../src/main/services/ipc.service');
jest.mock('../../src/main/services/notification.service');
jest.mock('../../src/main/services/dialog.service');
jest.mock('../../src/main/services/context.service');
jest.mock('../../src/main/services/menu.service');
jest.mock('../../src/main/services/editor.service');
jest.mock('../../src/main/services/git.service');
jest.mock('../../src/main/services/ai.service');
jest.mock('../../src/main/services/lifecycle.service');
jest.mock('../../src/main/services/keybinding.service');

describe('ServiceManager', () => {
  let serviceManager: ServiceManager;
  let mockLogger: jest.Mocked<LoggingServiceAPI>;

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      createScopedLogger: jest.fn(() => mockLogger),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<LoggingServiceAPI>;

    serviceManager = new ServiceManager();
  });

  describe('Initialization', () => {
    it('should create service manager with logger', () => {
      expect(serviceManager).toBeDefined();
      expect(serviceManager.logger).toBeDefined();
    });

    it('should initialize all core services', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      // Verify that all services are initialized
      const coreAPI = serviceManager.getCoreServicesAPI();
      expect(coreAPI.commands).toBeDefined();
      expect(coreAPI.views).toBeDefined();
      expect(coreAPI.storage).toBeDefined();
      expect(coreAPI.configuration).toBeDefined();
      expect(coreAPI.ipc).toBeDefined();
      expect(coreAPI.notifications).toBeDefined();
      expect(coreAPI.logger).toBeDefined();
      expect(coreAPI.dialogs).toBeDefined();
      expect(coreAPI.context).toBeDefined();
      expect(coreAPI.menus).toBeDefined();
      expect(coreAPI.editors).toBeDefined();
      expect(coreAPI.git).toBeDefined();
      expect(coreAPI.ai).toBeDefined();
      expect(coreAPI.lifecycle).toBeDefined();
    });

    it('should handle initialization errors gracefully', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      // Mock one service to throw an error during initialization
      const { StorageService } = await import('../../src/main/services/storage.service');
      const MockedStorageService = StorageService as jest.MockedClass<typeof StorageService>;
      MockedStorageService.prototype.initialize = jest.fn().mockRejectedValue(new Error('Storage init failed'));

      await expect(
        serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent)
      ).rejects.toThrow('Storage init failed');
    });
  });

  describe('Service Registration', () => {
    it('should register services in service registry', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      // Verify services are registered in the registry
      const allServices = serviceManager.serviceRegistry.getAllServices();
      expect(allServices.length).toBeGreaterThan(0);
    });

    it('should provide core services API', () => {
      const coreAPI = serviceManager.getCoreServicesAPI();

      expect(coreAPI).toHaveProperty('commands');
      expect(coreAPI).toHaveProperty('views');
      expect(coreAPI).toHaveProperty('storage');
      expect(coreAPI).toHaveProperty('configuration');
      expect(coreAPI).toHaveProperty('ipc');
      expect(coreAPI).toHaveProperty('notifications');
      expect(coreAPI).toHaveProperty('logger');
      expect(coreAPI).toHaveProperty('dialogs');
      expect(coreAPI).toHaveProperty('context');
      expect(coreAPI).toHaveProperty('menus');
      expect(coreAPI).toHaveProperty('editors');
      expect(coreAPI).toHaveProperty('git');
      expect(coreAPI).toHaveProperty('ai');
      expect(coreAPI).toHaveProperty('lifecycle');
    });
  });

  describe('Service Dependencies', () => {
    it('should initialize services in correct dependency order', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      const initializationOrder: string[] = [];

      // Mock all service constructors to track initialization order
      const services = [
        'StorageService',
        'CommandService',
        'ViewService',
        'ConfigurationService',
        'IpcService',
        'NotificationService',
        'DialogService',
        'ContextService',
        'MenuService',
        'EditorService',
        'GitService',
        'AIService',
        'LifecycleService',
        'KeybindingService',
      ];

      for (const serviceName of services) {
        const module = await import(`../../src/main/services/${serviceName.toLowerCase().replace('service', '.service')}`);
        const ServiceClass = module[serviceName] as jest.MockedClass<any>;
        ServiceClass.prototype.initialize = jest.fn().mockImplementation(async function(this: any) {
          initializationOrder.push(serviceName);
        });
      }

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      // Verify that core services are initialized before dependent services
      const storageIndex = initializationOrder.indexOf('StorageService');
      const commandIndex = initializationOrder.indexOf('CommandService');
      const ipcIndex = initializationOrder.indexOf('IpcService');

      expect(storageIndex).toBeLessThan(commandIndex); // Storage should be initialized before Command
      expect(ipcIndex).toBeLessThan(commandIndex); // IPC should be initialized before Command
    });
  });

  describe('Service Lifecycle', () => {
    it('should dispose all services on shutdown', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      // Mock dispose methods
      const coreAPI = serviceManager.getCoreServicesAPI();
      const disposeSpy = jest.spyOn(serviceManager.serviceRegistry, 'dispose');

      await serviceManager.dispose();

      expect(disposeSpy).toHaveBeenCalled();
    });

    it('should handle service disposal errors gracefully', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      // Mock one service to throw an error during disposal
      const { StorageService } = await import('../../src/main/services/storage.service');
      const MockedStorageService = StorageService as jest.MockedClass<typeof StorageService>;
      MockedStorageService.prototype.dispose = jest.fn().mockRejectedValue(new Error('Storage disposal failed'));

      // Should not throw, but should log the error
      await expect(serviceManager.dispose()).resolves.not.toThrow();
    });
  });

  describe('Service Access', () => {
    it('should provide access to individual services', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      expect(serviceManager.commands).toBeDefined();
      expect(serviceManager.views).toBeDefined();
      expect(serviceManager.storage).toBeDefined();
      expect(serviceManager.configuration).toBeDefined();
      expect(serviceManager.ipc).toBeDefined();
      expect(serviceManager.notifications).toBeDefined();
      expect(serviceManager.dialogs).toBeDefined();
      expect(serviceManager.context).toBeDefined();
      expect(serviceManager.menus).toBeDefined();
      expect(serviceManager.editors).toBeDefined();
      expect(serviceManager.git).toBeDefined();
      expect(serviceManager.ai).toBeDefined();
      expect(serviceManager.lifecycle).toBeDefined();
    });

    it('should provide logger access', () => {
      expect(serviceManager.logger).toBeDefined();
      expect(typeof serviceManager.logger.info).toBe('function');
      expect(typeof serviceManager.logger.error).toBe('function');
      expect(typeof serviceManager.logger.warn).toBe('function');
    });
  });

  describe('Re-initialization', () => {
    it('should support re-initialization of services', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      // Initialize once
      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      const firstAPI = serviceManager.getCoreServicesAPI();

      // Re-initialize
      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      const secondAPI = serviceManager.getCoreServicesAPI();

      // Should have new instances
      expect(secondAPI).toBeDefined();
      expect(secondAPI.commands).toBeDefined();
      expect(secondAPI.storage).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing window getter gracefully', async () => {
      const mockSendRendererEvent = jest.fn();

      // Pass null as window getter
      await expect(
        serviceManager.initializeServices(() => null, mockSendRendererEvent)
      ).resolves.not.toThrow();
    });

    it('should handle missing send renderer event function gracefully', async () => {
      const mockWindowGetter = jest.fn(() => null);

      // Pass null as send renderer event
      await expect(
        serviceManager.initializeServices(mockWindowGetter, () => {})
      ).resolves.not.toThrow();
    });
  });
});
