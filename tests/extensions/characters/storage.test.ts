import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { CharacterStorage } from '../../../src/extensions/characters/main/storage';
import type { StorageServiceAPI } from '../../../src/main/services/storage.service';
import type { Character, CharacterEvent, CharacterAttribute } from '../../../src/extensions/characters/shared/types';

// Mock the logger
jest.mock('../../../src/main/services/logging.service', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe('CharacterStorage', () => {
  let characterStorage: CharacterStorage;
  let mockStorageAPI: jest.Mocked<StorageServiceAPI>;

  beforeEach(() => {
    // Create mock storage API
    mockStorageAPI = {
      run: jest.fn(),
      get: jest.fn(),
      all: jest.fn(),
      transaction: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<StorageServiceAPI>;

    characterStorage = new CharacterStorage(mockStorageAPI);
  });

  describe('Schema Management', () => {
    it('should ensure schema creates all required tables', async () => {
      mockStorageAPI.all.mockResolvedValue([]); // No existing table
      mockStorageAPI.transaction.mockImplementation((callback) => {
        callback();
        return Promise.resolve();
      });

      await characterStorage.ensureSchema();

      expect(mockStorageAPI.transaction).toHaveBeenCalled();
      expect(mockStorageAPI.run).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS characters')
      );
      expect(mockStorageAPI.run).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS character_events')
      );
      expect(mockStorageAPI.run).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS character_attributes')
      );
    });

    it('should handle migration when bookId column is missing', async () => {
      // Mock table info without bookId column
      mockStorageAPI.all.mockResolvedValue([
        { name: 'id', type: 'TEXT', notnull: 1, dflt_value: null, pk: 1 },
        { name: 'name', type: 'TEXT', notnull: 1, dflt_value: null, pk: 0 },
      ]);

      mockStorageAPI.transaction.mockImplementation((callback) => {
        callback();
        return Promise.resolve();
      });

      await characterStorage.ensureSchema();

      expect(mockStorageAPI.run).toHaveBeenCalledWith(
        'ALTER TABLE characters ADD COLUMN bookId TEXT'
      );
      expect(mockStorageAPI.run).toHaveBeenCalledWith(
        'ALTER TABLE characters ADD COLUMN role TEXT'
      );
    });
  });

  describe('Character CRUD Operations', () => {
    describe('createCharacter', () => {
      it('should create a character with all fields', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await characterStorage.createCharacter(
          'John Snow',
          'Bastard of Winterfell',
          'book-123',
          'protagonist'
        );

        expect(result).toMatchObject({
          name: 'John Snow',
          description: 'Bastard of Winterfell',
          bookId: 'book-123',
          role: 'protagonist',
        });
        expect(result.id).toMatch(/^char-\d+-[a-z0-9]{6}$/);
        expect(result.createdAt).toBeGreaterThan(0);
        expect(result.updatedAt).toBeGreaterThan(0);

        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'INSERT INTO characters (id, name, description, bookId, role, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?)',
          result.id,
          'John Snow',
          'Bastard of Winterfell',
          'book-123',
          'protagonist',
          result.createdAt,
          result.updatedAt
        );
      });

      it('should create a character with minimal fields', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await characterStorage.createCharacter('Jane Doe');

        expect(result).toMatchObject({
          name: 'Jane Doe',
          description: undefined,
          bookId: undefined,
          role: undefined,
        });
        expect(result.id).toMatch(/^char-\d+-[a-z0-9]{6}$/);
      });
    });

    describe('getAllCharacters', () => {
      it('should return all characters when no bookId provided', async () => {
        const mockCharacters: Character[] = [
          {
            id: 'char-1',
            name: 'John Snow',
            description: 'Bastard of Winterfell',
            bookId: 'book-1',
            role: 'protagonist',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockCharacters);

        const result = await characterStorage.getAllCharacters();

        expect(result).toEqual(mockCharacters);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          'SELECT id, name, description, bookId, role, createdAt, updatedAt FROM characters ORDER BY name ASC'
        );
      });

      it('should filter characters by bookId when provided', async () => {
        const mockCharacters: Character[] = [
          {
            id: 'char-1',
            name: 'John Snow',
            description: 'Bastard of Winterfell',
            bookId: 'book-1',
            role: 'protagonist',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockCharacters);

        const result = await characterStorage.getAllCharacters('book-1');

        expect(result).toEqual(mockCharacters);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          'SELECT id, name, description, bookId, role, createdAt, updatedAt FROM characters WHERE bookId = ? ORDER BY name ASC',
          'book-1'
        );
      });
    });

    describe('getCharacterById', () => {
      it('should return character when found', async () => {
        const mockCharacter: Character = {
          id: 'char-1',
          name: 'John Snow',
          description: 'Bastard of Winterfell',
          bookId: 'book-1',
          role: 'protagonist',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        };
        mockStorageAPI.get.mockResolvedValue(mockCharacter);

        const result = await characterStorage.getCharacterById('char-1');

        expect(result).toEqual(mockCharacter);
        expect(mockStorageAPI.get).toHaveBeenCalledWith(
          'SELECT id, name, description, bookId, role, createdAt, updatedAt FROM characters WHERE id = ?',
          'char-1'
        );
      });

      it('should return null when character not found', async () => {
        mockStorageAPI.get.mockResolvedValue(undefined);

        const result = await characterStorage.getCharacterById('nonexistent');

        expect(result).toBeNull();
      });
    });

    describe('updateCharacter', () => {
      it('should update character fields', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const updateData = {
          name: 'Jon Snow',
          description: 'King in the North',
          role: 'king',
        };

        const result = await characterStorage.updateCharacter('char-1', updateData);

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          expect.stringContaining('UPDATE characters SET'),
          'Jon Snow',
          'King in the North',
          'king',
          expect.any(Number), // updatedAt
          'char-1'
        );
      });

      it('should return false when no fields to update', async () => {
        const result = await characterStorage.updateCharacter('char-1', {});

        expect(result).toBe(false);
        expect(mockStorageAPI.run).not.toHaveBeenCalled();
      });

      it('should return false when no rows affected', async () => {
        const mockRunResult = { changes: 0, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await characterStorage.updateCharacter('char-1', { name: 'New Name' });

        expect(result).toBe(false);
      });
    });

    describe('deleteCharacter', () => {
      it('should delete character successfully', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await characterStorage.deleteCharacter('char-1');

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'DELETE FROM characters WHERE id = ?',
          'char-1'
        );
      });

      it('should return false when character not found', async () => {
        const mockRunResult = { changes: 0, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await characterStorage.deleteCharacter('nonexistent');

        expect(result).toBe(false);
      });
    });
  });

  describe('Character Events', () => {
    describe('createCharacterEvent', () => {
      it('should create a character event', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const eventData = {
          characterId: 'char-1',
          bookId: 'book-1',
          relatedSceneId: 'scene-1',
          timelinePosition: 50,
          eventType: 'Goals' as const,
          description: 'Discovers his true parentage',
          impact: 9,
        };

        const result = await characterStorage.createCharacterEvent(eventData);

        expect(result).toMatchObject({
          characterId: 'char-1',
          bookId: 'book-1',
          relatedSceneId: 'scene-1',
          timelinePosition: 50,
          eventType: 'Goals',
          description: 'Discovers his true parentage',
          impact: 9,
        });
        expect(result.id).toMatch(/^event-\d+-[a-z0-9]{6}$/);
        expect(result.createdAt).toBeGreaterThan(0);
        expect(result.updatedAt).toBeGreaterThan(0);
      });
    });

    describe('getCharacterEvents', () => {
      it('should return events for a character', async () => {
        const mockEvents: CharacterEvent[] = [
          {
            id: 'event-1',
            characterId: 'char-1',
            bookId: 'book-1',
            relatedSceneId: 'scene-1',
            timelinePosition: 25,
            eventType: 'Personality',
            description: 'Shows honor and duty',
            impact: 7,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
          {
            id: 'event-2',
            characterId: 'char-1',
            bookId: 'book-1',
            relatedSceneId: 'scene-2',
            timelinePosition: 75,
            eventType: 'Goals',
            description: 'Discovers true parentage',
            impact: 9,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockEvents);

        const result = await characterStorage.getCharacterEvents('char-1');

        expect(result).toEqual(mockEvents);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          expect.stringContaining('SELECT id, characterId, bookId, relatedSceneId, timelinePosition, eventType, description, impact, createdAt, updatedAt FROM character_events WHERE characterId = ? ORDER BY timelinePosition ASC'),
          'char-1'
        );
      });
    });

    describe('updateCharacterEvent', () => {
      it('should update character event', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const updateData = {
          description: 'Updated description',
          impact: 8,
        };

        const result = await characterStorage.updateCharacterEvent('event-1', updateData);

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          expect.stringContaining('UPDATE character_events SET'),
          'Updated description',
          8,
          expect.any(Number), // updatedAt
          'event-1'
        );
      });
    });

    describe('deleteCharacterEvent', () => {
      it('should delete character event', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await characterStorage.deleteCharacterEvent('event-1');

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'DELETE FROM character_events WHERE id = ?',
          'event-1'
        );
      });
    });
  });

  describe('Character Attributes', () => {
    describe('createCharacterAttribute', () => {
      it('should create a character attribute', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await characterStorage.createCharacterAttribute(
          'char-1',
          'height',
          '6 feet'
        );

        expect(result).toMatchObject({
          characterId: 'char-1',
          attributeKey: 'height',
          attributeValue: '6 feet',
        });
        expect(result.id).toMatch(/^attr-\d+-[a-z0-9]{6}$/);
        expect(result.createdAt).toBeGreaterThan(0);
      });
    });

    describe('getCharacterAttributes', () => {
      it('should return attributes for a character', async () => {
        const mockAttributes: CharacterAttribute[] = [
          {
            id: 'attr-1',
            characterId: 'char-1',
            attributeKey: 'height',
            attributeValue: '6 feet',
            createdAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockAttributes);

        const result = await characterStorage.getCharacterAttributes('char-1');

        expect(result).toEqual(mockAttributes);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          'SELECT id, characterId, attributeKey, attributeValue, createdAt FROM character_attributes WHERE characterId = ? ORDER BY attributeKey ASC',
          'char-1'
        );
      });
    });
  });

  describe('Utility Methods', () => {
    describe('getEventsByScene', () => {
      it('should return events for a specific scene', async () => {
        const mockEvents: CharacterEvent[] = [
          {
            id: 'event-1',
            characterId: 'char-1',
            bookId: 'book-1',
            relatedSceneId: 'scene-1',
            timelinePosition: 25,
            eventType: 'Personality',
            description: 'Shows honor',
            impact: 7,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockEvents);

        const result = await characterStorage.getEventsByScene('scene-1');

        expect(result).toEqual(mockEvents);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          expect.stringContaining('WHERE relatedSceneId = ?'),
          'scene-1'
        );
      });
    });

    describe('getEventsByTimelineRange', () => {
      it('should return events within timeline range', async () => {
        const mockEvents: CharacterEvent[] = [
          {
            id: 'event-1',
            characterId: 'char-1',
            bookId: 'book-1',
            relatedSceneId: 'scene-1',
            timelinePosition: 50,
            eventType: 'Goals',
            description: 'Mid-story event',
            impact: 8,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockEvents);

        const result = await characterStorage.getEventsByTimelineRange('char-1', 25, 75);

        expect(result).toEqual(mockEvents);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          expect.stringContaining('WHERE characterId = ? AND timelinePosition >= ? AND timelinePosition <= ?'),
          'char-1',
          25,
          75
        );
      });
    });

    describe('getCharacterProfile', () => {
      it('should return complete character profile with events and attributes', async () => {
        const mockCharacter: Character = {
          id: 'char-1',
          name: 'John Snow',
          description: 'Bastard of Winterfell',
          bookId: 'book-1',
          role: 'protagonist',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        };

        const mockEvents: CharacterEvent[] = [
          {
            id: 'event-1',
            characterId: 'char-1',
            bookId: 'book-1',
            relatedSceneId: 'scene-1',
            timelinePosition: 50,
            eventType: 'Goals',
            description: 'Discovers parentage',
            impact: 9,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ];

        const mockAttributes: CharacterAttribute[] = [
          {
            id: 'attr-1',
            characterId: 'char-1',
            attributeKey: 'height',
            attributeValue: '6 feet',
            createdAt: Date.now(),
          },
        ];

        mockStorageAPI.get.mockResolvedValue(mockCharacter);
        mockStorageAPI.all
          .mockResolvedValueOnce(mockEvents)
          .mockResolvedValueOnce(mockAttributes);

        const result = await characterStorage.getCharacterProfile('char-1');

        expect(result).toEqual({
          ...mockCharacter,
          events: mockEvents,
          attributes: mockAttributes,
        });
      });

      it('should return null when character not found', async () => {
        mockStorageAPI.get.mockResolvedValue(undefined);

        const result = await characterStorage.getCharacterProfile('nonexistent');

        expect(result).toBeNull();
      });
    });
  });
});
