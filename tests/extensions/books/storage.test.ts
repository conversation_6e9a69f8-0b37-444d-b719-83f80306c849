import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { BookStorage } from '../../../src/extensions/books/main/storage';
import type { StorageServiceAPI } from '../../../src/main/services/storage.service';
import type { SceneService } from '../../../src/extensions/books/main/services/scene.service';
import type { Book, Chapter, Scene } from '../../../src/extensions/books/shared/types';

// Mock the logger
jest.mock('../../../src/main/services/logging.service', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe('BookStorage', () => {
  let bookStorage: BookStorage;
  let mockStorageAPI: jest.Mocked<StorageServiceAPI>;
  let mockSceneService: jest.Mocked<SceneService>;

  beforeEach(() => {
    // Create mock storage API
    mockStorageAPI = {
      run: jest.fn(),
      get: jest.fn(),
      all: jest.fn(),
      transaction: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<StorageServiceAPI>;

    // Create mock scene service
    mockSceneService = {
      initRepoForBook: jest.fn(),
      createSceneFile: jest.fn(),
      deleteSceneFile: jest.fn(),
      moveSceneFile: jest.fn(),
      saveSceneContent: jest.fn(),
      getSceneContent: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<SceneService>;

    bookStorage = new BookStorage(mockStorageAPI, mockSceneService);
  });

  describe('Schema Management', () => {
    it('should ensure schema creates all required tables', async () => {
      mockStorageAPI.transaction.mockImplementation((callback) => {
        callback();
        return Promise.resolve();
      });

      await bookStorage.ensureSchema();

      expect(mockStorageAPI.transaction).toHaveBeenCalled();
      expect(mockStorageAPI.run).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS books')
      );
      expect(mockStorageAPI.run).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS chapters')
      );
      expect(mockStorageAPI.run).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS scenes')
      );
    });
  });

  describe('Book CRUD Operations', () => {
    describe('createBook', () => {
      it('should create a book and initialize git repo', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);
        mockSceneService.initRepoForBook.mockResolvedValue();

        const result = await bookStorage.createBook('My Novel');

        expect(result).toMatchObject({
          title: 'My Novel',
        });
        expect(result.id).toMatch(/^book-\d+-[a-z0-9]{6}$/);
        expect(result.createdAt).toBeGreaterThan(0);

        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'INSERT INTO books (id, title, createdAt) VALUES (?, ?, ?)',
          result.id,
          'My Novel',
          result.createdAt
        );
        expect(mockSceneService.initRepoForBook).toHaveBeenCalledWith(result.id);
      });
    });

    describe('getAllBooks', () => {
      it('should return all books', async () => {
        const mockBooks: Book[] = [
          {
            id: 'book-1',
            title: 'First Novel',
            createdAt: Date.now(),
          },
          {
            id: 'book-2',
            title: 'Second Novel',
            createdAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockBooks);

        const result = await bookStorage.getAllBooks();

        expect(result).toEqual(mockBooks);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          'SELECT id, title, createdAt FROM books ORDER BY createdAt DESC'
        );
      });
    });

    describe('getBookById', () => {
      it('should return book when found', async () => {
        const mockBook: Book = {
          id: 'book-1',
          title: 'My Novel',
          createdAt: Date.now(),
        };
        mockStorageAPI.get.mockResolvedValue(mockBook);

        const result = await bookStorage.getBookById('book-1');

        expect(result).toEqual(mockBook);
        expect(mockStorageAPI.get).toHaveBeenCalledWith(
          'SELECT id, title, createdAt FROM books WHERE id = ?',
          'book-1'
        );
      });

      it('should return null when book not found', async () => {
        mockStorageAPI.get.mockResolvedValue(undefined);

        const result = await bookStorage.getBookById('nonexistent');

        expect(result).toBeNull();
      });
    });

    describe('updateBook', () => {
      it('should update book title', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await bookStorage.updateBook('book-1', { title: 'Updated Title' });

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'UPDATE books SET title = ? WHERE id = ?',
          'Updated Title',
          'book-1'
        );
      });

      it('should return false when no rows affected', async () => {
        const mockRunResult = { changes: 0, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await bookStorage.updateBook('book-1', { title: 'Updated Title' });

        expect(result).toBe(false);
      });
    });

    describe('deleteBook', () => {
      it('should delete book successfully', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await bookStorage.deleteBook('book-1');

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'DELETE FROM books WHERE id = ?',
          'book-1'
        );
      });
    });
  });

  describe('Chapter CRUD Operations', () => {
    describe('createChapter', () => {
      it('should create a chapter', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await bookStorage.createChapter('book-1', 'Chapter 1');

        expect(result).toMatchObject({
          bookId: 'book-1',
          title: 'Chapter 1',
          order: 0,
        });
        expect(result.id).toMatch(/^chapter-\d+-[a-z0-9]{6}$/);
        expect(result.createdAt).toBeGreaterThan(0);

        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'INSERT INTO chapters (id, bookId, title, `order`, createdAt) VALUES (?, ?, ?, ?, ?)',
          result.id,
          'book-1',
          'Chapter 1',
          0,
          result.createdAt
        );
      });

      it('should create chapter with specified order', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await bookStorage.createChapter('book-1', 'Chapter 2', 1);

        expect(result.order).toBe(1);
      });
    });

    describe('getChaptersByBookId', () => {
      it('should return chapters for a book', async () => {
        const mockChapters: Chapter[] = [
          {
            id: 'chapter-1',
            bookId: 'book-1',
            title: 'Chapter 1',
            order: 0,
            createdAt: Date.now(),
          },
          {
            id: 'chapter-2',
            bookId: 'book-1',
            title: 'Chapter 2',
            order: 1,
            createdAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockChapters);

        const result = await bookStorage.getChaptersByBookId('book-1');

        expect(result).toEqual(mockChapters);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          'SELECT id, bookId, title, `order`, createdAt FROM chapters WHERE bookId = ? ORDER BY `order` ASC',
          'book-1'
        );
      });
    });

    describe('updateChapter', () => {
      it('should update chapter', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await bookStorage.updateChapter('chapter-1', { title: 'Updated Chapter' });

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'UPDATE chapters SET title = ? WHERE id = ?',
          'Updated Chapter',
          'chapter-1'
        );
      });
    });

    describe('deleteChapter', () => {
      it('should delete chapter successfully', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await bookStorage.deleteChapter('chapter-1');

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'DELETE FROM chapters WHERE id = ?',
          'chapter-1'
        );
      });
    });

    describe('reorderChapters', () => {
      it('should reorder chapters', async () => {
        mockStorageAPI.transaction.mockImplementation((callback) => {
          callback();
          return Promise.resolve();
        });

        const chapterOrders = [
          { id: 'chapter-1', order: 1 },
          { id: 'chapter-2', order: 0 },
        ];

        await bookStorage.reorderChapters(chapterOrders);

        expect(mockStorageAPI.transaction).toHaveBeenCalled();
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'UPDATE chapters SET `order` = ? WHERE id = ?',
          1,
          'chapter-1'
        );
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'UPDATE chapters SET `order` = ? WHERE id = ?',
          0,
          'chapter-2'
        );
      });
    });
  });

  describe('Scene CRUD Operations', () => {
    describe('createScene', () => {
      it('should create a scene and scene file', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);
        mockSceneService.createSceneFile.mockResolvedValue();

        const result = await bookStorage.createScene('chapter-1', 'Scene 1');

        expect(result).toMatchObject({
          chapterId: 'chapter-1',
          title: 'Scene 1',
          order: 0,
        });
        expect(result.id).toMatch(/^scene-\d+-[a-z0-9]{6}$/);
        expect(result.filePath).toMatch(/\.md$/);

        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'INSERT INTO scenes (id, chapterId, title, `order`, filePath, createdAt) VALUES (?, ?, ?, ?, ?, ?)',
          result.id,
          'chapter-1',
          'Scene 1',
          0,
          result.filePath,
          result.createdAt
        );
        expect(mockSceneService.createSceneFile).toHaveBeenCalledWith(result.filePath, 'Scene 1');
      });
    });

    describe('getScenesByChapterId', () => {
      it('should return scenes for a chapter', async () => {
        const mockScenes: Scene[] = [
          {
            id: 'scene-1',
            chapterId: 'chapter-1',
            title: 'Scene 1',
            order: 0,
            filePath: 'scenes/scene-1.md',
            createdAt: Date.now(),
          },
        ];
        mockStorageAPI.all.mockResolvedValue(mockScenes);

        const result = await bookStorage.getScenesByChapterId('chapter-1');

        expect(result).toEqual(mockScenes);
        expect(mockStorageAPI.all).toHaveBeenCalledWith(
          'SELECT id, chapterId, title, `order`, filePath, createdAt FROM scenes WHERE chapterId = ? ORDER BY `order` ASC',
          'chapter-1'
        );
      });
    });

    describe('updateScene', () => {
      it('should update scene', async () => {
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);

        const result = await bookStorage.updateScene('scene-1', { title: 'Updated Scene' });

        expect(result).toBe(true);
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'UPDATE scenes SET title = ? WHERE id = ?',
          'Updated Scene',
          'scene-1'
        );
      });
    });

    describe('deleteScene', () => {
      it('should delete scene and scene file', async () => {
        const mockScene: Scene = {
          id: 'scene-1',
          chapterId: 'chapter-1',
          title: 'Scene 1',
          order: 0,
          filePath: 'scenes/scene-1.md',
          createdAt: Date.now(),
        };
        mockStorageAPI.get.mockResolvedValue(mockScene);
        
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);
        mockSceneService.deleteSceneFile.mockResolvedValue();

        const result = await bookStorage.deleteScene('scene-1');

        expect(result).toBe(true);
        expect(mockSceneService.deleteSceneFile).toHaveBeenCalledWith('scenes/scene-1.md');
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'DELETE FROM scenes WHERE id = ?',
          'scene-1'
        );
      });

      it('should handle scene not found', async () => {
        mockStorageAPI.get.mockResolvedValue(undefined);

        const result = await bookStorage.deleteScene('nonexistent');

        expect(result).toBe(false);
        expect(mockSceneService.deleteSceneFile).not.toHaveBeenCalled();
      });
    });

    describe('moveScene', () => {
      it('should move scene to different chapter', async () => {
        const mockScene: Scene = {
          id: 'scene-1',
          chapterId: 'chapter-1',
          title: 'Scene 1',
          order: 0,
          filePath: 'scenes/scene-1.md',
          createdAt: Date.now(),
        };
        mockStorageAPI.get.mockResolvedValue(mockScene);
        
        const mockRunResult = { changes: 1, lastInsertRowid: 1 };
        mockStorageAPI.run.mockResolvedValue(mockRunResult);
        mockSceneService.moveSceneFile.mockResolvedValue();

        const result = await bookStorage.moveScene('scene-1', 'chapter-2', 1);

        expect(result).toBe(true);
        expect(mockSceneService.moveSceneFile).toHaveBeenCalledWith(
          'scenes/scene-1.md',
          expect.stringMatching(/scenes\/scene-1-\d+\.md/)
        );
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'UPDATE scenes SET chapterId = ?, `order` = ?, filePath = ? WHERE id = ?',
          'chapter-2',
          1,
          expect.stringMatching(/scenes\/scene-1-\d+\.md/),
          'scene-1'
        );
      });
    });

    describe('reorderScenes', () => {
      it('should reorder scenes', async () => {
        mockStorageAPI.transaction.mockImplementation((callback) => {
          callback();
          return Promise.resolve();
        });

        const sceneOrders = [
          { id: 'scene-1', order: 1 },
          { id: 'scene-2', order: 0 },
        ];

        await bookStorage.reorderScenes(sceneOrders);

        expect(mockStorageAPI.transaction).toHaveBeenCalled();
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'UPDATE scenes SET `order` = ? WHERE id = ?',
          1,
          'scene-1'
        );
        expect(mockStorageAPI.run).toHaveBeenCalledWith(
          'UPDATE scenes SET `order` = ? WHERE id = ?',
          0,
          'scene-2'
        );
      });
    });
  });

  describe('Complex Operations', () => {
    describe('getBookStructure', () => {
      it('should return complete book structure', async () => {
        const mockBook: Book = {
          id: 'book-1',
          title: 'My Novel',
          createdAt: Date.now(),
        };

        const mockChapters: Chapter[] = [
          {
            id: 'chapter-1',
            bookId: 'book-1',
            title: 'Chapter 1',
            order: 0,
            createdAt: Date.now(),
          },
        ];

        const mockScenes: Scene[] = [
          {
            id: 'scene-1',
            chapterId: 'chapter-1',
            title: 'Scene 1',
            order: 0,
            filePath: 'scenes/scene-1.md',
            createdAt: Date.now(),
          },
        ];

        mockStorageAPI.get.mockResolvedValue(mockBook);
        mockStorageAPI.all
          .mockResolvedValueOnce(mockChapters)
          .mockResolvedValueOnce(mockScenes);

        const result = await bookStorage.getBookStructure('book-1');

        expect(result).toEqual({
          book: mockBook,
          chapters: [
            {
              ...mockChapters[0],
              scenes: mockScenes,
            },
          ],
        });
      });

      it('should return null when book not found', async () => {
        mockStorageAPI.get.mockResolvedValue(undefined);

        const result = await bookStorage.getBookStructure('nonexistent');

        expect(result).toBeNull();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const error = new Error('Database error');
      mockStorageAPI.run.mockRejectedValue(error);

      await expect(bookStorage.createBook('Test Book')).rejects.toThrow('Database error');
    });

    it('should handle scene service errors', async () => {
      const mockRunResult = { changes: 1, lastInsertRowid: 1 };
      mockStorageAPI.run.mockResolvedValue(mockRunResult);
      
      const error = new Error('Scene service error');
      mockSceneService.createSceneFile.mockRejectedValue(error);

      await expect(bookStorage.createScene('chapter-1', 'Scene 1')).rejects.toThrow('Scene service error');
    });
  });
});
