import { describe, it, expect, beforeEach, jest } from '@jest/globals';

// Mock better-sqlite3 before importing the storage service
jest.mock('better-sqlite3', () => {
  const mockStatement = {
    run: jest.fn(() => ({ changes: 1, lastInsertRowid: 1 })),
    get: jest.fn(() => ({ id: 1, name: 'test' })),
    all: jest.fn(() => [{ id: 1, name: 'test' }]),
  };

  const mockDatabase = jest.fn(() => ({
    prepare: jest.fn(() => mockStatement),
    pragma: jest.fn(),
    close: jest.fn(),
    transaction: jest.fn((callback) => callback()),
  }));

  return mockDatabase;
});

// Mock electron
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn(() => '/tmp/test-app-data'),
  },
}));

// Mock fs
jest.mock('fs', () => ({
  existsSync: jest.fn(() => true),
  mkdirSync: jest.fn(),
}));

describe('Storage Service Basic Tests', () => {
  let StorageService: any;
  let mockLogger: any;
  let mockIpcService: any;

  beforeEach(async () => {
    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      createScopedLogger: jest.fn(() => mockLogger),
      initialize: jest.fn(),
      dispose: jest.fn(),
    };

    // Create mock IPC service
    mockIpcService = {
      handle: jest.fn(),
      send: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    };

    // Import the StorageService after mocks are set up
    const storageModule = await import('../src/main/services/storage.service');
    StorageService = storageModule.StorageService;
  });

  it('should create storage service instance', () => {
    const storageService = new StorageService(mockLogger, mockIpcService);
    expect(storageService).toBeDefined();
  });

  it('should initialize storage service', async () => {
    const storageService = new StorageService(mockLogger, mockIpcService);
    const mockWindowGetter = jest.fn(() => null);

    await storageService.initialize(mockWindowGetter);

    expect(mockLogger.info).toHaveBeenCalledWith('Database connection opened successfully.');
  });

  it('should execute run operations', async () => {
    const storageService = new StorageService(mockLogger, mockIpcService);
    const mockWindowGetter = jest.fn(() => null);

    await storageService.initialize(mockWindowGetter);

    const result = await storageService.run('INSERT INTO test (name) VALUES (?)', 'test-name');

    expect(result).toEqual({ changes: 1, lastInsertRowid: 1 });
  });

  it('should execute get operations', async () => {
    const storageService = new StorageService(mockLogger, mockIpcService);
    const mockWindowGetter = jest.fn(() => null);

    await storageService.initialize(mockWindowGetter);

    const result = await storageService.get('SELECT * FROM test WHERE id = ?', 1);

    expect(result).toEqual({ id: 1, name: 'test' });
  });

  it('should execute all operations', async () => {
    const storageService = new StorageService(mockLogger, mockIpcService);
    const mockWindowGetter = jest.fn(() => null);

    await storageService.initialize(mockWindowGetter);

    const result = await storageService.all('SELECT * FROM test');

    expect(result).toEqual([{ id: 1, name: 'test' }]);
  });

  it('should handle errors gracefully', async () => {
    const storageService = new StorageService(mockLogger, mockIpcService);

    // Try to run without initialization
    await expect(
      storageService.run('SELECT 1')
    ).rejects.toThrow('Database not initialized. Call initialize() first.');
  });

  it('should dispose properly', async () => {
    const storageService = new StorageService(mockLogger, mockIpcService);
    const mockWindowGetter = jest.fn(() => null);

    await storageService.initialize(mockWindowGetter);
    await storageService.dispose();

    expect(mockLogger.info).toHaveBeenCalledWith('Database connection closed.');
  });
});
