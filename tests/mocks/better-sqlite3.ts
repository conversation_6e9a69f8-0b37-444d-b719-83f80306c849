import { jest } from '@jest/globals';

// Mock better-sqlite3 Database
export default class Database {
  private statements = new Map<string, MockStatement>();
  private isOpen = true;
  
  constructor(path: string, options?: any) {
    // Mock constructor
  }
  
  prepare(sql: string): MockStatement {
    if (!this.statements.has(sql)) {
      this.statements.set(sql, new MockStatement(sql));
    }
    return this.statements.get(sql)!;
  }
  
  pragma = jest.fn();
  close = jest.fn(() => {
    this.isOpen = false;
  });
  
  // Helper method for tests to set up mock data
  __setMockData(sql: string, data: any[]) {
    const stmt = this.prepare(sql);
    stmt.__setMockData(data);
  }
}

class MockStatement {
  private mockData: any[] = [];
  
  constructor(private sql: string) {}
  
  run = jest.fn((...params: any[]) => ({
    changes: 1,
    lastInsertRowid: 1,
  }));
  
  get = jest.fn((...params: any[]) => {
    return this.mockData[0] || null;
  });
  
  all = jest.fn((...params: any[]) => {
    return this.mockData;
  });
  
  // Helper method for tests
  __setMockData(data: any[]) {
    this.mockData = data;
  }
}
