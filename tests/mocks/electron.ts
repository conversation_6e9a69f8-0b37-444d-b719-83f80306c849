import { jest } from '@jest/globals';

// Mock Electron app
export const app = {
  getPath: jest.fn((name: string) => {
    switch (name) {
      case 'userData':
        return '/tmp/test-app-data';
      default:
        return '/tmp';
    }
  }),
  on: jest.fn(),
  quit: jest.fn(),
  exit: jest.fn(),
};

// Mock BrowserWindow
export class BrowserWindow {
  static getAllWindows = jest.fn(() => []);
  
  constructor(options?: any) {
    // Mock constructor
  }
  
  loadFile = jest.fn();
  loadURL = jest.fn();
  show = jest.fn();
  hide = jest.fn();
  close = jest.fn();
  destroy = jest.fn();
  on = jest.fn();
  webContents = {
    send: jest.fn(),
    on: jest.fn(),
  };
}

// Mock ipcMain
export const ipcMain = {
  handle: jest.fn(),
  on: jest.fn(),
  removeHandler: jest.fn(),
  removeAllListeners: jest.fn(),
};

// Mock ipcRenderer
export const ipcRenderer = {
  invoke: jest.fn(),
  send: jest.fn(),
  on: jest.fn(),
  removeAllListeners: jest.fn(),
};
