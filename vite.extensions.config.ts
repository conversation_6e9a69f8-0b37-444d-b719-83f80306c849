import { defineConfig } from 'vite';
import { builtinModules } from 'node:module';
import path from "node:path";

export default defineConfig({
  resolve: {
    conditions: ['node'],
    alias: {
        '@main': path.resolve(__dirname, './src/main'),
        '@renderer': path.resolve(__dirname, './src/renderer'),
        '@shared': path.resolve(__dirname, './src/shared'),
        '@': path.resolve(__dirname, './src'),
        '@extensions': path.resolve(__dirname, './src/extensions'),
        '@services': path.resolve(__dirname, './src/main/services'),
    },
  },
  mode: process.env.MODE === 'development' ? 'development' : 'production',
  build: {
    rollupOptions: {
      // Внешние зависимости: electron и встроенные модули Node.js
      external: [
        'electron',
        ...builtinModules,
        ...builtinModules.map((m) => `node:${m}`),
      ],
      output: {
        // Указываем выходную директорию ОТНОСИТЕЛЬНО папки сборки Main процесса (.vite/build/main)
        // Так как target: 'main' в forge.config.ts, VitePlugin будет использовать outDir из vite.main.config.ts как базу.
        // Мы хотим результат в .vite/build/main/extensions/
        dir: 'extensions', // Результат будет в .vite/build/main/extensions/
        // Сохраняем структуру директорий относительно 'src/extensions'
        entryFileNames: (chunkInfo) => {
          const relativePath = path.relative('src/extensions', chunkInfo.facadeModuleId || '');
          return relativePath.replace(/\.ts$/, '.js'); // -> books/main/index.js
        },
        format: 'es',
      },
    },
    minify: process.env.MODE !== 'development',
    emptyOutDir: false, // НЕ очищаем директорию, т.к. сюда же собирается main и preload
  },
});
