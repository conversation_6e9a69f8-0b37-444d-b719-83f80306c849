// Test character creation and IPC event flow
console.log('=== Testing Character Creation IPC Event Flow ===');

// Check if we can access the character store state
if (window.electronAPI) {
  console.log('✓ Electron API is available');
  
  // Test creating a character directly via command
  const testCharacterCreation = async () => {
    try {
      console.log('1. About to execute character creation command...');
      
      const result = await window.electronAPI.invoke('core:commands.execute', {
        payload: {
          commandId: 'book-ide.characters:commands.createCharacter',
          args: {}
        }
      });
      
      console.log('2. Character creation command result:', result);
      console.log('✓ Character creation command completed');
      
    } catch (error) {
      console.error('✗ Error executing character creation command:', error);
    }
  };
  
  // Add a button to test character creation
  const button = document.createElement('button');
  button.textContent = 'Test Character Creation';
  button.style.position = 'fixed';
  button.style.top = '10px';
  button.style.right = '10px';
  button.style.zIndex = '9999';
  button.style.padding = '10px';
  button.style.background = '#007acc';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';
  
  button.addEventListener('click', testCharacterCreation);
  document.body.appendChild(button);
  
  console.log('✓ Test button added to page');
  
} else {
  console.error('✗ Electron API not available');
}
