// Debug script to test character creation
const { app, BrowserWindow, ipcMain } = require('electron');

// Listen for all IPC events to debug
const originalEmit = ipcMain.emit;
ipcMain.emit = function(event, ...args) {
  if (event.includes('character') || event.includes('Character')) {
    console.log(`[DEBUG] IPC Main Event: ${event}`, args);
  }
  return originalEmit.call(this, event, ...args);
};

// Override webContents.send to monitor events being sent back to renderer
const originalSend = BrowserWindow.prototype.send;
BrowserWindow.prototype.send = function(channel, ...args) {
  if (channel.includes('character') || channel.includes('Character')) {
    console.log(`[DEBUG] Sending to Renderer: ${channel}`, args);
  }
  return originalSend.call(this, channel, ...args);
};

console.log('[DEBUG] Character creation debugging enabled');
