# Обзор Архитектуры AI-Books IDE

Этот документ описывает высокоуровневую архитектуру и основные компоненты приложения AI-Books IDE, построенного с использованием Electron и React, с заимствованием концепций из VS Code.

## 1. Основные Принципы

- **Многопроцессная архитектура Electron:**
  - **Main Process:** Управление жизненным циклом, окнами, нативными API, фоновыми задачами, доступ к ресурсам (БД, ФС). Содержит основные сервисы.
  - **Renderer Process:** Отображение UI (React), взаимодействие с пользователем.
- **Разделение Ответственности:** UI (Renderer) vs Логика/Данные (Main).
- **Асинхронное Взаимодействие:** IPC (`invoke`/`send`) через Preload скрипт.
- **Модульность через "Расширения":** Основные функциональные блоки приложения (управление книгами, персонажами, миром и т.д.) реализуются как изолированные "внутренние расширения", каждое со своей логикой и UI, управляемые ядром приложения.
- **Расширяемость:** Компоненты, сервисы, система команд, точки расширения (`contribution points`) для расширений, с потенциалом для будущей **внешней системы плагинов**.
- **Управление Состоянием:** Глобальное состояние UI в Renderer с помощью Zustand. Настройки в Main с помощью `electron-store`.
- **Хранение Данных:** Используется **гибридный подход**: контент сцен хранится в Markdown файлах (версионируется через Git), а структурированные данные (метаданные книг, персонажи, мир, заметки и т.д.) хранятся в базе данных SQLite (управляется `StorageService`).

## 2. Структура Проекта (Основные директории)

- `src/main/`: Код **ядра** Main процесса.
  - `main.ts`: Точка входа, инициализация ядра и расширений.
  - `core/`: (Предлагается) Сервисы ядра (Storage, Settings, Logging, CommandService, ViewService, ExtensionRegistry и т.д.).
  - `ipc/`: Основные обработчики IPC ядра.
- `src/renderer/`: Код **ядра** Renderer процесса (React).
  - `index.tsx`: Точка входа React.
  - `core/`: (Предлагается) Компоненты ядра UI (Workbench, Sidebar, Editor, Panel и т.д.), сервисы Renderer (NotificationService, SnippetService), глобальное состояние (Zustand stores).
- `src/extensions/`: **Высокоуровневая директория для всех расширений.**
  - `characters/`: Пример расширения "Управление Персонажами".
    - `main/`: Backend-часть расширения.
      - `index.ts`: Точка входа (активация, деактивация, регистрация).
      - `commands.ts`: Команды расширения.
      - `ipcHandlers.ts`: Обработчики IPC расширения.
      - `characterService.ts`: Специфичная логика.
    - `renderer/`: Frontend-часть расширения.
      - `components/`: React-компоненты расширения (виды, диалоги).
      - `hooks/`: React-хуки расширения.
      - `state.ts`: Локальное состояние расширения (если нужно).
    - `shared/`: Общие типы и утилиты для расширения.
      - `types.ts`: Типы данных расширения.
  - `world-building/`: Другое расширение...
  - `...`
- `src/preload/`: Preload скрипт (`contextBridge`), предоставляющий API ядра и, возможно, расширений.
- `src/shared/`: Общие типы и утилиты **для всего приложения**.
- `docs/`: Документация.
- `themes/`: (Предлагается) Директория для файлов тем оформления.

## 3. Основные Компоненты и Сервисы

- **Workbench (Renderer):** Основной каркас UI приложения, включающий:
  - **Activity Bar:** Вертикальная панель для переключения основных видов Sidebar.
  - **Sidebar:** Боковая панель, отображающая различные виды (структура книги, поиск, персонажи, мир и т.д.).
  - **Editor Group:** Основная область для редактирования текста (сцен) с поддержкой вкладок. Использует редактор Lexical.
  - **(Будущее) Разделение Редакторов (Split View):** Планируется реализовать возможность разделения области редактора на несколько групп (вертикально или горизонтально), как в VS Code. Это позволит одновременно просматривать и редактировать несколько файлов или открывать связанные инструменты (например, заметки, профиль персонажа) рядом с текстом. Реализация потребует значительных изменений в макете Workbench, управлении состоянием (`workbenchStore`) и компоненте `EditorGroup`.
  - **Panel:** Нижняя панель для вспомогательных видов (проблемы, вывод, консоль и т.д.).
  - **Status Bar:** Нижняя строка состояния для отображения контекстной информации.
- **CommandPalette (Renderer):** Компонент UI для быстрого поиска и вызова команд приложения.
- **InputDialog (Renderer):** Стандартный диалог для запроса простого текстового ввода у пользователя.
- **Zustand Stores (Renderer):** Набор хранилищ для управления состоянием UI в Renderer процессе (например, `workbenchStore` для состояния панелей и активной книги, `editorStore` для состояния редактора, `notificationStore` для уведомлений).
- **Command Registry/Service (Main):** Система в Main процессе для регистрации и выполнения команд. Команды могут быть вызваны из Command Palette, меню, горячих клавиш. Включает логику для определения доступности команд в текущем контексте (`when` clause).
- **StorageService (Main):** Сервис в Main процессе, отвечающий за чтение и запись **структурированных данных** приложения (метаданные книг, персонажи, мир, заметки и т.д.) в локальную базу данных SQLite. Предоставляет CRUD операции и базовый механизм истории/снимков для этих данных через IPC.
- **GitService (Main):** (Как описано в [`version-control.md`](./version-control.md)) Сервис для управления Git-репозиторием, версионирующим **файлы сцен (Markdown)**.
- **SettingsService (Main):** Сервис в Main процессе для управления настройками приложения. Использует `electron-store`.
- **LoggingService (Main):** Сервис в Main процессе для централизованного логирования событий приложения (ошибки, информация, отладка).
- **IPC Handlers (Main):** Набор функций **ядра** в Main процессе, которые обрабатывают основные асинхронные запросы (`invoke`) от Renderer. Расширения регистрируют свои специфичные обработчики через `context.ipc.handle('publisher.extensionName:command', ...)`.
- **Preload Script:** Скрипт, выполняющийся в контексте Renderer. Использует `contextBridge` для безопасного предоставления API из Main процесса в изолированный мир Renderer. Предоставляет унифицированный метод для вызова именованных IPC каналов (например, `window.electronAPI.invoke('publisher.extensionName:command', args)`).

## 4. Ключевые Архитектурные Концепции (Адаптация VS Code)

### 4.1. Система Расширений

Приложение использует систему внутренних расширений для обеспечения модульности и управляемого добавления функционала. Основные системы (управление книгами, персонажами, миром и т.д.) реализованы как изолированные расширения, взаимодействующие через ядро.

Каждое расширение имеет точку входа `main/index.ts`, которая экспортирует функцию `activate(context)` для регистрации своих вкладов (команд, видов, редакторов, обработчиков IPC и т.д.) в ядре приложения. Расширения также могут предоставлять API для взаимодействия друг с другом.

Подробное описание архитектуры системы расширений, точек вклада и механизма взаимодействия находится в документе: **[Архитектура Системы Расширений](./extension-system.md)**.

### 4.2. Система Команд

- **Цель:** Централизованное управление всеми действиями в приложении.
- **Подход:**
  - ✅ **Базовый реестр и UI:** Уже реализованы (`commands/index.ts`, `CommandPalette`).
  - **Улучшения:**
    - **Регистрация:** Реализовать `CommandService`, который будет принимать регистрацию команд из разных расширений (через `contribution points`).
    - **Контекст (`when`):** Добавить свойство `when` к интерфейсу `Command`. `CommandService` будет предоставлять метод для проверки активности команды на основе текущего контекста приложения (например, `activeView`, `selectedItemType`, `editorHasFocus`). Контекст может управляться через Zustand store.
    - **Горячие клавиши:** Создать `KeybindingService`, который будет слушать события клавиатуры, определять контекст и вызывать соответствующую команду через `CommandService`. Конфигурация горячих клавиш может быть частью настроек (`keybindings` contribution point).
    - **Меню:** Использовать `CommandService` для построения главного меню Electron и контекстных меню (`menus` contribution point).

### 4.3. Система Уведомлений

- **Цель:** Заменить `alert()`, предоставлять обратную связь пользователю.
- **Подход:**
  - Создать компонент `NotificationArea` (например, в углу Workbench).
  - Создать стор Zustand (`useNotificationStore`) для хранения списка активных уведомлений (id, message, type, duration, actions).
  - Создать сервис/функции в Renderer (`notificationService.showInfo(...)`) для добавления уведомлений в стор.
  - Для уведомлений из Main: использовать IPC-событие `show-notification`, которое будет вызывать `notificationService` в Renderer.

### 4.4. Контекстные Меню

- **Цель:** Быстрый доступ к релевантным командам.
- **Подход:**
  - Использовать API Electron `Menu` в Main процессе.
  - Создать IPC-канал `show-context-menu`.
  - В Renderer: обработчик `onContextMenu` на элементах UI (например, в `Sidebar`) отправляет `invoke('show-context-menu', { contextType: 'chapter', itemId: '...' })`.
  - В Main: обработчик получает контекст, запрашивает у `CommandService` список команд, активных в данном контексте (через `menus` contribution point), строит меню (`Menu.buildFromTemplate`) и показывает его (`menu.popup`). Выбор пункта меню вызывает выполнение соответствующей команды (возможно, через другой IPC-вызов `execute-command`).

### 4.5. Управление Состоянием Рабочей Области

- **Цель:** Сохранение состояния UI между сессиями.
- **Подход:**
  - Использовать `SettingsService` (`electron-store`) для хранения:
    - Списка ID открытых вкладок редактора.
    - ID активной вкладки.
    - ID выбранной книги.
    - Размера/положения окна (`BrowserWindow.getBounds()`).
    - Состояния панелей Sidebar/Panel (ширина, видимость, активный вид).
  - При запуске приложения (`createWindow`, `Workbench` mount) читать эти значения и восстанавливать состояние UI.
  - При изменении состояния (закрытие вкладки, смена активной вкладки/книги, изменение размера окна) сохранять новые значения (возможно, с debounce).

### 4.6. Панель Проблем/Диагностики (Problems Panel)

- **Цель:** Централизованное отображение ошибок, предупреждений и информации от систем анализа (Narrative Server, Style Server).
- **Подход:**
  - Использовать существующую `Panel` (нижняя панель).
  - Создать новый вид (`view`) для `Panel`, например, `ProblemsView`.
  - Зарегистрировать этот вид через `ViewService` (или аналогичный механизм регистрации видов) через точку вклада `views`.
  - Сервисы анализа в Main Process (зарегистрированные через `analysisProviders`) отправляют диагностические сообщения через IPC (`send('diagnostics-update', diagnostics)`).
  - `ProblemsView` подписывается на это событие и отображает список проблем, позволяя переходить к соответствующему месту в тексте.

### 4.7. Запуск Задач (Task Runner)

- **Цель:** Автоматизация рутинных писательских задач (генерация PDF, проверка консистентности, бэкап).
- **Подход:**
  - Создать `TaskService` в Main Process.
  - Определить формат описания задач (например, в `package.json` или специальном файле `.ai-books/tasks.json`).
  - Реализовать точку вклада `tasks` для регистрации задач из расширений.
  - `TaskService` предоставляет API для получения списка задач и их запуска (`runTask(taskId)`).
  - Запуск задачи может включать выполнение скриптов, вызов внутренних API или команд ОС (`child_process`).
  - UI для выбора и запуска задач может быть интегрирован в Command Palette или отдельный вид.

### 4.8. Фрагменты (Snippets)

- **Цель:** Ускорение ввода часто используемых текстовых шаблонов.
- **Подход:**
  - Определить формат для сниппетов (например, JSON, похожий на формат VS Code).
  - Реализовать `SnippetService` в Renderer, который загружает сниппеты (возможно, через IPC из Main, где они могут быть частью настроек или файлов проекта).
  - Интегрировать `SnippetService` с редактором Lexical для предложения и вставки сниппетов по ключевому слову/префиксу.
  - Реализовать точку вклада `snippets` для добавления сниппетов из расширений.

### 4.9. Веб-представления (Webviews)

- **Цель:** Рендеринг сложного UI или интеграция веб-контента (графы связей, карты, предпросмотр).
- **Подход:**
  - Использовать тег `<webview>` Electron или `<iframe>` внутри React-компонентов.
  - Обеспечить строгую изоляцию и безопасность (`sandbox`, `nodeIntegration=false`, `contextIsolation=true`).
  - Коммуникация между Renderer и Webview через `postMessage` и Preload скрипты для Webview.
  - Использовать для специфических задач, где стандартные компоненты React недостаточны (например, интеграция с библиотеками визуализации графов, предпросмотр EPUB).

### 4.10. Поддержка Тем Оформления (Theming)

- **Цель:** Кастомизация внешнего вида приложения.
- **Подход:**
  - Использовать CSS-переменные для всех ключевых цветов, шрифтов и размеров UI.
  - Определить формат файла темы (например, JSON, определяющий значения CSS-переменных).
  - `SettingsService` хранит ID активной темы.
  - Реализовать `ThemeService` (в Main или Renderer), который загружает темы.
  - При старте и при смене темы в настройках, `ThemeService` загружает соответствующий файл темы (возможно, из директории `themes/` или зарегистрированный через `themes` contribution point) и применяет CSS-переменные к корневому элементу (`document.documentElement`).

### 4.11. Внешняя Система Расширений (Future Potential)

- **Цель:** Позволить сторонним разработчикам расширять функциональность приложения аналогично внутренним расширениям.
- **Подход (Будущее):**
  - **API Расширений:** Формализовать и стабилизировать API, предоставляемое ядром (`context` в `activate`) и, возможно, некоторыми ключевыми внутренними расширениями. Это API будет доступно внешним расширениям.
  - **Extension Host Process:** Реализовать отдельный процесс Node.js (`Extension Host`) для безопасного выполнения кода внешних расширений, изолируя их от ядра и друг от друга (аналогично VS Code). Взаимодействие между Extension Host и Main процессом ядра будет происходить через IPC.
  - **Механизм Загрузки:** Создать механизм для обнаружения, загрузки, активации (вызова `activate`) и управления жизненным циклом внешних расширений (установка, обновление, включение/выключение).
  - **Манифест Расширения:** Определить формат манифеста (например, `package.json`), где внешние расширения будут декларировать свои метаданные, точки вклада (`contributes`) и события активации (`activationEvents`), аналогично VS Code.
  - **(Опционально) Marketplace:** Создать платформу для публикации, поиска и установки внешних расширений.
  - **Межплагиновое Взаимодействие:** Внешние плагины смогут взаимодействовать друг с другом и с внутренними расширениями через тот же механизм экспорта/получения API, что и внутренние расширения, но с учетом асинхронности и IPC между Extension Host и Main процессом.

## 5. Потоки Данных (Примеры)

- **Загрузка книг:** `Workbench` (mount) -> `useWorkbenchStore.loadBooks` -> `window.electronAPI.getBooks` -> `ipcMain('get-books')` -> `StorageService.getBooks` -> `DB` -> возврат данных -> `useWorkbenchStore._setBooks` -> обновление UI.
- **Создание главы (через Command Palette):** `CommandPalette` (выбор) -> `CommandService.executeCommand('book.createChapter')` -> `command.handler` -> `showInputDialog` -> `InputDialog` (confirm) -> `window.electronAPI.createChapter` -> `ipcMain('create-chapter')` -> `StorageService.createChapter` -> `DB` -> `mainWindow.webContents.send('structure-updated')` -> `Workbench` (подписка) -> `useWorkbenchStore.loadStructure` -> обновление UI.
- **Вызов команды по горячей клавише:** `Workbench` (keydown) -> `KeybindingService` (match) -> `CommandService.executeCommand(id)` -> `command.handler` -> ...
- **Показ диагностики:** `AnalysisService` (Main) -> `send('diagnostics-update', diagnostics)` -> `ProblemsView` (Renderer) -> `updateState(diagnostics)` -> обновление UI.
