# Архитектура Контекстного Автодополнения (IntelliSense)

Этот документ описывает архитектуру системы контекстного автодополнения (IntelliSense) в AI-Books IDE, предназначенной для помощи пользователю при вводе текста путем предложения релевантных вариантов завершения.

## 1. Цели

- Предлагать пользователю варианты завершения по мере набора текста (например, имена персонажей, локаций, термины мира, команды, сниппеты).
- Основывать предложения на текущем контексте (тип документа, позиция курсора, введенный текст).
- Интегрироваться с другими системами, такими как "Серверы Анализа" и "Система Фрагментов", для получения данных для предложений.
- Обеспечить быстрый отклик и ненавязчивый пользовательский интерфейс.
- Позволить расширениям регистрировать собственные провайдеры автодополнения.

## 2. Основные Компоненты

### 2.1. `CompletionProvider` (Интерфейс и Реализации)

- **Ответственность:** Предоставление списка предложений для автодополнения в определенном контексте. Реализуется ядром и расширениями.
- **Интерфейс (`CompletionProvider`):**
  - `id: string`: Уникальный идентификатор провайдера (например, `ai-books.character-completion`, `core.snippet-completion`).
  - `triggerCharacters?: string[]`: Символы, которые могут инициировать запрос автодополнения (например, `@` для персонажей, `:` для команд).
  - `provideCompletions(documentUri: string, position: Position, context: CompletionContext): Promise<CompletionItem[] | CompletionList | null>`: Основной метод, возвращающий список предложений.
- **`CompletionContext`:** Содержит информацию о причине вызова (триггерный символ, ручной вызов) и текущем токене/слове.
- **`CompletionItem` (Интерфейс, аналогично LSP):**
  - `label: string`: Текст, отображаемый в списке предложений.
  - `kind?: CompletionItemKind`: Тип предложения (Текст, Функция, Переменная, Файл, Сниппет, Персонаж, Локация и т.д.) для отображения иконки.
  - `detail?: string`: Дополнительная информация (например, тип персонажа).
  - `documentation?: string | MarkupContent`: Документация/описание.
  - `insertText?: string`: Текст, который будет вставлен (если отличается от `label`).
  - `range?: Range`: Диапазон текста, который будет заменен при вставке.
  - `filterText?: string`: Текст для фильтрации/поиска.
  - `sortText?: string`: Текст для сортировки.
  - `command?: Command`: Команда, выполняемая после вставки (например, для запуска режима сниппета).
- **Реализации:**
  - **Ядро:**
    - `SnippetCompletionProvider`: Предлагает сниппеты из `SnippetService`.
    - `WordCompletionProvider`: Предлагает слова из текущего документа.
  - **Расширения:**
    - `CharacterCompletionProvider`: Предлагает имена персонажей (возможно, после `@`).
    - `LocationCompletionProvider`: Предлагает названия локаций.
    - `CommandCompletionProvider`: Предлагает команды (возможно, после `/` или другого символа).

### 2.2. `CompletionService` (Ядро, Main или Renderer Process)

- **Ответственность:** Координация запросов и агрегация результатов от различных `CompletionProvider`.
  - **Вариант 1 (Main Process):**
    - Регистрирует `CompletionProvider` (через точку вклада `completionProviders`).
    - Получает запросы на автодополнение от Renderer через IPC (`invoke('completion:get', ...)`).
    - Определяет релевантные провайдеры на основе контекста (URI, позиция, триггер).
    - Вызывает `provideCompletions` у выбранных провайдеров.
    - Агрегирует, сортирует и фильтрует результаты.
    - Возвращает итоговый список `CompletionItem[]` в Renderer.
  - **Вариант 2 (Renderer Process):**
    - Регистрирует `CompletionProvider` (если они могут быть реализованы в Renderer).
    - Напрямую взаимодействует с редактором.
    - Может запрашивать данные у Main процесса через IPC, если провайдеру нужны данные из Main (например, список персонажей из `StorageService`).
    - **Преимущество:** Меньше задержек IPC для простых провайдеров (сниппеты, слова).
    - **Недостаток:** Более сложная архитектура, если провайдерам нужен доступ к данным Main.
- **Рекомендация:** Начать с **Варианта 1 (Main Process)** для централизации логики и доступа к данным, оптимизируя позже при необходимости.

### 2.3. Интеграция с Редактором (Lexical, Renderer Process)

- **Ответственность:** Инициирование запросов автодополнения и отображение UI.
- **Реализация:**
  - Плагин для Lexical, отслеживающий ввод пользователя и позицию курсора.
  - При вводе текста или триггерного символа (или при ручном вызове, например, `Ctrl+Space`):
    - Отправляет запрос в `CompletionService` (через IPC, если сервис в Main) с URI документа, позицией и контекстом (`invoke('completion:get', ...)`).
    - Получает список `CompletionItem[]`.
    - Отображает UI предложений (выпадающий список) с `label`, `kind` (иконка), `detail`.
    - Фильтрует список по мере дальнейшего ввода пользователя (может выполняться на стороне Renderer или через повторные запросы к сервису).
    - При выборе пользователем предложения:
      - Вставляет `insertText` или `label` в редактор, заменяя текст в `range` (если указан).
      - Выполняет `command` (если указан), например, для активации режима сниппета.

## 3. Процесс Работы (Вариант с Сервисом в Main)

1.  **Регистрация (Main):** Расширения регистрируют свои `CompletionProvider` через `context.completion.registerProvider(...)` (предполагаемый API).
2.  **Ввод (Renderer):** Пользователь набирает текст или триггерный символ в редакторе Lexical.
3.  **Запрос (Renderer -> Main):** Плагин редактора отправляет `invoke('completion:get', { uri, position, context })`.
4.  **Координация (Main):** `CompletionService` получает запрос, находит релевантные `CompletionProvider`.
5.  **Делегирование (Main):** `CompletionService` вызывает `provideCompletions` у каждого релевантного провайдера.
6.  **Получение Данных (Main):** Провайдеры (например, `CharacterCompletionProvider`) могут запрашивать данные у других сервисов ядра (`StorageService`, `ContextService`).
7.  **Агрегация (Main):** `CompletionService` собирает, фильтрует и сортирует результаты от всех провайдеров.
8.  **Ответ (Main -> Renderer):** `CompletionService` возвращает итоговый список `CompletionItem[]`.
9.  **Отображение (Renderer):** Плагин редактора получает список и отображает UI предложений.
10. **Вставка (Renderer):** Пользователь выбирает предложение, редактор выполняет вставку и связанные действия.

## 4. Преимущества

- **Улучшение UX:** Значительно ускоряет ввод и уменьшает количество ошибок.
- **Контекстуальность:** Предложения релевантны текущей задаче пользователя.
- **Расширяемость:** Новые типы автодополнения легко добавляются через провайдеры.
- **Интеграция:** Естественным образом интегрируется с другими системами (анализ, сниппеты).
