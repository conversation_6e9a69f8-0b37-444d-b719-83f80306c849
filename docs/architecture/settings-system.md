# Архитектура Системы Настроек AI-Books

Этот документ описывает архитектуру системы управления настройками в AI-Books IDE, разработанную для обеспечения
гибкости, расширяемости и удобного пользовательского интерфейса, вдохновленного VS Code.

## 1. Цели

- Предоставить централизованный механизм для управления настройками приложения.
- Позволить "внутренним расширениям" (основным модулям приложения) декларировать и регистрировать собственные настройки.
- Обеспечить UI для просмотра, поиска и изменения настроек пользователем.
- Поддерживать различные типы данных (строки, числа, булевы значения, перечисления) и базовую валидацию.
- Учитывать области видимости настроек (на данный момент - только "Пользователь").
- Уведомлять компоненты приложения об изменениях настроек для динамического обновления.

## 2. Основные Компоненты

### 2.1. `SettingsService` (Main Process)

- **Расположение:** `src/main/settings.service.ts`
- **Ответственность:** Является ядром системы настроек.
    - Хранит **декларации** всех зарегистрированных настроек (метаданные: тип, описание, значение по умолчанию и т.д.) в
      памяти (`Map<string, SettingDeclaration>`).
    - Хранит **значения**, установленные пользователем, используя `electron-store` (или аналогичный персистентный
      механизм, например, в `settings.json`).
    - Предоставляет API для регистрации деклараций, чтения эффективных значений (пользовательское или дефолтное) и
      записи пользовательских значений.
    - Выполняет валидацию типа при записи значений.
    - Уведомляет подписчиков об изменениях настроек.
- **Ключевое API (`SettingsServiceAPI`):**
    - `registerSetting(declaration: SettingDeclaration): Disposable`: Регистрирует декларацию настройки. Вызывается
      расширениями через `ExtensionContext`. Возвращает `Disposable` для отмены регистрации.
    - `getSettingValue<T>(key: string): T`: Возвращает текущее _эффективное_ значение настройки (сначала ищет в
      пользовательских настройках, затем возвращает значение по умолчанию из декларации).
    - `setSettingValue(key: string, value: any): Promise<void>`: Устанавливает пользовательское значение настройки,
      валидирует тип, сохраняет в `electron-store` и уведомляет подписчиков.
    - `getAllSettingsDeclarations(): SettingDeclaration[]`: Возвращает массив всех зарегистрированных деклараций (
      используется для построения UI).
    - `getAllSettingValues(): Record<string, any>`: Возвращает объект со всеми _установленными пользователем_
      значениями.
    - `onDidChangeSetting(listener: (e: { key: string; value: any }) => void): Disposable`: Подписка на изменение
      конкретной настройки.
    - `onDidChangeSettings(listener: () => void): Disposable`: Подписка на любые изменения настроек.

### 2.2. `SettingDeclaration` (Интерфейс)

- **Расположение:** `src/shared/types/settings.ts` (или аналогичное общее место)
- **Ответственность:** Определяет структуру метаданных, которые расширение предоставляет при регистрации настройки.
- **Поля:**
    - `id: string`: Уникальный идентификатор в формате `extensionId.category.settingName` (например,
      `books.editor.fontSize`, `workbench.theme`).
    - `description: string`: Краткое описание настройки, отображаемое в UI.
    - `type: 'string' | 'number' | 'boolean' | 'enum' | 'array' | 'object'`: Тип данных. Определяет элемент управления в
      UI и правила валидации.
    - `scope: 'user'`: Область видимости (пока только 'user').
    - `default: any`: Значение по умолчанию.
    - `enum?: string[]`: Массив допустимых строковых значений (для `type: 'enum'`).
    - `enumDescriptions?: string[]`: Описания для значений `enum` (опционально).
    - `minimum?: number`, `maximum?: number`: Ограничения для `type: 'number'`.
    - `markdownDescription?: string`: Более подробное описание (опционально).

### 2.3. `ExtensionContext` (Main Process)

- **Расположение:** `src/main/extension-registry.ts`
- **Ответственность:** Предоставляет API ядра расширениям во время их активации.
- **Интеграция:** Содержит поле `settings: SettingsServiceAPI`, через которое расширения вызывают `registerSetting`.
    - Пример: `context.settings.registerSetting({ id: 'myExt.feature.enabled', type: 'boolean', default: true, ... });`

### 2.4. IPC Каналы

- **Ответственность:** Обеспечивают связь между `SettingsService` (Main) и UI Настроек (Renderer).
- **Каналы:**
    - **`invoke('settings:getDeclarationsAndValues')`:** Renderer запрашивает все декларации и текущие пользовательские
      значения. Main возвращает `{ declarations: SettingDeclaration[], values: Record<string, any> }`.
    - **`invoke('settings:setValue', { key: string, value: any })`:** Renderer отправляет запрос на изменение значения
      настройки. Main обрабатывает через `SettingsService`.
    - **`on('settings:updated', (event, { key: string, value: any }) => ...)`:** Main уведомляет Renderer об изменении
      настройки (например, если она была изменена другим процессом или программно).

### 2.5. `SettingsEditor` (Компонент React, Renderer)

- **Расположение:** `src/renderer/components/SettingsEditor/SettingsEditor.tsx` (пример)
- **Ответственность:** Предоставляет пользовательский интерфейс для просмотра и редактирования настроек.
- **Реализация:**
    - Открывается как вкладка в основной области редактора (требует регистрации через `EditorService` и команду
      `workbench.action.openSettings`).
    - Использует `useEffect` для вызова `invoke('settings:getDeclarationsAndValues')` при монтировании.
    - Хранит полученные декларации и значения в своем состоянии.
    - Группирует настройки по префиксу ID (например, "Workbench", "Books Editor", "Character Management").
    - Для каждой настройки рендерит:
        - Название (из `id`).
        - Описание (`description`, `markdownDescription`).
        - Соответствующий элемент управления (`<input type="text">`, `<input type="number">`, `<input type="checkbox">`,
          `<select>`) на основе `declaration.type` и `declaration.enum`.
        - Текущее эффективное значение (пользовательское или дефолтное).
        - Индикацию, если значение отличается от дефолтного.
    - Предоставляет поле для поиска/фильтрации настроек по ID или описанию.
    - При изменении значения пользователем вызывает `invoke('settings:setValue', ...)`.
    - Использует `useEffect` для подписки на событие `on('settings:updated', ...)` и обновления отображаемых значений.

## 3. Процесс Работы (Workflow)

1. **Запуск (Main):** `SettingsService` загружает пользовательские `settings.json`. `ExtensionRegistry` активирует
   расширения.
2. **Регистрация (Main):** Каждое расширение вызывает `context.settings.registerSetting()` для своих настроек.
   `SettingsService` сохраняет декларации.
3. **Открытие UI (Renderer):** Пользователь открывает редактор настроек.
4. **Загрузка UI (Renderer -> Main -> Renderer):** `SettingsEditor` запрашивает (`settings:getDeclarationsAndValues`) и
   получает декларации и пользовательские значения.
5. **Отображение (Renderer):** `SettingsEditor` рендерит UI, вычисляя эффективные значения.
6. **Изменение (Renderer -> Main -> Renderer):** Пользователь меняет настройку -> `SettingsEditor` отправляет (
   `settings:setValue`) -> `SettingsService` валидирует, сохраняет, отправляет событие (`settings:updated`) ->
   `SettingsEditor` обновляет UI.
7. **Чтение (Любой компонент):** Компонент запрашивает значение (`get-setting` или `settingsService.getSettingValue`) ->
   `SettingsService` возвращает эффективное значение.

## 4. Преимущества

- **Модульность:** Расширения полностью отвечают за свои настройки.
- **Расширяемость:** Легко добавлять новые настройки без изменения ядра.
- **Централизация:** Единый сервис (`SettingsService`) управляет всеми настройками.
- **Удобство:** Предоставляет пользователю знакомый UI для управления конфигурацией.
- **Надежность:** Валидация типов и четкое разделение Main/Renderer.
