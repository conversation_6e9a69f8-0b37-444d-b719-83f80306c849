# Техническая Архитектура: Book Management System

Этот документ описывает техническую реализацию и интеграцию системы управления книгами (`Book Management System`) в рамках общей архитектуры AI-Books IDE, основанной на **гибридном подходе к хранению данных**.

## 1. Реализация как Расширение

- **ID Расширения:** `ai-books.books` (предполагаемое).
- **Структура:** Следует стандартной структуре расширений, описанной в [`extension-system.md`](./extension-system.md), с директориями `main/`, `renderer/`, `shared/`.
- **Активация:** Функция `activate` в `src/extensions/books/main/index.ts` регистрирует все необходимые компоненты (команды, виды, редакторы, обработчики IPC) через `ExtensionContext`.

## 2. Интеграция с Сервисами Ядра

- **`StorageService`:** Используется для хранения **структурированных данных** книги: метаданные книги, секций, глав, сцен (включая порядок и путь к файлу контента сцены), а также для реализации **внутренней истории/снимков** этих метаданных.
- **Файловая Система:** Используется для хранения **контента сцен** в виде отдельных **Markdown файлов**.
- **`GitService` ([`version-control.md`](./version-control.md)):** Используется для **версионирования файлов сцен (Markdown)**.
- **`CommandService`:** Регистрирует команды для действий со структурой книги (создать главу, удалить сцену, переместить элемент - операции над данными в `StorageService` и файлами на диске) и других функций управления книгами.
- **`ViewService`:** Регистрирует:
  - Основной вид структуры книги (например, `BookStructureView`) для отображения в `Sidebar` (читает данные из `StorageService`).
  - Возможно, другие вспомогательные виды.
- **`EditorService`:** Регистрирует:
  - Основной редактор сцен (например, `SceneEditor` на базе Lexical) как стандартный тип редактора для **Markdown файлов сцен**.
  - Возможно, специализированные редакторы для метаданных книги, главы или секции (работают с данными из `StorageService`).
- **`SettingsService`:** Расширение может регистрировать специфичные для управления книгами настройки (например, стандартное имя файла сцены).
- **`IpcService`:** Регистрирует обработчики (`context.ipc.handle('ai-books.books:...')`) для запросов от UI (Renderer), связанных с CRUD операциями над структурой книги (в `StorageService`), получением/сохранением контента сцен (чтение/запись Markdown файлов) и т.д.
- **`ContextService`:** Используется для определения контекста (например, `bookIsOpen`, `activeEditorIsScene`), который может влиять на доступность команд или видов.
- **`NotificationService`:** Используется для информирования пользователя о результатах операций.
- **`SearchService` ([`search-replace.md`](./search-replace.md)):** Используется для поиска по содержимому **файлов сцен (Markdown)** и, возможно, по метаданным в `StorageService`.
- **`AnalysisService` ([`analysis-servers.md`](./analysis-servers.md)):** Может использоваться для анализа структуры книги (из `StorageService`) или контента сцен (из файлов). Расширение может предоставлять `AnalysisProvider`.

## 3. Ключевые Компоненты Расширения (`ai-books.books`)

### 3.1. Backend (`main/`)

- **`index.ts`:** Точка входа, активация, регистрация компонентов в ядре.
- **`bookStructureService.ts` (или аналогичный):** Логика для управления иерархией книги (CRUD операции, перемещение), взаимодействующая со **`StorageService`**.
- **`sceneService.ts` (или аналогичный):** Логика для работы с **файлами сцен Markdown** (чтение, запись, создание, удаление), взаимодействующая с файловой системой.
- **`commands.ts`:** Реализация обработчиков команд, зарегистрированных в `CommandService`.
- **`ipcHandlers.ts`:** Реализация обработчиков IPC, зарегистрированных в `IpcService`.
- **(Опционально) `analysisProvider.ts`:** Реализация `AnalysisProvider` для анализа структуры или контента.

### 3.2. Frontend (`renderer/`)

- **`views/BookStructureView.view.tsx`:** Компонент React для отображения иерархии книги в `Sidebar`. Взаимодействует с backend через IPC для получения данных **из `StorageService`** и выполнения команд. Реализует drag-and-drop.
- **`editors/SceneEditor.editor.tsx`:** Компонент React, оборачивающий текстовый редактор (Lexical) для редактирования **файлов сцен Markdown**. Загружает и сохраняет контент через IPC (вызывая `sceneService` в backend). Интегрируется с `IntelliSense`, `Snippets`, `Analysis` через API редактора и соответствующие сервисы.
- **(Опционально) Другие виды и редакторы:** Для планирования, метаданных и т.д. (работают с данными из `StorageService`).

## 4. Нерешенные Вопросы / Будущие Компоненты

- **`TemplateService`:** Необходимо спроектировать и реализовать сервис и точку вклада для поддержки шаблонов книг, глав, сцен (работающих как с БД, так и с файлами).
- **`TimelineService`:** Может потребоваться для интеграции с функциями планирования и отслеживания событий (связывая Git-коммиты и историю БД).
- **Синхронизация Истории:** Необходимо продумать механизм связи между Git-коммитами сцен и снимками метаданных в `StorageService` для создания единого таймлайна.

## 5. Взаимодействие с Другими Расширениями

- **`ai-books.characters`, `ai-books.world`:** Получает данные (например, для отображения упоминаний в редакторе или связывания сцен с локациями) через их экспортируемое API (`context.extensions.getApi(...)`). Данные персонажей/мира хранятся в `StorageService`.
- **`ai-books.goals`:** Предоставляет информацию о структуре (из `StorageService`) и прогрессе (количество слов из файлов сцен) для системы управления целями.
- **`ai-books.collaboration`:** Интегрируется для отображения комментариев, статусов блокировки и т.д. (требует проектирования, особенно для синхронизации как файлов, так и данных БД).
