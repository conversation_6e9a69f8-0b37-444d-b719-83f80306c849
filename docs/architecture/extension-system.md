# Архитектура Системы Расширений AI-Books

Этот документ описывает систему внутренних расширений, точки вклада и механизм взаимодействия между расширениями в
AI-Books IDE.

## 1. Система Внутренних Расширений

- **Цель:** Обеспечить модульность и управляемое добавление функционала. Основные системы приложения (управление
  книгами, персонажами, миром, целями и т.д.) реализуются как такие "расширения", изолированные друг от друга, но
  взаимодействующие через ядро.
- **Подход:**
    - Определить **точки расширения (`contribution points`)**, куда модули расширений могут регистрировать свой
      функционал. Примеры:
        - `commands`: Команды для Command Palette, меню, горячих клавиш. Команда может включать стандартное сочетание
          клавиш (`keybinding`) и флаг видимости в палитре (`showInPalette`). (См. `ICommand` в `command.service.ts`).
        - `views`: Компоненты для Sidebar (например, `CharacterListView`), Panel (например, `ProblemsView`) или Status
          Bar (например, `WordCountStatusBarItem`).
        - `editors`: Пользовательские редакторы для основной области (`EditorGroup`).
        - `viewContainers`: Иконки в Activity Bar для активации основных видов расширений (только для
          `location: 'sidebar'`).
        - `settings`: Декларативные настройки расширения, управляемые через `SettingsService`. (См. `SettingDeclaration`
          в `shared/types/settings.ts`).
        - `menus`: Пункты для главного и контекстных меню, связанные с командами расширения.
          // - `keybindings`: Прямая регистрация keybindings убрана, стандартные привязки указываются при регистрации
          команды. Пользовательские привязки - TBD.
        - `analysisProviders`: Провайдеры для системы анализа текста и данных ([
          `analysis-servers.md`](./analysis-servers.md)).
        - `completionProviders`: Провайдеры для системы автодополнения ([`intellisense.md`](./intellisense.md)).
        - `outlineProviders`: Провайдеры для генерации структуры документа ([`outline-view.md`](./outline-view.md)).
        - `scmProviders`: Провайдеры для систем контроля версий (например, Git) ([
          `version-control.md`](./version-control.md)).
        - `tasks`: Статические определения задач для Task Runner ([`task-runner.md`](./task-runner.md)). (Провайдеры
          задач регистрируются программно).
        - `snippets`: Наборы фрагментов ([`snippets-system.md`](./snippets-system.md)).
        - `themes`: Темы оформления ([`theming.md`](./theming.md)).
          // - `keybindings`: (Убрано, определяется в `commands`).
          // - `templates`: (Будущее, для `TemplateService`).
    - Каждое расширение должно иметь уникальный идентификатор в формате `publisher.name` (например,
      `ai-books.characters`, где `ai-books` - издатель для внутренних расширений).
    - Каждая **основная функциональность** приложения (например, управление персонажами, миром, задачами) реализуется
      как отдельное "расширение" в директории `src/extensions/`.
    - Расширение инкапсулирует свою логику и UI в поддиректориях `main/` и `renderer/` внутри своей папки (
      `src/extensions/my-extension/`).
    - Точка входа backend-части расширения (`src/extensions/my-extension/main/index.ts`) экспортирует функцию
      `activate(context)` для регистрации вкладов и, опционально, функцию `deactivate()` для очистки ресурсов.
    - **Активация:** При старте приложения **ядро** (в Main процессе, например, `ExtensionRegistry` в `src/main/`)
      загружает **backend-модули** всех _внутренних_ расширений из `src/extensions/*/main/index.ts` и вызывает их
      функции `activate`, передавая контекст с сервисами ядра. (Примечание: В VS Code используется ленивая активация по
      событиям (`activationEvents`), что может быть реализовано в будущем, особенно для внешних плагинов, для
      оптимизации времени запуска).
    - **Деактивация:** При завершении работы приложения ядро вызывает функцию `deactivate()` каждого активного
      расширения (если она экспортирована) для освобождения ресурсов (например, отписки от событий, закрытия
      соединений).
    - UI-компоненты расширений (`src/extensions/my-extension/renderer/...`) динамически загружаются и отображаются ядром
      UI (`src/renderer/core/`) в Renderer процессе по мере необходимости (например, при активации вида или редактора).

### 1.1. Структура и Интеграция Расширения

Чтобы обеспечить единообразие и управляемость, каждое "внутреннее расширение" (например, `characters`) следует
определенной структуре и процессу интеграции:

**1. Структура директорий:**

```
src/
└── extensions/              # Директория для всех расширений
    └── characters/          # Директория расширения
        ├── main/            # Backend-часть (Main Process)
        │   ├── index.ts     # Точка входа (активация, деактивация, регистрация)
        │   ├── commands.ts  # Команды расширения
        │   ├── ipcHandlers.ts # Обработчики IPC расширения
        │   └── service.ts   # Специфичная логика (опционально)
        ├── renderer/        # Frontend-часть (Renderer Process)
        │   ├── components/  # Общие React-компоненты расширения
        │   ├── views/       # Компоненты для Sidebar/Panel (*.view.tsx)
        │   ├── editors/     # Компоненты для EditorGroup (*.editor.tsx)
        │   ├── hooks/       # React-хуки
        │   └── state.ts     # Локальное состояние (опционально)
        └── shared/          # Общие элементы для расширения
            └── types.ts     # Типы данных расширения
```

**2. Backend-часть (`src/extensions/my-extension/main/index.ts`):**

- **Активация:** Экспортирует функцию `activate(context)`, которая вызывается ядром (`src/main/ExtensionRegistry`) при
  старте (или по событию активации в будущем). Эта функция **должна возвращать объект API** (или `Promise`,
  разрешающийся в API), который расширение предоставляет другим. Если API не предоставляется, возвращается `undefined`
  или пустой объект.
- **Деактивация (Опционально):** Экспортирует функцию `deactivate()`, которая вызывается ядром при выключении приложения
  или деактивации расширения. Используется для очистки ресурсов (отписки от событий, закрытия соединений и т.д.).
- **Контекст (`context`):** Объект, предоставляемый ядром, содержащий:
    - Ссылки на API основных сервисов ядра (`commands`, `views`, `editors`, `storage`, `settings`, `ipc`,
      `notifications`, `logger`, `dialogs`, `context`, `menus`, `analysis?`, `completion?`, `outline?`, `tasks?`,
      `theme?` и т.д. - состав API зависит от реализации ядра).
    - Метод для получения API других расширений:
      `context.extensions.getApi<T>(extensionId: string): Promise<T | undefined>`.
    - Коллекцию `subscriptions: Disposable[]`, куда **следует** добавлять все созданные Disposable-объекты (результаты
      регистрации команд, видов, редакторов, настроек, провайдеров, слушателей событий, обработчиков IPC и т.д.). Это
      позволяет ядру автоматически очищать ресурсы расширения при его деактивации.
- **Регистрация Вкладов:** Внутри `activate` расширение использует API из `context` для регистрации своих вкладов.
  Возвращаемые Disposable-объекты **должны** добавляться в `context.subscriptions`.
    -
    `context.subscriptions.push(context.commands.registerCommand({ id: '...', title: '...', handler: ..., keybinding: 'Cmd+K', showInPalette: true }));` (
    Регистрация команды со стандартной привязкой).
    -
    `context.subscriptions.push(context.settings.registerSetting({ id: '...', label: '...', description: '...', type: '...', default: ... }));` (
    Регистрация настройки).
    - `context.subscriptions.push(context.views.registerView(...));`
    - `context.subscriptions.push(context.editors.registerEditorProvider(...));`
    - `context.subscriptions.push(context.menus.registerMenuItems(...));`
    - `context.subscriptions.push(context.ipc.handle('my-extension:myAction', ...));` (Регистрация специфичных IPC
      обработчиков **с префиксом ID расширения**).
    - `context.subscriptions.push(context.analysis?.registerAnalysisProvider(...));` // Пример регистрации провайдера
    - `context.subscriptions.push(context.completion?.registerCompletionProvider(...));` // Пример
    - `context.subscriptions.push(context.outline?.registerOutlineProvider(...));` // Пример
    - `context.subscriptions.push(context.tasks?.registerTaskProvider(...));` // Пример
- **Логика:** Может импортировать и использовать собственную логику (`service.ts`) или взаимодействовать с сервисами
  ядра и API других расширений через `context`.

**3. Frontend-часть (`src/extensions/my-extension/renderer/`):**

- **UI Компоненты:** Предоставляет React-компоненты для видов (`views/*.view.tsx`) и редакторов (
  `editors/*.editor.tsx`). Может содержать и другие общие компоненты (`components/`).
- **Взаимодействие с Backend:** Использует API, предоставленное через Preload скрипт (например,
  `window.electronAPI.invoke('ai-books.characters:getCharacter', id)`), для вызова **именованных** IPC-обработчиков,
  зарегистрированных backend-частью расширения. Preload скрипт обеспечивает унифицированный и безопасный доступ к IPC.
- **Состояние UI:** Может использовать глобальный Zustand store ядра (`src/renderer/core/state`) или иметь свой
  собственный локальный стор/слайс (`state.ts`).
- **Стили:** Компоненты стилизуются с использованием выбранной системы (например, CSS Modules).

**4. Процесс Интеграции:**

- **Загрузка Ядра (Main):** При старте `src/main/main.ts` инициализирует сервисы ядра (`src/main/`).
- **Загрузка Расширений (Main):** `ExtensionRegistry` ядра сканирует директорию `src/extensions/`, импортирует
  `main/index.ts` каждого расширения и вызывает его функцию `activate(context)`, передавая контекст с API ядра.
  Сохраняет возвращенное API и Disposable-объекты из `context.subscriptions`.
- **Регистрация (Main):** Каждое расширение регистрирует свои команды, виды, редакторы, обработчики IPC и т.д. в
  соответствующих сервисах ядра через `context`.
- **Запуск UI (Renderer):** Renderer процесс запускается (`src/renderer/index.tsx`).
- **Отображение UI Ядра (Renderer):** Компоненты ядра UI (`src/renderer/core/components/`) отображаются, получая
  информацию о зарегистрированных видах и контейнерах от ядра Main процесса через IPC при инициализации.
- **Динамическая Загрузка UI Расширений (Renderer):** Когда пользователь активирует вид или редактор, принадлежащий
  расширению, ядро UI (например, `Sidebar` или `ActiveEditorArea`) динамически импортирует соответствующий
  React-компонент из `src/extensions/my-extension/renderer/views/` или `src/extensions/my-extension/renderer/editors/` и
  монтирует его (используя `import.meta.glob` и `React.lazy`).
- **Взаимодействие:** Пользователь взаимодействует с UI расширения. Компоненты расширения вызывают **именованные**
  IPC-запросы к backend-части расширения через Preload API. Backend-часть расширения обрабатывает запросы,
  взаимодействует с сервисами ядра и возвращает результат или инициирует обновление UI через события (`send`).
- **Деактивация (Main):** При завершении работы приложения, `ExtensionRegistry` проходит по всем активным расширениям и
  вызывает их функции `deactivate()` (если они есть), а также очищает все ресурсы, добавленные в
  `context.subscriptions`.

Этот подход обеспечивает четкое разделение между ядром и расширениями, а также между backend и frontend логикой каждого
расширения, способствуя модульности и упрощая разработку и поддержку.

### 1.2. Взаимодействие Между Расширениями (Модель VS Code)

Взаимодействие между внутренними расширениями (а в будущем и внешними плагинами) строится по модели, аналогичной VS
Code, обеспечивая слабую связанность и управляемость.

**1. Предоставление API:**

- **Экспорт из `activate`:** Основной механизм — backend-часть расширения (`main/index.ts`) **возвращает объект API** (
  или `Promise`, разрешающийся в API) из своей функции `activate`. Этот объект определяет публичный контракт расширения.

  ```typescript
  // src/extensions/characters/main/index.ts
  import * as characterCommands from "./commands";
  import { CharacterAPI } from "../shared/types"; // Интерфейс API в shared

  export function activate(context): CharacterAPI {
    // ... регистрация команд, видов, IPC ...
    context.subscriptions.push(
      context.commands.registerCommand(
        "characters.create",
        characterCommands.create
      )
    );

    // Логика для получения данных о персонажах
    const getCharacterById = async (id: string): Promise<Character | null> => {
      // Используем сервис ядра для доступа к данным
      return await context.storage.characters.findById(id); // Пример: предполагаем, что storage имеет типизированные репозитории
    };
    const findCharactersInScene = async (
      sceneId: string
    ): Promise<Character[]> => {
      // ... сложная логика поиска ...
      return [];
    };

    // Возвращаем публичное API
    return {
      getCharacterById,
      findCharactersInScene,
    };
  }
  ```

- **Регистрация Сервиса:** Менее предпочтительный вариант для взаимодействия между расширениями, лучше использовать
  экспорт API для явного контракта.

**2. Получение и Использование API:**

- **Через Контекст Ядра:** Ядро приложения (например, `ExtensionRegistry`) отвечает за сбор и предоставление доступа к
  API расширений. При активации расширения Б, оно может запросить API расширения А у ядра через переданный `context`.

  ```typescript
  // src/extensions/editor/main/index.ts (Пример, такого расширения нет)
  import { CharacterAPI } from "../../characters/shared/types"; // Импорт интерфейса

  export async function activate(context) {
    // Получаем API расширения персонажей от ядра
    // Метод getApi асинхронный и может активировать 'characters', если нужно
    const characterApi = await context.extensions.getApi<CharacterAPI>(
      "ai-books.characters" // ID расширения в формате publisher.name
    );

    if (!characterApi) {
      console.error("Character extension API not available.");
      return; // Не регистрируем команду, если зависимость не найдена
    }

    // Регистрируем команду, использующую API расширения персонажей
    context.subscriptions.push(
      context.commands.registerCommand(
        "editor.showCharactersInScene",
        async () => {
          const activeSceneId = context.editors.getActiveSceneId(); // Пример API ядра
          if (activeSceneId) {
            try {
              const characters = await characterApi.findCharactersInScene(
                activeSceneId
              );
              // ... показать персонажей в UI через IPC ...
              context.ipc.sendToRenderer("editor:showCharacters", characters);
            } catch (error) {
              console.error("Error fetching characters for scene:", error);
              context.notifications.showError(
                "Не удалось загрузить персонажей для сцены."
              ); // Пример API уведомлений ядра
            }
          }
        }
      )
    );
  }
  ```

- **Асинхронность и Активация:** Получение API (`context.extensions.getApi`) должно быть асинхронным, так как может
  потребоваться активация запрашиваемого расширения. Ядро управляет этим процессом.
- **Управление Зависимостями:** Явное получение API через `context.extensions.getApi` позволяет ядру отслеживать
  зависимости и управлять порядком активации или обрабатывать циклические зависимости (например, возвращая `undefined`
  или прокси-объект).

**3. Интерфейсы:**

- Крайне важно определять четкие TypeScript интерфейсы для API, предоставляемых расширениями, и размещать их в **общем
  месте** (например, в `src/extensions/my-extension/shared/types.ts` или даже в `src/shared/extension-apis/`). Это
  обеспечивает типобезопасность и служит документацией контракта взаимодействия.

Этот механизм, заимствованный из VS Code, позволяет внутренним расширениям (и в будущем внешним плагинам) надежно и
управляемо взаимодействовать друг с другом, сохраняя при этом модульность и изоляцию.

### 1.3. Взаимодействие Команд Main Process с Renderer Process

Некоторые команды, инициированные в Main процессе (например, через горячие клавиши или меню), могут требовать выполнения
действий в Renderer процессе, часто связанных с текущим состоянием UI (например, сохранение контента активного
редактора).

**Проблема:** Обработчик команды в Main процессе не имеет прямого доступа к состоянию компонентов в Renderer процессе (
например, к несохраненному тексту в редакторе).

**Решение (Архитектурно Корректное):**

1. **Команда Расширения (Main):** Обработчик команды расширения (например, `editor.action.saveScene`) не пытается
   выполнить действие напрямую. Вместо этого он вызывает **общую команду ядра**, предназначенную для этого типа
   действия (например, `workbench:triggerSaveActiveEditor`), используя `context.commands.executeCommand(...)`.
2. **Команда Ядра (Main):** Обработчик команды ядра (зарегистрированный в `src/main/core.commands.ts` или аналогичном
   месте) определяет активное окно `BrowserWindow`.
3. **Целевое IPC (Main -> Renderer):** Обработчик команды ядра отправляет **целевое IPC-сообщение** (например,
   `editor:force-save-triggered`) _только_ в активное окно, используя `focusedWindow.webContents.send(...)`.
4. **Слушатель IPC (Renderer - Ядро UI):** Компонент ядра UI (например, `Workbench.tsx`) или связанный сервис/стор (
   например, `editorStore`) подписывается на это целевое IPC-сообщение.
5. **Действие в Renderer:** При получении сообщения слушатель выполняет необходимое действие в Renderer, которое может
   включать:
    - Чтение данных из состояния UI (например, получение контента из активного редактора).
    - Вызов функции, зарегистрированной активным компонентом (например, вызов `forceSave()` из `editorStore`, который
      был туда помещен плагином редактора).
    - Инициирование **обратного IPC-вызова** (`invoke`) из Renderer в Main для передачи данных или выполнения операций в
      Main процессе (например, вызов `ai-books.books:saveSceneContent` с актуальным контентом редактора).

**Пример: Поток Сохранения Сцены (Ctrl+S)**

```mermaid
sequenceDiagram
    participant User
    participant Core (Main - Keybindings/Commands)
    participant ExtBooks (Main - handleSaveScene)
    participant Core (Main - triggerSave Handler)
    participant Core (Renderer - Workbench Listener)
    participant ExtBooks (Renderer - Editor/Autosave)
    participant ExtBooks (Main - saveSceneContent IPC)

    User->>Core (Main - Keybindings/Commands): Нажимает Ctrl+S
    Core (Main - Keybindings/Commands)->>ExtBooks (Main - handleSaveScene): executeCommand('editor.action.saveScene')
    ExtBooks (Main - handleSaveScene)->>Core (Main - Keybindings/Commands): executeCommand('workbench:triggerSaveActiveEditor')
    Core (Main - Keybindings/Commands)->>Core (Main - triggerSave Handler): Вызов обработчика 'workbench:triggerSaveActiveEditor'
    Core (Main - triggerSave Handler)->>Core (Renderer - Workbench Listener): webContents.send('editor:force-save-triggered')
    Core (Renderer - Workbench Listener)->>ExtBooks (Renderer - Editor/Autosave): Вызов forceSave() из editorStore
    ExtBooks (Renderer - Editor/Autosave)->>ExtBooks (Main - saveSceneContent IPC): invoke('ai-books.books:saveSceneContent', sceneId, content)
    ExtBooks (Main - saveSceneContent IPC)->>ExtBooks (Main - saveSceneContent IPC): bookStorage.saveSceneContent() -> sceneService.writeSceneContent()
```

Этот подход гарантирует, что:

- Логика, специфичная для расширения (формат данных, сохранение), остается внутри расширения.
- Расширение не зависит от деталей реализации UI ядра.
- Ядро управляет общими действиями Workbench (как сохранение активного редактора) и маршрутизацией сигналов между
  процессами.

---

## 2. Точка Вклада: Пользовательские Редакторы

Этот раздел описывает план по расширению архитектуры для поддержки отображения различных типов контента (помимо
текстовых сцен) во вкладках основной области редактирования Workbench.

### 2.1. Цель

Позволить расширениям предоставлять специализированные редакторы или представления (например, профиль персонажа, карта
мира, редактор сцен) для отображения в основной области Workbench (Editor Group) в виде вкладок, аналогично тому, как VS
Code обрабатывает разные типы файлов или пользовательские редакторы.

### 2.2. План Реализации

1. **Реестр "Редакторов" (`EditorService`) в Main процессе:**

    - Сервис `src/main/editor.service.ts` существует.
    - Интерфейс `EditorProvider` определен и описывает регистрируемый редактор:
        - `editorType`: Уникальный ID (строка, например, `ai-books.characters:profile-view`).
        - `extensionId`: ID расширения, предоставляющего редактор (например, `ai-books.characters`).
        - `componentName`: Имя React-компонента (например, `CharacterProfileView`).
        - `title?`: Шаблон заголовка вкладки (опционально).
        - `icon?`: Иконка вкладки (опционально).
    - `EditorService` предоставляет метод `registerEditorProvider(provider: EditorProvider): Disposable` через
      `ExtensionContext`.
    - `EditorService` хранит зарегистрированные провайдеры.
    - `EditorServiceAPI` добавлен в `ExtensionContext`.

2. **Обновление состояния Workbench (`workbenchStore.ts` - Renderer):**

    - Структура данных для открытых вкладок (`openTabs` в `WorkbenchTab`) содержит:
        - `id`: Уникальный ID вкладки (например, `editorType + ':' + dataId`).
        - `editorType`: ID типа редактора.
        - `extensionId`: ID расширения.
        - `componentName`: Имя компонента.
        - `title`: Заголовок вкладки.
        - `dataId`: ID объекта данных (ID сцены, ID персонажа и т.д.).
        - `icon?`: Иконка вкладки (опционально).
        - `isDirty?`: Флаг несохраненных изменений (опционально).

3. **Динамический рендеринг в Workbench (`ActiveEditorArea.tsx` - Renderer):**

    - Компонент `ActiveEditorArea` отвечает за рендеринг содержимого активной вкладки.
    - Он получает `activeTabId` из `workbenchStore`.
    - Использует `useEffect` для получения `extensionId`, `componentName` и `dataId` из данных активной вкладки (
      `useWorkbenchStore.getState().openTabs`) при изменении `activeTabId`.
    - Использует `useMemo` и `React.lazy` с `import.meta.glob` для динамической загрузки компонента редактора из
      соответствующего расширения на основе `extensionId` и `componentName` (см. раздел 2.4).
    - Рендерит загруженный компонент внутри `React.Suspense`, передавая ему `dataId` и другие необходимые props.

4. **Команда для открытия редакторов (`workbench.action.openEditor`):**

    - Команда ядра `workbench.action.openEditor` зарегистрирована (в `src/main/ipc/handlers.ts`).
    - Аргументы команды:
      `{ editorType: string, dataId: string, title?: string, options?: { preserveFocus?: boolean, pinned?: boolean } }`.
    - Обработчик команды:
        - Проверяет `editorType` в `EditorService`.
        - Получает `extensionId`, `componentName` и другие детали провайдера.
        - Формирует объект `WorkbenchTab` (включая `extensionId`).
        - Отправляет IPC-сообщение `'workbench:open-tab'` в Renderer с данными вкладки.

5. **IPC и Preload:**
    - Обработчик для `'workbench:open-tab'` в Renderer (`Workbench.tsx`) обновляет состояние `openTabs`.
    - Функция `openEditor` (вызывающая команду `workbench.action.openEditor`) существует в Preload API.
    - Специфичные для редакторов IPC-каналы (например, `getSceneContent`, `saveSceneContent`) регистрируются **внутри
      соответствующего расширения** с префиксом (например, `ai-books.books:getSceneContent`) и вызываются из Renderer
      через универсальный `window.electronAPI.invoke`.

### 2.3. Соглашение о Путях и Динамическая Загрузка (Vite `import.meta.glob`)

Для надежной динамической загрузки компонентов редакторов в Renderer процессе с использованием Vite, вводится следующее
соглашение:

- **Расположение Компонентов:** Все React-компоненты, предназначенные для использования в качестве редакторов, должны
  располагаться по пути:
  `src/extensions/{имя-расширения}/renderer/editors/{ИмяКомпонента}.editor.tsx`
    - `{имя-расширения}`: Имя директории расширения (например, `books`, `characters`).
    - `{ИмяКомпонента}`: Имя компонента, указанное в `componentName` при регистрации `EditorProvider`. Имя файла должно
      точно совпадать с `componentName` и иметь суффикс `.editor.tsx`.
- **Регистрация:** При регистрации `EditorProvider` в `EditorService` (Main процесс), необходимо **обязательно**
  указывать `extensionId` (например, `ai-books.books`) и `componentName` (например, `Editor`).
- **Передача Данных:** Команда `workbench.action.openEditor` включает `extensionId` в объект `WorkbenchTab`,
  передаваемый в Renderer через событие `workbench:open-tab`. `workbenchStore` хранит `extensionId` для каждой вкладки.
- **Загрузка в Renderer:** Компонент `ActiveEditorArea.tsx`:
    - Использует `import.meta.glob('/src/extensions/*/renderer/editors/*.editor.tsx')` для получения карты модулей во
      время сборки.
    - Получает `extensionId` и `componentName` активной вкладки (через локальное состояние, обновляемое по
      `activeTabId`).
    - Конструирует ожидаемый ключ пути (например, `/src/extensions/books/renderer/editors/Editor.editor.tsx`).
    - Находит соответствующую функцию динамического импорта в карте модулей.
    - Использует `React.lazy()` с найденной функцией импорта для загрузки компонента.

Этот подход позволяет Vite корректно обрабатывать динамические импорты во время сборки и обеспечивает масштабируемый
механизм загрузки редакторов без необходимости жестко кодировать пути в ядре UI.

### 2.4. Пример Использования

1. **Регистрация:** Расширение "Персонажи" вызывает
   `context.editors.registerEditorProvider({ editorType: 'ai-books.characters:profile-view', extensionId: 'ai-books.characters', componentName: 'CharacterProfileView', ... })`.
   Компонент находится в `src/extensions/characters/renderer/editors/CharacterProfileView.editor.tsx`.
2. **Действие:** Пользователь кликает на персонажа.
3. **Вызов:** Frontend вызывает
   `window.electronAPI.openEditor({ editorType: 'ai-books.characters:profile-view', dataId: 'char-123', title: '...' })`.
4. **Обработка:** Main процесс обрабатывает команду, находит провайдер (включая `extensionId` и `componentName`),
   отправляет IPC `'workbench:open-tab'` с полными данными вкладки (включая `extensionId`).
5. **Рендеринг:** Renderer обновляет `openTabs`. `ActiveEditorArea` получает `activeTabId`, обновляет свое локальное
   состояние, получая `extensionId` и `componentName`, находит нужный модуль через `import.meta.glob` и `React.lazy`, и
   рендерит `<CharacterProfileView dataId='char-123' />`.

---

## 3. Принципы Добавления Новых Вкладов (Contribution Points)

Этот раздел описывает общие шаги для добавления различных типов функциональности через систему расширений.

- **Новый вид Sidebar/Panel или Элемент Status Bar:**

    1. Создать React компонент:
        - Для Sidebar/Panel: `src/extensions/my-extension/renderer/views/MyView.view.tsx`.
        - Для Status Bar: `src/extensions/my-extension/renderer/statusbar/MyItem.statusbar.tsx`.
    2. В функции `activate` (`src/extensions/my-extension/main/index.ts`) зарегистрировать через
       `context.views.registerView`:
        - Указать `id`, `name`, `componentName`, `extensionId`.
        - Указать `location: 'sidebar' | 'panel' | 'statusbar'`.
        - Для `statusbar`: указать `alignment: 'left' | 'right'` и опционально `priority: number`.
        - Для `sidebar` или `panel`: можно указать `icon`.
    3. (Опционально) Для основного вида `sidebar`, зарегистрировать иконку в Activity Bar через
       `context.views.registerViewContainer({ id: '...', title: '...', icon: '...', viewId: '...' })`.
    4. Убедиться, что путь к компоненту соответствует соглашению для `import.meta.glob` в `Sidebar.tsx`, `Panel.tsx` или
       `StatusBar.tsx`.

- **Новый Редактор:**

    1. Создать React компонент для редактора в `src/extensions/my-extension/renderer/editors/MyEditor.editor.tsx`.
    2. В функции `activate` (`src/extensions/my-extension/main/index.ts`) зарегистрировать редактор через
       `context.editors.registerEditorProvider({ editorType: '...', extensionId: '...', componentName: 'MyEditor', ... })`.
    3. Убедиться, что путь к компоненту соответствует соглашению для `import.meta.glob` в `ActiveEditorArea.tsx`.
    4. Реализовать команду или другой механизм, который будет вызывать
       `window.electronAPI.openEditor({ editorType: '...', dataId: '...' })` для открытия этого редактора.

- **Новая команда:**

    1. Реализовать обработчик команды **в backend-части расширения** (`src/extensions/my-extension/main/commands.ts` или
       `index.ts`).
    2. В функции `activate` (`src/extensions/my-extension/main/index.ts`) зарегистрировать команду через
       `context.commands.registerCommand({ id: '...', title: '...', category: '...', handler: ..., keybinding?: '...', showInPalette?: boolean, when?: '...' })`.
    3. `CommandService` автоматически зарегистрирует стандартное сочетание клавиш (`keybinding`) в `KeybindingService`.
    4. Команда будет доступна через Command Palette (если `showInPalette !== false` и `when` выполняется) и может быть
       привязана к меню. Горячие клавиши будут работать через `KeybindingService`.

- **Новая настройка:**

    1. Определить интерфейс `SettingDeclaration` (см. `src/shared/types/settings.ts`).
    2. В функции `activate` (`src/extensions/my-extension/main/index.ts`) зарегистрировать декларацию настройки через
       `context.settings.registerSetting({ id: 'myExt.feature.option', label: 'Option Name', description: '...', type: 'boolean', default: true, scope: 'user' })`.
    3. `SettingsService` сохранит декларацию и сделает настройку доступной в UI настроек и для чтения через
       `context.settings.getSettingValue('myExt.feature.option')`.

- **Новая сущность данных:**

    1. Добавить таблицу/схему в `StorageService` ядра (`src/main/storage.service.ts`) или реализовать специфичное для
       расширения хранилище.
    2. Добавить CRUD-методы в `StorageService` или в сервисе расширения.
    3. Зарегистрировать IPC-обработчики для этих методов в **backend-части расширения** (
       `src/extensions/my-extension/main/index.ts`) через `context.ipc.handle('my-extension:myCrudAction', ...)`.
    4. Вызывать эти обработчики из Renderer через `window.electronAPI.invoke('my-extension:myCrudAction', ...)`.

- **Новая задача (Task Runner):**

    1. Реализовать логику задачи **в backend-части расширения** (`src/extensions/my-extension/main/`).
    2. В функции `activate` (`src/extensions/my-extension/main/index.ts`) зарегистрировать задачу через
       `context.tasks.registerTask(...)` (если `TaskService` будет реализован).

- **Новый набор фрагментов (Snippets):**

    1. Создать файл(ы) сниппетов в формате JSON внутри расширения (`src/extensions/my-extension/snippets/`).
    2. В функции `activate` (`src/extensions/my-extension/main/index.ts`) зарегистрировать путь к файлу(ам) через
       `context.configuration.registerSnippetsPath(...)` (если `SnippetService` будет реализован).

- **Новая тема оформления:**
    1. Создать файл темы в формате JSON (например, в `src/extensions/my-theme/theme.json`).
    2. Зарегистрировать тему в `activate` через `context.themes.registerTheme(...)` (если `ThemeService` будет
       реализован).
