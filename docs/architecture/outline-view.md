# Архитектура Представления Структуры Документа (Outline View)

Этот документ описывает архитектуру представления "Структура Документа" (Outline View) в AI-Books IDE, предназначенного для отображения иерархической структуры активного документа (например, сцены) и обеспечения быстрой навигации по нему.

## 1. Цели

- Отображать иерархическую структуру активного документа в виде дерева (например, заголовки, маркеры сцен, комментарии, упоминания персонажей/локаций).
- Позволить пользователю быстро переходить к соответствующему элементу в редакторе по клику на элемент в дереве структуры.
- Автоматически обновлять структуру при изменении содержимого документа.
- (Опционально) Предоставлять возможности фильтрации или сортировки элементов структуры.
- (Опционально) Позволить расширениям предоставлять провайдеров структуры для пользовательских типов документов или редакторов.

## 2. Основные Компоненты

### 2.1. `OutlineView` (Компонент React, Renderer Process)

- **Расположение:** `src/renderer/components/Sidebar/views/OutlineView.view.tsx` (предполагаемое).
- **Ответственность:** Отображение дерева структуры активного документа в Sidebar.
  - Получает данные о структуре от `OutlineService` (Renderer).
  - Рендерит иерархическое дерево элементов структуры (например, используя рекурсивный компонент или библиотеку для деревьев).
  - Отображает имя/текст элемента и, возможно, иконку типа (заголовок, персонаж и т.д.).
  - Обрабатывает клики по элементам дерева, вызывая команду или сервис для навигации в редакторе к соответствующему месту.
- **Регистрация:** Должен быть зарегистрирован как вид (`view`) для `Sidebar` через `ViewService`.

### 2.2. `OutlineService` (Сервис, Renderer Process)

- **Расположение:** `src/renderer/core/outline.service.ts` (предполагаемое) или как часть Zustand store.
- **Ответственность:** Управление состоянием структуры для активного документа в Renderer.
  - Отслеживает активный редактор/документ (через `workbenchStore` или события).
  - Запрашивает данные о структуре у Main процесса через IPC (`invoke('outline:get', { uri })`) при смене активного документа или принудительном обновлении.
  - Подписывается на события обновления структуры от Main процесса (`on('outline:update', ...)`).
  - Хранит актуальные данные структуры для активного документа.
  - Предоставляет API (или является частью store) для `OutlineView` для получения данных структуры.

### 2.3. `OutlineProvider` (Интерфейс и Реализации, Main Process)

- **Ответственность:** Анализ документа и извлечение его структуры. Реализуется ядром и расширениями.
- **Интерфейс (`OutlineProvider`):**
  - `id: string`: Уникальный идентификатор провайдера.
  - `languageId: string | string[]`: Тип(ы) документа, для которых предназначен провайдер.
  - `provideOutline(documentUri: string, content: string): Promise<OutlineItem[] | null>`: Анализирует контент и возвращает массив корневых элементов структуры.
- **`OutlineItem` (Интерфейс):**
  - `label: string`: Текст элемента.
  - `kind: OutlineItemKind`: Тип элемента (Заголовок, Сцена, Персонаж, Локация, Комментарий и т.д.).
  - `range: Range`: Полный диапазон элемента в документе.
  - `selectionRange: Range`: Диапазон для выделения при навигации (обычно начало `label`).
  - `children?: OutlineItem[]`: Дочерние элементы структуры.
- **Реализации:**
  - **Ядро:**
    - `MarkdownOutlineProvider`: Парсит заголовки Markdown.
    - (Возможно) `SceneMarkerOutlineProvider`: Ищет специальные маркеры сцен.
  - **Расширения:**
    - `CharacterMentionOutlineProvider`: Находит упоминания персонажей (может использовать `AnalysisService`).
    - Провайдеры для пользовательских форматов или редакторов.

### 2.4. `OutlineCoordinator` (Ядро, Main Process)

- **Расположение:** `src/main/outline.coordinator.ts` (предполагаемое).
- **Ответственность:** Координация работы `OutlineProvider`.
  - Регистрирует `OutlineProvider` (через точку вклада `outlineProviders`).
  - Обрабатывает запросы `invoke('outline:get', ...)` от Renderer.
  - Получает уведомления об изменении документов.
  - Выбирает подходящий `OutlineProvider` на основе `languageId` документа.
  - Вызывает `provideOutline` у выбранного провайдера.
  - Кэширует результаты для производительности.
  - Возвращает результат в Renderer.
  - (Опционально) Инициирует обновление структуры и отправляет `send('outline:update', ...)` при изменении документа.

### 2.5. Интеграция с Навигацией

- **Ответственность:** Переход к элементу в редакторе.
- **Реализация:**
  - При клике на элемент в `OutlineView`:
    - Получаем `selectionRange` элемента.
    - Вызываем команду ядра (например, `editor.revealRange`) с URI документа и `selectionRange`.
    - Команда обрабатывается в Main, который передает ее активному редактору в Renderer для выполнения прокрутки и установки курсора.

## 3. Процесс Работы

1.  **Регистрация (Main):** Ядро и расширения регистрируют свои `OutlineProvider` в `OutlineCoordinator`.
2.  **Активация Вида (Renderer):** Пользователь открывает `OutlineView` в Sidebar.
3.  **Запрос Структуры (Renderer -> Main):** `OutlineService` определяет активный документ и запрашивает его структуру: `invoke('outline:get', { uri })`.
4.  **Координация (Main):** `OutlineCoordinator` получает запрос, находит подходящий `OutlineProvider` для URI/типа документа.
5.  **Анализ (Main):** `OutlineCoordinator` вызывает `provider.provideOutline(uri, content)`. Провайдер анализирует контент (возможно, запрашивая его у `StorageService` или `EditorService`) и возвращает `Promise<OutlineItem[]>`.
6.  **Ответ (Main -> Renderer):** `OutlineCoordinator` возвращает результат `OutlineService`.
7.  **Обновление Состояния (Renderer):** `OutlineService` сохраняет полученную структуру.
8.  **Отображение (Renderer):** `OutlineView` получает данные от `OutlineService` и рендерит дерево структуры.
9.  **Навигация (Renderer -> Main -> Renderer):** Пользователь кликает на элемент -> `OutlineView` вызывает команду `editor.revealRange` -> Main передает команду в Renderer -> Редактор выполняет навигацию.
10. **Обновление при Редактировании (Main -> Renderer):** Пользователь меняет документ -> `OutlineCoordinator` (если настроен на отслеживание) или `OutlineService` (по таймеру/событию) инициирует повторный анализ (шаги 3-8) и/или `OutlineCoordinator` отправляет `send('outline:update', ...)` для обновления UI.

## 4. Преимущества

- **Улучшение Навигации:** Быстрый переход к нужным частям больших документов.
- **Обзор Структуры:** Помогает понять организацию контента.
- **Расширяемость:** Позволяет добавлять поддержку структуры для разных типов контента.
- **Интеграция:** Может использовать результаты других систем (например, анализа упоминаний персонажей).
