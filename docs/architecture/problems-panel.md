# Архитектура Панели Проблем/Диагностики

Этот документ описывает архитектуру компонента "Панель Проблем" (Problems Panel) в AI-Books IDE, предназначенного для централизованного отображения диагностических сообщений от различных "Серверов" Анализа.

## 1. Цели

- Предоставить пользователю единое место для просмотра всех проблем (ошибок, предупреждений, информации, подсказок), обнаруженных в проекте.
- Позволить легко навигировать от проблемы к соответствующему месту в тексте или данных.
- Обеспечить фильтрацию и группировку проблем для удобства анализа.
- Интегрироваться с системой "Серверов" Анализа для получения диагностических данных.

## 2. Основные Компоненты

### 2.1. `ProblemsView` (Компонент React, Renderer Process)

- **Расположение:** `src/renderer/components/Panel/views/ProblemsView.view.tsx` (предполагаемое) или как часть расширения, если это будет реализовано как вклад.
- **Ответственность:** Отображение списка проблем в UI нижней панели (Panel).
  - Получает диагностические данные из `ProblemsService` (Renderer).
  - Рендерит список проблем, сгруппированных по файлу/URI.
  - Отображает иконку серьезности, сообщение, источник и местоположение (строка, столбец) для каждой проблемы.
  - Предоставляет элементы управления для фильтрации (по серьезности, тексту) и, возможно, группировки (по файлу, по типу).
  - Обрабатывает клики по проблемам, вызывая команду или сервис для открытия соответствующего файла/редактора и перемещения курсора к месту проблемы.
- **Регистрация:** Должен быть зарегистрирован как вид (`view`) для `Panel` через `ViewService` (вероятно, ядром приложения или специальным расширением "diagnostics").

### 2.2. `ProblemsService` (Сервис, Renderer Process)

- **Расположение:** `src/renderer/core/problems.service.ts` (предполагаемое) или как часть Zustand store.
- **Ответственность:** Управление состоянием диагностики в Renderer процессе.
  - Подписывается на IPC-событие `on('diagnostics:update', ...)` от Main процесса.
  - Хранит актуальный список всех диагностических сообщений (`Map<string, Diagnostic[]>`, где ключ - URI документа).
  - Предоставляет API для `ProblemsView` для получения текущего списка проблем (возможно, отфильтрованного).
  - Может предоставлять методы для очистки диагностики для закрытых файлов.
- **Взаимодействие:** Получает данные от Main, предоставляет данные для `ProblemsView`.

### 2.3. `AnalysisService` (Ядро, Main Process)

- **Ответственность:** (Как описано в `analysis-servers.md`) Агрегирует диагностику от всех `AnalysisProvider` и отправляет ее в Renderer.
- **Ключевое Действие:** Отправляет событие `send('diagnostics:update', { uri: documentUri, diagnostics: allDiagnostics })` при обновлении диагностики для какого-либо документа.

### 2.4. Интеграция с Навигацией

- **Ответственность:** Обеспечение перехода от проблемы в `ProblemsView` к месту в редакторе.
- **Реализация:**
  - При клике на проблему в `ProblemsView`:
    - Определяется URI документа и `Range` проблемы.
    - Вызывается команда ядра (например, `workbench.action.openEditor`) с URI документа и опциями для выделения `Range` или перемещения курсора.
    - Команда обрабатывается в Main процессе, который открывает нужный редактор (если он еще не открыт) и отправляет событие в Renderer для установки фокуса и выделения.

## 3. Процесс Работы

1.  **Анализ (Main):** `AnalysisService` получает диагностику от провайдеров.
2.  **Уведомление (Main -> Renderer):** `AnalysisService` отправляет `send('diagnostics:update', { uri, diagnostics })`.
3.  **Обновление Состояния (Renderer):** `ProblemsService` (или Zustand store) получает событие и обновляет свой внутренний список диагностики.
4.  **Перерисовка UI (Renderer):** `ProblemsView` (подписанный на изменения в `ProblemsService`/store) получает обновленный список и перерисовывает UI панели.
5.  **Навигация (Renderer -> Main -> Renderer):** Пользователь кликает на проблему -> `ProblemsView` вызывает команду `workbench.action.openEditor` с URI и Range -> Main процесс обрабатывает команду, открывает/фокусирует редактор -> Renderer (редактор) получает команду установить курсор/выделение.

## 4. Преимущества

- **Централизация:** Все проблемы собраны в одном месте.
- **Интерактивность:** Легкий переход к источнику проблемы.
- **Гибкость:** Возможность фильтрации и группировки.
- **Разделение Ответственности:** Четкое разделение между сбором данных (Main) и отображением (Renderer).
