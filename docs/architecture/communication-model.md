# Модель Коммуникаций и Событий AI-Books IDE

## 1. Введение

Этот документ описывает основные каналы, типы взаимодействий и соглашения по именованию для обмена информацией между различными частями приложения AI-Books IDE: ядром Main процесса, ядром Renderer процесса (UI), backend-частями расширений (Main) и frontend-частями расширений (Renderer). Цель — создать понятную, управляемую и расширяемую систему коммуникаций.

## 2. Основные Участники

- **Main Process (Ядро):** Основной процесс Electron. Управляет жизненным циклом приложения, окнами, доступом к системным ресурсам (ФС, БД), содержит основные сервисы (`ServiceManager`, `WindowManager`, `ExtensionManager`, `CommandService`, `StorageService`, `SettingsService`, `ContextService`, `EditorService`, `ViewService`, `IpcService` и т.д.).
- **Renderer Process (Ядро UI):** Процесс окна браузера. Отвечает за отображение пользовательского интерфейса с помощью React (`Workbench`, `EditorGroup`, `Sidebar`, `Panel`, `StatusBar` и т.д.). Управляет состоянием UI (например, через Zustand `workbenchStore`).
- **Main Process (Расширения):** Логика конкретных расширений (например, `ai-books.books`, `ai-books.characters`), выполняющаяся в Main процессе. Загружается и активируется `ExtensionManager`. Взаимодействует с ядром через `ExtensionContext`.
- **Renderer Process (UI Расширений):** Компоненты React, предоставляемые расширениями (например, `BooksExplorerView`, `SceneEditor`), которые динамически загружаются и отображаются ядром UI в Renderer процессе.

## 3. Каналы Коммуникации

Существует несколько основных каналов для взаимодействия:

1.  **IPC (Inter-Process Communication):** Стандартный механизм Electron для связи между Main и Renderer процессами. Используется через Preload скрипт (`contextBridge`) для безопасности.

    - **`invoke` / `handle` (Renderer -> Main -> Renderer):** Асинхронные запросы из Renderer, ожидающие ответа (`Promise`) от Main. Основной способ для Renderer запросить данные или выполнить действие в Main с получением результата. Обработчики регистрируются в Main через `ipcMain.handle()`.
    - **`send` / `on` (Main -> Renderer):** Асинхронные сообщения (события) из Main в Renderer без ожидания ответа. Используются для уведомления UI об изменениях состояния, командах для UI и т.д. Отправка через `webContents.send()`. Слушатели регистрируются в Renderer через `window.electronAPI.onMainEvent()`.
    - **`send` / `on` (Renderer -> Main):** Асинхронные сообщения из Renderer в Main без ожидания ответа. **Использование минимизировано.** В основном для "fire-and-forget" уведомлений, таких как логирование (`core:log.message`). Для большинства других случаев предпочтительнее `invoke`. Отправка через `window.electronAPI.send()`. Обработчики в Main через `ipcMain.on()`.

2.  **Система Команд (`CommandService`, Main):** Централизованный реестр действий.

    - Команды регистрируются ядром и расширениями в `CommandService` (Main).
    - Вызываются из Main через `commandService.executeCommand(...)`.
    - Вызываются из Renderer **только** через IPC `invoke` канал `core:commands.execute`.
    - Обработчики команд выполняются в Main процессе.

3.  **API Расширений (`ExtensionContext`, Main):** Объект, передаваемый ядром в функцию `activate()` расширения. Содержит ссылки на API сервисов ядра (включая типизированные обертки для регистрации IPC и команд), позволяя расширению регистрировать свои компоненты и взаимодействовать с ядром.

4.  **Внутренние События/Состояние (Renderer):** Взаимодействие между React-компонентами внутри Renderer процесса.

    - **Zustand:** Для глобального и модульного управления состоянием UI.
    - **Props/Callbacks:** Стандартный механизм React.
    - **React Context:** Для передачи данных вниз по дереву компонентов.
    - **(Опционально) Шина событий (`mitt`):** Для слабой связи между компонентами, использовать с осторожностью.

5.  **Абстракция IPC (`ipcMainService`, `ipcRendererService`):**
    - **Цель:** Инкапсулировать прямые вызовы Electron IPC, добавить типизацию, логирование и централизованную обработку.
    - **`ipcMainService` (Main):** Предоставляет методы `handle<TArgs, TResult>(channel, handler)` и `send<TData>(webContents, channel, data)`. Автоматически применяет типизацию и обработку ошибок.
    - **`ipcRendererService` (Renderer):** Предоставляет методы `invoke<TResult, TArgs>(channel, args?)` и `on<TData>(channel, listener)`. Обеспечивает типизацию и логирование.

## 4. Типы Взаимодействий и Соглашения по Именованию

**Важно:** Все каналы IPC и аргументы/результаты команд **должны** быть строго типизированы с использованием TypeScript интерфейсов, определенных в `src/shared/types/`.

Используется **точечная нотация** (`domain.action`) после префикса для единообразия.

### 4.1. IPC `invoke` (Renderer -> Main)

- **Назначение:** Запрос данных или выполнение действия в Main с ожиданием результата (`Promise`). Основной способ взаимодействия Renderer -> Main.
- **Префиксы:**
  - `core:` - для запросов к API ядра.
  - `[extensionId]:` - для запросов к API конкретного расширения (например, `ai-books.books:`).
- **Формат:** `[prefix]:[domain].[action]`
- **Примеры:**
  - `core:settings.get` (args: { key: string }) => Promise<unknown>
  - `core:settings.set` (args: { key: string, value: unknown }) => Promise<void>
  - `core:settings.getAll` () => Promise<Record<string, unknown>>
  - `core:commands.execute` (args: { commandId: string, args?: unknown[] }) => Promise<unknown>
  - `core:commands.getAll` () => Promise<CommandDescription[]>
  - `core:context.get` (args: { key: string }) => Promise<unknown>
  - `core:context.set` (args: { key: string, value: unknown }) => Promise<void>
  - `core:views.getRegisteredViews` () => Promise<ViewDescription[]>
  - `core:views.getRegisteredViewContainers` () => Promise<ViewContainerDescription[]>
  - `core:keybindings.getActive` () => Promise<Keybinding[]>
  - `core:app.getVersion` () => Promise<string>
  - `core:window.isMaximized` () => Promise<boolean>
  - `core:editor.getContent` (args: { editorId: string }) => Promise<string | null> // Новый для Save All
  - `ai-books.books.getStructure` () => Promise<BookStructure>
  - `ai-books.books.createChapter` (args: CreateChapterArgs) => Promise<Chapter>
  - `ai-books.books.saveSceneContent` (args: { sceneId: string, content: string }) => Promise<void>
  - `ai-books.books.getSceneContent` (args: { sceneId: string }) => Promise<string | null>
  - `ai-books.characters.getDetails` (args: { characterId: string }) => Promise<CharacterDetails>

### 4.2. События `send` (Main -> Renderer)

- **Назначение:** Уведомление Renderer об _изменениях состояния_, произошедших в Main, или простые UI-триггеры. Renderer реагирует на них, обновляя UI или запрашивая доп. данные через `invoke`. **Не использовать для команд к UI.**
- **Префиксы:** Аналогично `invoke`.
- **Формат:** `[prefix]:[domain].[event]`
- **Примеры:**
  - `core:workbench.openTab` (data: WorkbenchTab)
  - `core:workbench.closeTab` (data: { tabId: string })
  - `core:context.changed` (data: { key: string, value: unknown })
  - `core:workbench.showNotification` (data: NotificationOptions)
  - `core:workbench.showInputDialog` (data: InputDialogOptions) // Запрос на показ диалога
  - `core:workbench.togglePalette` ()
  - `core:workbench.toggleSidebar` ()
  - `core:workbench.togglePanel` ()
  - `core:settings.changed` (data: { key: string, newValue: unknown })
  - `ai-books.books.structureChanged` (data?: { bookId: string }) // Уведомление об изменении структуры

### 4.3. IPC `send` (Renderer -> Main)

- **Назначение:** Минимизировано. В основном для логирования.
- **Префиксы:** `core:`
- **Формат:** `[prefix]:[domain].[action]`
- **Примеры:**
  - `core:log.message` (data: { level: 'info' | 'warn' | 'error', message: string, ... })
  - `core:dialog.inputResponse` (data: { dialogId: string, value: string | null }) // Ответ от InputDialog

### 4.4. Команды (`CommandService`)

- **Назначение:** Централизованные действия, вызываемые пользователем или программно.
- **Префиксы:**
  - `app.` - команды управления приложением.
  - `workbench.` - команды управления UI ядра.
  - `editor.` - команды, связанные с активным редактором.
  - `[extensionId].` - команды, предоставляемые расширениями.
- **Формат:** `[prefix].[action]`
- **Примеры:**
  - `app.quit`
  - `app.about`
  - `workbench.openSettings`
  - `workbench.saveAll` // Логика централизована в Main
  - `workbench.openEditor` (args: { editorType: string, dataId: string, title: string, ... })
  - `workbench.toggleDevTools`
  - `workbench.toggleFullScreen`
  - `workbench.toggleCommandPalette`
  - `workbench.toggleSidebarVisibility`
  - `workbench.togglePanelVisibility`
  - `editor.saveActive` // Команда для сохранения текущего активного редактора (логика в Main)
  - `ai-books.books.createNewChapter`
  - `ai-books.characters.openProfile`

## 5. Обработка Ошибок IPC

- Обработчики `ipcMain.handle` (внутри `ipcMainService`) должны перехватывать ошибки выполнения.
- Если происходит ошибка, `handle` должен `throw` стандартизированный объект ошибки (или кастомный класс), например:
  ```typescript
  interface IpcErrorData {
    code: string; // e.g., 'STORAGE_ERROR', 'NOT_FOUND', 'VALIDATION_FAILED'
    message: string;
    details?: unknown;
  }
  ```
- `ipcRendererService.invoke` автоматически обрабатывает rejection Promise. Приложение должно иметь глобальный обработчик ошибок Promise rejection или обрабатывать ошибки в месте вызова `invoke` (через `.catch()`), чтобы информировать пользователя (например, через `core:workbench.showNotification` - **Post-MVP**).

## 6. Обработка Горячих Клавиш (Hotkeys)

- **Триггер:** Пользователь нажимает сочетание клавиш (например, `Cmd/Ctrl+S`).
- **Обнаружение (Main Process):**
    - **Стандартные действия:** Для стандартных действий (Сохранить, Копировать, Вставить, Отменить, Повторить и т.д.) используется механизм `accelerator` в главном меню Electron (`Menu.buildFromTemplate`). Electron автоматически обрабатывает эти сочетания в контексте активного окна.
    - **Глобальные действия:** Для глобальных сочетаний (например, показать/скрыть Command Palette) может использоваться `globalShortcut.register` (использовать с осторожностью, чтобы не перехватывать системные сочетания).
- **Привязка к Команде (Main Process):** Каждый `accelerator` в меню или зарегистрированный `globalShortcut` привязывается к конкретному `commandId` из `CommandService`.
- **Выполнение (Main Process):** При срабатывании акселератора/глобального шортката вызывается `CommandService.executeCommand(commandId)`.
- **Действие:** Обработчик команды выполняет необходимое действие. Если требуется взаимодействие с UI (как при сохранении), он инициирует соответствующий IPC-поток (см. п. 7.2).
- **Пользовательская Настройка:** Настройка горячих клавиш пользователем - **Post-MVP**.

## 7. Обработка Контекстных Меню

- **Триггер:** Пользователь кликает правой кнопкой мыши на элементе UI в Renderer Process (например, на элементе в `BookStructureView` или в редакторе).
- **Событие (Renderer Process):** Срабатывает обработчик `onContextMenu` в React-компоненте.
- **Сбор Контекста (Renderer Process):** Обработчик собирает информацию о контексте вызова (например, `{ contextType: 'sceneNode', sceneId: 'scene-123', bookId: 'book-abc' }`).
- **IPC Запрос (Renderer -> Main):** Обработчик вызывает `window.electronAPI.invoke('core:workbench.showContextMenu', { contextType, contextData })`.
- **Построение Меню (Main Process):**
    - Обработчик IPC `core:workbench.showContextMenu` получает контекст.
    - Он запрашивает у `CommandService` (или специализированного `MenuService`) список команд, доступных для данного `contextType` (проверяя `when` клаузы команд или используя точки вклада `menus`).
    - Строит динамическое меню с помощью `Menu.buildFromTemplate`, где каждый `MenuItem` имеет `click` обработчик, вызывающий `CommandService.executeCommand(commandId, contextData)`.
    - Отображает меню пользователю с помощью `menu.popup({ window: BrowserWindow.fromWebContents(event.sender) })`.
- **Выполнение Команды (Main Process):** При выборе пункта меню Electron вызывает соответствующий `click` обработчик, который выполняет команду через `CommandService`.

## 8. Ключевые Потоки Событий (Обновленные Примеры)

### 6.1. Открытие Редактора Сцены

1.  **Renderer (`BooksExplorerView`):** Пользователь кликает на сцену.
2.  **Renderer (`ipcRendererService`):** Вызывается `ipcRendererService.invoke('core:commands.execute', { commandId: 'workbench.openEditor', args: [{ editorType: 'ai-books.books:scene-editor', dataId: sceneId, title: sceneTitle }] })`.
3.  **Main (`ipcMainService`):** Обрабатывается `core:commands.execute`.
4.  **Main (`CommandService`):** Выполняется команда `workbench.openEditor`.
5.  **Main (`commands.ts`):** Обработчик команды находит `EditorProvider` через `EditorService`, формирует `WorkbenchTab`.
6.  **Main (`ipcMainService`):** Отправляет событие `ipcMainService.send(mainWindow.webContents, 'core:workbench.openTab', tabData)`.
7.  **Renderer (`ipcRendererService`):** Получает событие `core:workbench.openTab`, уведомляет подписчиков (например, `Workbench.tsx`).
8.  **Renderer (`Workbench.tsx`):** Обновляет `workbenchStore`.
9.  **Renderer (`ActiveEditorArea.tsx`):** Реагирует на изменение `activeTabId`, динамически загружает компонент редактора.
10. **Renderer (`Editor.editor.tsx`):** Монтируется, вызывает `ipcRendererService.invoke('ai-books.books.getSceneContent', { sceneId })`.
11. **Main (`ipcMainService`):** Обрабатывает `ai-books.books.getSceneContent`, возвращает контент.
12. **Renderer (`Editor.editor.tsx`):** Получает контент, инициализирует Lexical.

### 8.2. Сохранение Активной Сцены (Ctrl+S / Команда `editor.saveActive`) (MVP Вариант)

1.  **Триггер (Main/Renderer):** Пользователь нажимает `Cmd/Ctrl+S` (обрабатывается через меню Electron) или вызывает команду `editor.saveActive` программно.
2.  **Выполнение Команды (Main):** `CommandService` выполняет команду `editor.saveActive`.
3.  **Определение Контекста (Main):** Обработчик команды `editor.saveActive` определяет активную вкладку/сцену (например, через `ContextService` или `WindowManager`). Получает `sceneId`.
4.  **Запрос на Сохранение (Main -> Renderer):** Обработчик команды отправляет **целевое событие** в нужный Renderer процесс: `send('core:editor.requestSave', { sceneId: activeSceneId })`.
5.  **Получение События (Renderer):** `AutosavePlugin` (или другой компонент редактора) слушает событие `core:editor.requestSave`.
6.  **Получение Контента (Renderer):** При получении события, плагин немедленно (без debounce) получает текущее состояние редактора Lexical (`editorState.toJSON()`).
7.  **IPC Запрос на Сохранение (Renderer -> Main):** Плагин вызывает `invoke('books:saveSceneContent', { sceneId: activeSceneId, contentJsonString: editorStateJson })`.
8.  **Сохранение (Main):** Обработчик IPC `books:saveSceneContent` вызывает `SceneContentService` для конвертации JSON в Markdown и записи в файл. Может также вызвать `VersioningService` для создания снимка.
9.  **Ответ (Main -> Renderer):** Обработчик IPC возвращает результат (успех/ошибка).
10. **Обновление UI (Renderer):** UI может обновить статус (например, убрать индикатор "несохранено").

### 8.3. Сохранение при Выходе (`onWillQuit`) (MVP Вариант)

1.  **Триггер (Main):** Пользователь закрывает приложение, срабатывает событие `app.on('will-quit')`.
2.  **Вызов Команды (Main):** Обработчик `onWillQuit` вызывает `commandService.executeCommand('workbench.saveAll')`.
3.  **Определение "Грязных" Вкладок (Main):** Обработчик `workbench.saveAll` получает список открытых вкладок и их "грязный" статус (этот статус должен поддерживаться в `workbenchStore` и синхронизироваться с Main или запрашиваться у Renderer). _Упрощение для MVP: можно просто отправить запрос на сохранение всем открытым редакторам._
4.  **Запрос на Сохранение (Main -> Renderers):** Обработчик отправляет событие `send('core:editor.requestSave', { sceneId })` **всем** открытым редакторам сцен.
5.  **Сохранение (Renderers -> Main):** Каждый активный редактор сцены получает событие и вызывает `invoke('books:saveSceneContent', ...)` (как в п. 8.2, шаги 6-8).
6.  **Ожидание Завершения (Main):** Обработчик `workbench.saveAll` должен дождаться завершения всех инициированных `invoke('books:saveSceneContent', ...)` запросов. Это можно сделать, собирая промисы от IPC-вызовов, инициированных из Renderer, или используя механизм подтверждения от Renderer (например, `invoke('core:workbench.saveRequestCompleted', { sceneId })`). _Требует аккуратной реализации асинхронного ожидания._
7.  **Завершение (Main):** После завершения всех сохранений (или по таймауту), обработчик `workbench.saveAll` завершается.
8.  **Продолжение Выхода (Main):** Обработчик `onWillQuit` продолжает выполнение: `await extensionManager.dispose()`, `await serviceManager.dispose()`.
9.  **Закрытие БД (Main):** `StorageService.dispose()` закрывает соединение с SQLite.
10. **Выход (Main):** `app.exit()`.

## 9. Рекомендации

- **Строго типизировать всё.**
- Использовать `ipcRendererService` и `ipcMainService` для абстракции.
- Придерживаться префиксов и нейминга `domain.action`.
- Минимизировать прямые зависимости между расширениями, использовать API ядра и команды.
- Использовать `invoke` (Renderer -> Main) для запросов данных и действий с результатом.
- Использовать `send` (Main -> Renderer) **только** для уведомлений об изменениях состояния или простых UI-триггеров.
- Централизовать сложную логику (как `saveAll`) в Main, минимизируя сложную хореографию событий между процессами. Рассмотреть альтернативы двунаправленному `invoke`, если возможно (например, кэширование состояния в Main или явные запросы на сохранение от Main к Renderer с последующим `invoke` от Renderer).
- Внедрить стандартизированную обработку ошибок IPC.
- Централизовать обработку _основных_ событий UI ядра (`core:workbench.*`) в `Workbench.tsx` или специализированных хуках/сервисах Renderer.
- Обработку событий, специфичных для UI расширения, реализовывать внутри компонентов этого расширения.
