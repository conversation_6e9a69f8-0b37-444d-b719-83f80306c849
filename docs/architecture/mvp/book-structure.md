# MVP Book Structure Management Architecture

## 1. Overview

This document details the MVP implementation for managing the hierarchical structure of writing projects (Books) within the AI-Books IDE. It focuses on providing core organizational capabilities using the hybrid storage model defined in `storage.md`.

## 2. Core Principles (MVP Focus)

- **Hierarchy:** Support for Book → Section → Chapter → Scene structure.
- **Metadata Storage:** All structural metadata (titles, positions, relationships, file paths for scenes) stored in the SQLite database via `StorageService`.
- **Basic Operations:** Enable essential Create, Read, Update, Delete (CRUD) operations for all structural elements.
- **Simple Ordering:** Manage the order of sections, chapters, and scenes using a numerical `position` field.
- **UI Integration:** Provide a visual representation of the structure in the Sidebar (`BookStructureView`).

## 3. Components

### 3.1. Data Models (SQLite - Managed by `StorageService`)

- **`books`:** Stores top-level book information.
- **`sections`:** Stores section metadata, linked to `books`. Includes `position`.
- **`chapters`:** Stores chapter metadata, linked to `books` and optionally `sections`. Includes `position`.
- **`scenes`:** Stores scene metadata, linked to `chapters`. Includes `position` and `markdown_file_path`.

### 3.2. `BookStructureService` (or similar, Main Process)

- **Responsibilities:** Encapsulates the business logic for managing the book hierarchy.
  - Handles CRUD operations for Books, Sections, Chapters, and Scenes (metadata only, content handled by `SceneContentService`).
  - Manages the `position` field for ordering elements within their parent.
  - Ensures data integrity (e.g., updating parent links, managing positions on delete/move).
  - Interacts with `StorageService` to persist changes.
  - Interacts with `SceneContentService` to create/delete corresponding Markdown files when scenes are added/removed.
- **API (Internal):** Methods like `createChapter(bookId, sectionId, title, position)`, `moveScene(sceneId, targetChapterId, newPosition)`, `deleteSection(sectionId)`, etc.

### 3.3. IPC Handlers (Main Process)

- **Responsibilities:** Expose `BookStructureService` functionality to the Renderer process securely.
- **Channels (Examples):**
  - `invoke('books:getStructure', { bookId })`: Returns the full hierarchical structure for a book (Sections, Chapters, Scenes metadata).
  - `invoke('books:createSection', { bookId, title, position? })`
  - `invoke('books:createChapter', { bookId, sectionId?, title, position? })`
  - `invoke('books:createScene', { chapterId, title, position? })`
  - `invoke('books:updateElement', { elementType: 'section'|'chapter'|'scene', elementId, data: { title?, position? } })`
  - `invoke('books:deleteElement', { elementType: 'section'|'chapter'|'scene', elementId })`
  - `invoke('books:moveElement', { elementType: 'section'|'chapter'|'scene', elementId, targetParentId, newPosition })`
- **Implementation:** Handlers call the corresponding methods on `BookStructureService`.

### 3.4. `BookStructureView` (React Component, Renderer Process)

- **Responsibilities:** Displays the book hierarchy in the Sidebar and allows user interaction.
- **Implementation:**
  - Fetches the book structure via `invoke('books:getStructure', ...)` on load or book change.
  - Renders a tree view (e.g., using a library or custom component) based on the received data.
  - Provides UI elements (buttons, context menus) to trigger CRUD and move operations by calling the relevant IPC handlers.
  - Handles basic reordering logic (determining `targetParentId` and `newPosition`) before calling the `books:moveElement` IPC handler.
  - Updates its view based on data refresh or events from Main (e.g., `ai-books.books.structureChanged`).

### 3.5. Position Management

- **Mechanism:** Each level (Sections in Book, Chapters in Section/Book, Scenes in Chapter) uses an integer `position` column in the database.
- **Ordering:** Elements are displayed based on ascending `position` value.
- **Updates:** `BookStructureService` is responsible for recalculating and updating positions when elements are added, deleted, or moved to maintain a contiguous sequence (e.g., 0, 1, 2, ...). This avoids complex fractional or linked-list approaches for the MVP.

## 4. Key Workflows (MVP)

- **Viewing Structure:** `BookStructureView` requests structure via IPC, `BookStructureService` queries `StorageService`, data is returned and rendered.
- **Adding an Element:** User clicks "Add Chapter" in `BookStructureView` -> UI prompts for title -> UI calls `invoke('books:createChapter', ...)` -> `BookStructureService` creates DB entry (calculating position) -> `BookStructureService` returns new chapter data -> UI updates. (Similar for Sections/Scenes, Scene creation also triggers `SceneContentService` to create the Markdown file).
- **Renaming an Element:** User edits title in `BookStructureView` -> UI calls `invoke('books:updateElement', ...)` -> `BookStructureService` updates DB -> UI updates.
- **Deleting an Element:** User confirms delete in `BookStructureView` -> UI calls `invoke('books:deleteElement', ...)` -> `BookStructureService` deletes DB entry (and children), adjusts positions of siblings, triggers `SceneContentService` if deleting a scene -> UI updates.
- **Moving an Element (Basic):** User indicates move (e.g., via context menu "Move Up/Down" or simple drag if implemented) -> UI calculates target parent and position -> UI calls `invoke('books:moveElement', ...)` -> `BookStructureService` updates element's parent ID and adjusts positions in old and new parent lists -> UI updates.

## 5. Excluded from MVP

- **Templates:** No predefined or custom structure templates.
- **Advanced Drag-and-Drop:** Complex visual drag-and-drop between different levels or with visual feedback might be deferred. Basic reordering within the same parent level is feasible.
- **Progress/Status Tracking:** No fields or UI for tracking writing status (draft, complete) per element.
- **Bulk Operations:** No UI for selecting and moving/deleting multiple items at once.
- **Advanced Metadata:** Only core metadata (title, position) managed in MVP.
