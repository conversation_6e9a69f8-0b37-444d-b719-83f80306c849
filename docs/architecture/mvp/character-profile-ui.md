# MVP Character Profile UI Architecture

## 1. Overview

This document outlines the agreed-upon UI architecture for viewing and editing character profiles in the AI-Books IDE MVP. The goal is to provide a structured and scalable interface for managing detailed character information while maintaining a clean user experience.

## 2. Core Principles

- **Unified Editor Tab:** All information and editing capabilities for a single character reside within one main editor tab (`CharacterProfileEditor.editor.tsx`).
- **Internal Navigation:** Navigation between different sections of the character profile (Details, Attributes, Events, etc.) is handled via an internal navigation panel (secondary sidebar) within the main editor tab, similar to the application's Settings editor.
- **Modular Components:** Each section within the editor is rendered by a dedicated React component (e.g., `CharacterProfileDescription`, `CharacterProfileAttributes`, `CharacterProfileEvents`), promoting code modularity and maintainability.
- **Focused Sidebar View:** The main application sidebar (`CharactersView.view.tsx`) will primarily serve as a list for selecting characters, triggering the opening of the main editor tab. It will not display detailed profile information itself.

## 3. UI Structure

### 3.1. `CharacterProfileEditor.editor.tsx` (Main Editor Tab)

- **Layout:** Two-column layout.
  - **Left Column (Internal Navigation):** A narrow vertical panel listing the available profile sections (e.g., "Details", "Attributes", "Events", "Metadata"). Clicking an item updates the right column. Includes a search/filter bar (future enhancement).
  - **Right Column (Content Area):** Displays the content for the currently selected section by rendering the corresponding component.
- **State Management:** Manages the overall loading state, error state, and potentially the selection of the active internal section. Fetches the complete character profile data.
- **Header:** Includes the character name (potentially editable) and a global "Save Profile" button (rendered by `CharacterProfileHeader`).

### 3.2. Internal Navigation Panel (Component within `CharacterProfileEditor`)

- Displays a list of clickable section names/icons.
- Manages the state of the currently selected section.
- Updates the content area when a section is selected.

### 3.3. Section Components (`src/extensions/characters/renderer/components/profile/`)

- `CharacterProfileHeader.tsx`: Displays character name input and Save button.
- `CharacterProfileDescription.tsx`: Displays/Edits the main description textarea.
- `CharacterProfileAttributes.tsx`: (Future) Displays/Edits key-value attributes.
- `CharacterProfileEvents.tsx`: (Future) Displays/Edits character development events (potentially with a timeline).
- `CharacterProfileMetadata.tsx`: Displays read-only metadata (created/updated dates).
- _(Future Sections):_ Relations, Notes, Gallery, etc.

### 3.4. `CharactersView.view.tsx` (Main Sidebar View)

- Displays a simple list of characters in the current project.
- Handles character creation trigger (e.g., "+" button).
- Handles character selection: Clicking a character executes the `core:workbench.action.openEditor` command to open the corresponding `CharacterProfileEditor` tab.
- Handles context menu actions (Delete, Open Profile - which also triggers the editor).

## 4. Workflow Example

1.  User clicks on the "Characters" icon in the Activity Bar.
2.  The `CharactersView` appears in the main sidebar, showing the list of characters.
3.  User clicks on "Alex" in the list.
4.  The `core:workbench.action.openEditor` command is executed.
5.  A new tab opens with the `CharacterProfileEditor` for "Alex".
6.  The `CharacterProfileEditor` loads Alex's data.
7.  The editor displays its internal navigation panel (left) and the content for the default section (e.g., "Details") in the main area (right), rendered by `CharacterProfileDescription` and potentially other components.
8.  User clicks "Events" in the internal navigation panel.
9.  The content area updates to show the `CharacterProfileEvents` component.
10. User edits information and clicks the main "Save Profile" button in the header.

## 5. Future Enhancements (Not MVP)

- "Peek" view on hover/click within the scene editor.
- Integration with global search.
- Drag-and-drop reordering for events/attributes.
- Visual relationship mapping.
