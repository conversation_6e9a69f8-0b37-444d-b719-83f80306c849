# MVP Character Event System Architecture

## 1. Overview

This document details the MVP implementation for the event-driven character development system within the AI-Books IDE. The focus is on establishing the core data structures and user interactions for manually creating and managing character development events, stored locally in the SQLite database. This system is a key differentiator, providing a structured way to track character arcs even in the MVP.

## 2. Core Principles (MVP Focus)

- **Event-Centric:** Character development is tracked through discrete, significant events defined by the user.
- **Manual Creation:** Users manually create and define events; automatic detection from text is Post-MVP.
- **Timeline Positioning:** Events are placed on a simple 0-100 timeline representing the story's progression.
- **SQLite Storage:** Character profiles and events are stored as structured data in the local SQLite database via `StorageService`.
- **Basic UI:** Provide essential UI for creating, viewing, editing, and deleting characters and their associated events.
- **Scene Linking:** Allow users to link character events to specific scenes for context.

## 3. Components

### 3.1. Data Models (SQLite - Managed by `StorageService`)

- **`characters`:** (As defined in `storage.md`) Stores core profile information (id, book_id, name, role, description, metadata_json).
- **`character_attributes`:** (As defined in `storage.md`) Stores basic key-value attributes.
- **`character_events`:** (As defined in `storage.md`) **Central table for MVP.**
  - `id`: Primary key.
  - `character_id`: Foreign key to `characters`.
  - `book_id`: Foreign key to `books` (for easier querying).
  - `related_scene_id`: Foreign key to `scenes` (nullable, user-set link).
  - `timeline_position`: Integer (0-100), required. Represents approximate position in the narrative.
  - `event_type`: String (e.g., 'Decision', 'Action', 'Revelation', 'Relationship Change', 'Internal Shift'). User selectable from a predefined list.
  - `description`: Text, required. User's description of the event.
  - `impact`: Integer (1-10, optional). User's assessment of the event's significance.
  - `created_at`, `updated_at`: Timestamps.
- **`metadata_versions`:** (As defined in `storage.md`) Used for simple snapshot history of `characters` table entries upon manual save/update.

### 3.2. `CharacterService` (Main Process - Part of `ai-books.characters` extension)

- **Responsibilities:**
  - Handles CRUD operations for `characters`, `character_attributes`, and `character_events` via `StorageService`.
  - Validates input data (e.g., timeline position range).
  - Manages the relationship between characters and events.
  - Triggers creation of metadata snapshots in `VersioningService` when character profiles are updated.
- **API (Internal):** Methods like `createCharacter`, `updateCharacter`, `getCharacterWithEvents(characterId)`, `createCharacterEvent(characterId, eventData)`, `updateCharacterEvent(eventId, eventData)`, `deleteCharacterEvent(eventId)`, `linkEventToScene(eventId, sceneId)`.

### 3.3. IPC Handlers (Main Process - Registered by `ai-books.characters` extension)

- **Responsibilities:** Expose `CharacterService` functionality to the Renderer.
- **Channels (Examples):**
  - `invoke('characters:getAll', { bookId })`: Get list of characters for a book.
  - `invoke('characters:getDetails', { characterId })`: Get character profile and associated events.
  - `invoke('characters:create', { bookId, name, role, ... })`
  - `invoke('characters:update', { characterId, data })`
  - `invoke('characters:delete', { characterId })`
  - `invoke('characters:createEvent', { characterId, eventData })`
  - `invoke('characters:updateEvent', { eventId, eventData })`
  - `invoke('characters:deleteEvent', { eventId })`

### 3.4. UI Components (Renderer Process - Part of `ai-books.characters` extension)

- **`CharacterListView` (Sidebar View):**
  - Displays the list of characters for the active book (fetched via IPC).
  - Allows creating new characters (triggers IPC).
  - Allows selecting a character to open their profile editor.
- **`CharacterProfileEditor` (Editor Tab):**
  - Displays and allows editing of character profile fields (name, role, description, basic attributes). Saves changes via IPC.
  - **Includes an "Events" section/tab:**
    - Displays a list of `character_events` associated with this character (fetched via IPC).
    - Allows creating new events (opens a form/modal, triggers IPC).
    - Allows editing existing events (opens form/modal, triggers IPC).
    - Allows deleting events (triggers IPC).
    - Allows linking an event to the currently open scene (if applicable, requires context from `workbenchStore`).

## 4. Key Workflows (MVP)

- **Creating a Character:** User clicks "New Character" in `CharacterListView` -> UI prompts for name/role -> UI calls `invoke('characters:create', ...)` -> `CharacterService` saves to DB -> List updates.
- **Editing Profile:** User opens `CharacterProfileEditor` -> Modifies fields -> Changes saved via `invoke('characters:update', ...)` -> `CharacterService` updates DB and triggers `VersioningService` snapshot.
- **Adding an Event:** User in `CharacterProfileEditor` clicks "Add Event" -> UI shows form (description, type, impact, timeline position) -> User fills form -> UI calls `invoke('characters:createEvent', ...)` -> `CharacterService` saves event to DB -> Event list in UI updates.
- **Linking Event to Scene:** User views an event in `CharacterProfileEditor` while a scene is open -> User clicks "Link to Current Scene" -> UI gets `activeSceneId` -> UI calls `invoke('characters:updateEvent', { eventId, eventData: { related_scene_id: activeSceneId } })` -> `CharacterService` updates DB.
- **Viewing Events:** `CharacterProfileEditor` loads character details including events via `invoke('characters:getDetails', ...)`.

## 5. AI Integration (MVP)

- The **AI Character Consistency Check** feature (part of `ai-books.ai` or core AI service) will use the character events stored in the database.
- **Workflow:** AI Check triggered -> `AI Service` (Main) requests character profile _and_ relevant `character_events` from `CharacterService` (via internal API or IPC) -> `AI Service` compares scene text against profile traits and event descriptions/types/impacts -> Returns inconsistencies.

## 6. Excluded from MVP

- Automatic detection/suggestion of events from scene text.
- Templates for characters or events.
- Visualization of the timeline or character arcs.
- Advanced development metrics/scoring.
- Relationship mapping (beyond simple attributes).
- UI for directly editing timeline position visually (MVP uses a number input 0-100).
- Integration with Goals/Achievements.
