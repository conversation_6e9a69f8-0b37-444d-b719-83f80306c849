# AI-Books MVP Plan

## MVP Vision

Create a focused, writer-centered platform that leverages AI to enhance the writing process while providing core organizational features that distinguish it from competitors. The MVP will deliver immediate value to fiction writers through integrated structure management, intelligent character development, version control, contextual AI assistance, and basic collaboration, all with reliable offline support.

## Core MVP Features (Refined)

The MVP focuses on delivering a reliable offline-first desktop experience with unique character development tracking and integrated AI analysis.

### 1. Electron Core & Offline-First Storage

**Goal:** Establish a robust, private, offline-capable desktop application foundation. +**Status:** ✅ **Частично** (Ядро Electron, ServiceManager, IPC, базовые сервисы реализованы. `StorageService` использует SQLite. **НО:** `SceneService` некорректно работает с Markdown вместо JSON для сцен).
**Details:**

- Electron application shell, window management, secure IPC (`IpcService`), ServiceManager, AppLifecycle - **Реализовано**.
- Local storage as the primary data source:
  - **SQLite Database:** `StorageService` реализован, использует `better-sqlite3`, WAL. Схемы создаются расширениями (`BookStorage`, `CharacterStorage`).
  - **Scene Content:** **Несоответствие!** Документация требует JSON, но `SceneService` и `BookStorage` реализованы для работы с **Markdown (.md)** файлами. Renderer (`Editor.editor.tsx`) корректно работает с JSON через IPC.
    **USP:** Reliable offline functionality, data privacy, desktop performance.

### 2. Integrated Book Structure Management

**Goal:** Provide core tools for organizing the manuscript. +**Status:** 🚧 **Частично** (CRUD для Книг/Глав/Сцен (метаданные) реализован в `BookStorage`. UI (`BooksExplorerView`) есть. **Секции отсутствуют**).
**Details:**

- Hierarchical structure: Book → Chapter → Scene - **Реализовано**. Уровень **Section** - **Отсутствует**.
- Basic CRUD operations (Create, Read, Update, Delete) for books, chapters, scenes (метаданные) - **Реализовано** в `BookStorage` и через IPC. CRUD для **Sections** - **Отсутствует**.
- Display hierarchy in a sidebar view (`BooksExplorerView`) - **Реализовано**.
- Position management (basic ordering) - **Реализовано** в `BookStorage`.
  **Excludes (Post-MVP):** Templates, drag-and-drop reordering, progress/status tracking, bulk operations.

### 3. Contextual Scene Editor (Lexical)

**Goal:** Enable effective writing and editing of scene content. +**Status:** ✅ **Частично** (Renderer работает с JSON, Main - с Markdown. Ручное сохранение требует проверки).
**Details:**

- Lexical editor integration (`Editor.editor.tsx`) - **Реализовано** в Renderer, **корректно работает с JSON** для загрузки/сохранения через IPC.
- Basic formatting toolbar (`ToolbarPlugin`) - **Реализовано**.
- Reliable autosave mechanism (`AutosavePlugin`) - **Реализовано** в Renderer (сохраняет JSON через IPC), **НО** `SceneService` в Main **некорректно сохраняет как Markdown**.
  **Excludes (Post-MVP):** Distraction-free mode, advanced custom nodes (beyond basic structure), AI annotations in editor UI.

### 4. Event-Driven Character Management (Core)

**Goal:** Implement the unique event-based character development tracking. +**Status:** 🚧 **Частично** (CRUD базовых профилей есть. **События и Атрибуты отсутствуют**).
**Details:**

- Create/Edit character profiles (name, description) stored in SQLite (`CharacterStorage`) - **Реализовано**. Поле `role` и связь с `bookId` отсутствуют.
- **Character Events:** **Отсутствует** (нет таблиц `character_events`, `character_attributes` в `CharacterStorage`, нет логики CRUD/IPC).
- Link characters and events to specific scenes - **Отсутствует**.
- Basic UI for viewing character list (`CharactersView`) and profiles (`CharacterProfileEditor`) - **Реализовано**.
  **USP:** Unique event-driven approach to character development.
  **Excludes (Post-MVP):** Templates, relationship mapping visualization, advanced metrics, timeline visualization UI.

### 5. Reliable Content Versioning (Basic)

**Goal:** Ensure user work is safe and basic history is available. +**Status:** 🚧 **Частично** (Автосохранение сцен некорректно, Git - заглушка, есть логирование метаданных).
**Details:**

- **Scene Content:** Autosave реализован, но **некорректно сохраняет Markdown вместо JSON**. `GitService` - **заглушка**, интеграция с Git не работает. Команда "Save Snapshot" не реализована.
- **Metadata (DB):** Базовый механизм логирования истории (`history_log`) реализован в `BookStorage`.
- Basic UI to view list of versions - **Отсутствует**.
  **Excludes (Post-MVP):** Differential storage, visual diffing, branching/merging (especially for DB data), named checkpoints (beyond manual commit messages).

### 6. Contextual AI Core & Consistency Check

**Goal:** Demonstrate integrated AI analysis leveraging the unique data structure. +**Status:** 🚧 **Частично** (Инфраструктура есть, задача определена, но неработоспособна).
**Details:**

- AI Service infrastructure (`AIService`) в Main Process - **Реализовано** (управление ключами, реестр задач, вызов OpenAI).
- **One Core Feature:** **AI Character Consistency Check**.
  - Задача `consistencyCheckTask` - **Определена**.
  - IPC обработчик (`characters/ipcHandlers`) - **Реализован**, **НО**:
    - Читает **Markdown** вместо JSON.
    - Не может получить **данные о событиях** (т.к. они не хранятся).
  - UI для отображения результатов (`AIResultsView`) - **Зарегистрирован**, но не протестирован с реальными данными.
    **USP:** AI analysis using integrated character event history.
    **Excludes (Post-MVP):** Other AI features (generation, style analysis, suggestions), model management UI, prompt customization.

### 7. Basic Feedback System

**Goal:** Allow simple feedback collection. +**Status:** ❌ **Не начато** (Нет схемы БД, сервисов, UI).
**Details:**

- Ability to select text within the scene editor.
- Add simple text comments associated with the selection (stored in SQLite).
- Basic UI to view comments associated with a scene.
  **Excludes (Post-MVP):** Real-time collaboration, comment threads, resolution status, notifications, user roles beyond owner.

### 8. Command Palette (Core)

**Goal:** Provide basic command discovery and execution.
**Status:** ✅ **Базово Реализовано** (Сервис, UI, IPC есть).
**Details:**

- UI component (`CommandPalette.tsx`) - **Реализовано**.
- Integration with `CommandService` via IPC (`core:commands.execute`, `core:commands.getAll`) - **Реализовано**.
- Basic commands for core actions - **Реализовано**.
  **Excludes (Post-MVP):** Advanced filtering, recently used commands, full command coverage.

### 9. Settings System (Core)

**Goal:** Allow basic application configuration.
**Status:** ✅ **Базово Реализовано** (Сервис, UI, IPC есть).
**Details:**

- `SettingsService` in Main using `electron-store` - **Реализовано**.
- IPC channels for getting/setting values - **Реализовано**.
- Basic `SettingsEditor` UI component in Renderer - **Реализовано**.
- Core settings registered - **Реализовано**.
  **Excludes (Post-MVP):** Workspace settings, complex validation, advanced UI controls.

### 10. Dynamic Workbench & Views (Core)

**Goal:** Provide the main application shell and load basic views.
**Status:** ✅ **Реализовано** (Каркас, `ViewService`, хуки инициализации есть).
**Details:**

- Core Workbench layout components (`Workbench.tsx`, ActivityBar, Sidebar, EditorGroup, Panel, StatusBar) - **Реализовано**.
- `ViewService` for registering views - **Реализовано**.
- `useWorkbenchInitializer` hook to load registered views via IPC - **Реализовано**.
- Dynamic rendering of views in Sidebar/Panel - **Реализовано**.
  **Excludes (Post-MVP):** Split editors, advanced view management, complex panel layouts.

## Phased Implementation

_(This section might need adjustment based on the refined MVP scope, but provides a general direction)_

### Phase 1: Foundation

**Objectives:**

- Establish core architecture and data models (Electron, SQLite, File Structure)
- Implement basic `StorageService` for Books/Chapters/Scenes (metadata)
- Create book structure management (basic CRUD, hierarchy view)
- Develop offline storage architecture foundation

**Key Deliverables:**

1.  Working Electron application shell.
2.  SQLite database setup with initial schema (Books, Chapters, Scenes).
3.  Basic `StorageService` and IPC for book structure.
4.  UI for displaying book hierarchy and creating/deleting elements.

### Phase 2: Content Creation & Characters

**Objectives:**

- Implement Lexical editor for scene Markdown files.
- Implement saving/loading scene content (Markdown files).
- Develop basic Character Management (profiles, events in DB).
- Implement basic versioning (autosave, manual snapshots).

**Key Deliverables:**

1.  Lexical editor integration capable of editing Markdown scene files.
2.  Autosave mechanism for scene files.
3.  `StorageService` and IPC for Character profiles and Events.
4.  UI for creating/editing Characters and Events.
5.  Basic version history tracking (DB snapshots/Git commits).

### Phase 3: AI Integration & Feedback

**Objectives:**

- Implement core AI service infrastructure (Main Process).
- Develop the AI Character Consistency Check feature.
- Implement the basic commenting system.

**Key Deliverables:**

1.  AI Service infrastructure setup.
2.  Working Character Consistency Check feature with UI display.
3.  Commenting functionality within the scene editor.

### Phase 4: Polish & Build

**Objectives:**

- Refine user experience based on core features.
- Implement build and packaging process (Forge/Builder).
- Basic testing (Unit, E2E).

**Key Deliverables:**

1.  Polished UI/UX for MVP features.
2.  Build process for target platforms (macOS, Windows, Linux).
3.  Initial test suite.
4.  Basic documentation.

## Technical Architecture Details

_(Keep existing sections, as the overall Electron/Hybrid storage approach remains)_

### "Бэкенд" (Main Process Electron)

- **Архитектура Main Process**

  - Node.js/TypeScript с разделением на сервисы.
  - Управление окнами и жизненным циклом приложения.
  - **IPC (Inter-Process Communication)** как основной API для Renderer.
  - Обработка фоновых задач (Node.js workers или отдельные процессы).
  - Управление доступом к нативным ресурсам ОС.
  - Координация опциональной синхронизации и AI-операций.

- **Локальное Хранилище Данных**

  - **SQLite** (предпочтительно) или файловая система для хранения данных.
  - Оптимизированные запросы к локальной БД.
  - Управление миграциями схемы SQLite.
  - Локальное версионирование данных.

- **Интеграция AI**

  - Вызовы AI API (OpenAI, Anthropic) выполняются из Main Process.
  - Возможность выполнения некоторых AI задач локально.
  - Управление контекстом для AI-запросов.
  - Обработка и валидация структурированных ответов AI.
  - Кеширование AI-запросов.

- **Опциональный Облачный API**
  - Используется только для синхронизации/коллаборации (если реализуется).
  - RESTful или GraphQL.
  - Аутентификация (например, JWT).

### Фронтенд (Renderer Process Electron)

- **Архитектура React**

  - TypeScript со строгой типизацией.
  - Компонентная структура (например, Atomic Design, feature-based).
  - Zustand для управления состоянием UI.
  - Кастомные хуки для UI-логики и взаимодействия с IPC.

- **Реализация Редактора**

  - Lexical с кастомными узлами и плагинами.
  - Интеграция AI-функций через IPC.
  - Боковая панель для контекстной информации (загружается через IPC).
  - Обработка комментариев (если реализуется).

- **Взаимодействие с Main Process**

  - Использование API, предоставленного через `contextBridge` (`preload.ts`).
  - Вызовы `ipcRenderer.invoke` для запроса данных или выполнения действий.
  - Подписка на события от Main Process через `ipcRenderer.on`.
  - Обработка ошибок, возвращаемых через IPC.

- **UI Компоненты**
  - Библиотека UI компонентов (например, Chakra UI, Material UI или кастомная).
  - Адаптивный дизайн.
  - Поддержка тем оформления (светлая/темная).
  - Соблюдение стандартов доступности (Accessibility).

## Integration Points

_(Keep existing sections, adjust details later if needed based on MVP implementation)_

1. **Editor ↔ Character System**

   - Character references in content
   - Scene-character associations
   - Character sidebar in editor

2. **Editor ↔ Versioning**

   - Automatic version creation during editing
   - Version comparison from editor
   - Version restore within editor context

3. **Character System ↔ Versioning**

   - Character version history
   - Development tracking across versions
   - Version-aware relationship mapping

4. **AI ↔ All Systems**

   - Content analysis using character knowledge
   - Character suggestions based on content
   - Version-aware AI suggestions
   - Context gathering from all systems

5. **Локальное Хранилище (Main) ↔ Все Компоненты (Renderer через IPC)**

   - Все операции с данными идут через Main Process.
   - Renderer запрашивает данные и отправляет изменения через IPC.
   - Main Process уведомляет Renderer об изменениях данных (например, после синхронизации).

6. **Коллаборация (Опционально) ↔ Main Process ↔ Renderer**
   - Логика коллаборации (обработка событий, разрешение конфликтов) находится в Main Process.
   - Main Process обменивается данными с облачным сервером (если есть).
   - Main Process уведомляет Renderer об изменениях от других участников через IPC.

## Post-MVP Roadmap

_(Keep existing sections)_

### Near-term Enhancements (2-3 months post-MVP)

1. **World Building System**

   - Location hierarchy management
   - Cultural systems definition
   - World rules and consistency checking
   - Timeline integration with world events

2. **Enhanced Analytics**

   - Writing patterns analysis
   - Character development metrics
   - Session productivity insights
   - AI impact assessment

3. **Advanced Collaboration**
   - Rich text commenting
   - Reader groups with different access levels
   - Comment analytics and patterns
   - Inline suggestion mode

### Medium-term Expansion (3-6 months post-MVP)

1. **Research Management**

   - Source organization and import
   - Citation tracking
   - Content annotation system
   - AI-assisted research suggestions

2. **Advanced AI Analysis**

   - Narrative structure analysis
   - Emotional arc tracking
   - Genre-specific recommendations
   - Marketing copy generation

3. **Goal & Achievement System**

   - Customizable writing goals
   - Achievement badges
   - Writing streak tracking
   - Productivity analytics

### Long-term Vision (6-12 months post-MVP)

1. **Real-time Collaboration**

   - Concurrent editing with multiple authors
   - Presence awareness
   - Permission management for collaborative writing
   - Activity feeds for team projects

2. **Publishing Tools**

   - Multiple export formats
   - Submission package preparation
   - Self-publishing integration
   - Marketing assistance

3. **Mobile Application**
   - Full-featured mobile experience
   - Offline mobile writing
   - Cross-device workflow
   - Mobile-optimized UI
