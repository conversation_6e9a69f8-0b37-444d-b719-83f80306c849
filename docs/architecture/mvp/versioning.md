# MVP Content Versioning Architecture

## 1. Overview

This document details the MVP implementation for content versioning in the AI-Books IDE. The focus is on providing essential data safety through reliable autosave and basic manual snapshot capabilities for both scene content (Lexical JSON state) and structured metadata (SQLite).

**Note:** This MVP implementation **does not** include advanced features like visual diffing of the JSON state, branching, merging, or full Git integration beyond potentially simple commits of the JSON data.

## 2. Core Principles (MVP Focus)

- **Data Safety:** Prevent accidental data loss during editing.
- **Basic History:** Allow users to view a simple history of changes and potentially revert scene content. Displaying diffs relies on generating Markdown from JSON states.
- **Hybrid Approach:** Apply different mechanisms for JSON scene state and SQLite metadata.
- **User Control:** Provide manual snapshot capability for significant points.
- **Simplicity:** Avoid complex versioning logic for the MVP.

## 3. Components

### 3.1. Scene Content Versioning (Lexical JSON State)

- **Mechanism:** Primarily relies on **reliable autosave** of the JSON state and **manual snapshots** using Git (preferred) or file copies.
- **Autosave (`SceneContentService`, Main Process):**
  - Receives editor state JSON string from `AutosavePlugin` (Renderer) via IPC (`books:saveSceneContent`).
  - Saves/Overwrites the current JSON state (in its file or DB record).
  - **Backup:** A simple backup mechanism for the previous JSON state is recommended before overwriting.
- **Manual Snapshot (`VersioningService`, Main Process):**
  - Triggered by a user command (e.g., "Save Snapshot").
  - **Option A (Basic Git Commit - Preferred):** If a Git repository exists and `GitService` is minimally available, performs `git add [scene_json_file_path_or_export]` and `git commit -m "Snapshot: [Scene Title] - [Timestamp]"`. This leverages Git for storage and history. (If JSON is in DB, it needs to be exported to a file for commit).
  - **Option B (Simple File Copy):** Creates a timestamped copy of the current scene JSON state file (if stored as files) or exports DB content to a versioned file.
- **Diffing (MVP Strategy):**
  - To show diffs, the `VersioningService` retrieves two JSON states (e.g., from Git history).
  - It uses the JSON -> Markdown transformer (from `SceneContentService`) to generate Markdown representations for both versions.
  - It returns these two Markdown strings to the Renderer.
- **UI (`SceneEditor` / `BasicVersionList`, Renderer Process):**
  - Provides the "Save Snapshot" command/button.
  - Displays a simple list of available snapshots (timestamps or commit messages) fetched via IPC (`versioning:getSceneVersions`).
  - Allows selecting two versions to compare:
    - Calls IPC handler (`versioning:getSceneDiffMarkdown`) which returns the two generated Markdown strings.
    - Displays a standard text diff (e.g., using `diff` library) of the generated Markdown.
  - Allows selecting a previous snapshot to view its content (read-only, potentially by loading the JSON state into a read-only editor instance) or revert (replaces current JSON state with the selected snapshot's JSON state from Git/copy).
  - **Future Enhancement:** Replace Markdown diff with a custom JSON diff UI.

### 3.2. Metadata Versioning (SQLite Data)

- **Mechanism:** Simple **snapshots** of relevant database records.
- **`StorageService` (Main Process):**
  - When specific entities (e.g., `characters`, potentially `sections`, `chapters`) are updated via their respective services (e.g., `CharacterService`), the service triggers the `VersioningService`.
- **`VersioningService` (Basic, Main Process):**
  - Receives the entity type (e.g., 'character') and entity ID.
  - Reads the _current_ state of the entity from the database (before the update is saved).
  - Serializes the entity's data (e.g., to JSON).
  - Creates a new record in the `metadata_versions` table (defined in `storage.md`), storing the `entity_type`, `entity_id`, `timestamp`, and the serialized `previous_state` JSON.
- **UI (`CharacterProfileEditor` / `BasicVersionList`, Renderer Process):**
  - Displays a simple list of available snapshots for the entity (fetched via IPC `versioning:getMetadataVersions`).
  - Allows viewing the data stored in a snapshot (read-only).
  - Reverting to a previous state is **Post-MVP** due to complexity in handling relational data changes.

### 3.3. `VersioningService` (Basic, Main Process)

- **Responsibilities (MVP):**
  - `createMetadataSnapshot(entityType, entityId, currentData)`: Saves the `currentData` (JSON) to the `metadata_versions` table. Called _before_ updating the main entity table.
  - `createSceneSnapshot(sceneId, sceneJsonContentOrPath)`: Creates a snapshot of the scene JSON state (either Git commit or file copy).
  - `getMetadataVersions(entityType, entityId)`: Returns a list of timestamps/IDs for available metadata snapshots.
  - `getSceneVersions(sceneId)`: Returns a list of timestamps/commit IDs for available scene JSON snapshots.
  - `getMetadataSnapshotContent(versionId)`: Returns the JSON data of a specific metadata snapshot.
  - `getSceneSnapshotContent(sceneId, versionId)`: Returns the **JSON state** content of a specific scene snapshot (reads from file copy or Git history).
  - `getSceneDiffMarkdown(sceneId, versionId1, versionId2)`: Gets JSON states for version1 and version2, transforms both to Markdown, and returns both Markdown strings.

### 3.4. IPC Handlers (Main Process)

- Expose the necessary `VersioningService` methods to the Renderer via `invoke` channels (e.g., `versioning:getSceneVersions`, `versioning:getSceneSnapshotContent`, etc.).

## 4. Key Workflows (MVP)

- **Autosave Scene:** User edits -> `AutosavePlugin` sends content JSON string -> `SceneContentService` saves JSON state (overwriting previous).
- **Manual Scene Snapshot:** User clicks "Save Snapshot" -> Renderer calls `invoke('versioning:createSceneSnapshot', { sceneId })` -> `VersioningService` creates Git commit of JSON state (or file copy).
- **Saving Character Profile:** User saves changes in `CharacterProfileEditor` -> Renderer calls `invoke('characters:update', ...)` -> `CharacterService` calls `VersioningService.createMetadataSnapshot(...)` _before_ calling `StorageService.updateCharacter(...)`.
- **Viewing Scene History:** User opens history UI -> Renderer calls `invoke('versioning:getSceneVersions', ...)` -> `VersioningService` lists Git commits or file copies -> UI displays list.
- **Comparing Scene Versions (MVP):** User selects two versions -> Renderer calls `invoke('versioning:getSceneDiffMarkdown', ...)` -> `VersioningService` gets both JSON states, converts to MD, returns MD strings -> UI displays text diff of the MD strings.
- **Viewing Metadata History:** User opens history UI for character -> Renderer calls `invoke('versioning:getMetadataVersions', ...)` -> `VersioningService` queries `metadata_versions` table -> UI displays list.

## 5. Excluded from MVP

- Visual Diffing of JSON state (Post-MVP enhancement, replacing Markdown diff).
- Branching and Merging (for both scenes and metadata).
- Reverting metadata changes (Post-MVP due to complexity).
- Advanced Git integration (status view, staging, branching UI).
- Semantic comparison or AI analysis of versions.
- Version pruning or complex storage optimization.
