# MVP Storage Architecture (AI-Books IDE)

## 1. Overview

This document details the storage architecture for the AI-Books MVP, focusing on the **offline-first, hybrid approach** combining a local SQLite database for structured metadata and **serialized editor state (JSON)** for primary writing content (scenes). Markdown serves as an import/export format.

## 2. Core Principles

- **Offline-First:** All essential data resides locally, ensuring full functionality without internet access.
- **Data Ownership:** User data is stored directly on their machine. While the primary scene format is JSON for editor fidelity, Markdown export ensures accessibility.
- **Reliability:** Utilize SQLite's transactional capabilities for structured data integrity and reliable saving of the editor's JSON state.
- **Performance:** Optimize for fast local data access.

## 3. Storage Components

### 3.1. SQLite Database

- **Purpose:** Stores all structured metadata and relational data.
- **Location:** Single file (e.g., `ai_books_data.sqlite`) within the application's user data directory (`app.getPath('userData')`).
- **Management:** Managed exclusively by the `StorageService` in the **Main Process**.
- **Schema (<PERSON>):**
  - `books`: Core book metadata (id, title, description, etc.).
  - `sections`: Section metadata (id, book_id, title, position).
  - `chapters`: Chapter metadata (id, book_id, section_id, title, position).
  - `scenes`: Scene metadata (id, chapter_id, title, position). **Scene content (Lexical JSON state) can be stored either in a dedicated file (path stored here) or directly in a TEXT/JSON column.**
  - `characters`: Character profiles (id, book_id, name, role, description, metadata_json).
  - `character_attributes`: Key-value attributes for characters.
  - `character_events`: Events in a character's development (id, character_id, description, type, impact, timeline_position, related_scene_id).
  - `comments`: User's local comments/annotations (id, scene_id, range_start, range_end, selected_text, comment_text).
  - `settings`: User-specific application settings (managed by `SettingsService` potentially using `electron-store` or this DB).
  - `metadata_versions`: Simple snapshots of structured data (e.g., character profiles) for basic history.
- **Technology:** `better-sqlite3` (recommended for synchronous API in Main Process) or `sqlite3`.
- **Migrations:** Managed using a library like `knex-migrate` or a custom solution, executed by `StorageService` on startup.

### 3.2. Scene Content Storage (Lexical JSON)

- **Purpose:** Stores the primary writing content for each scene, including text, formatting, and embedded metadata (character mentions, etc.), preserving the editor's internal structure.
- **Location:** Either:
  - Dedicated files (e.g., `[ProjectRoot]/scenes/content/scene_id_1.json`) with the path stored in the `scenes` table.
  - OR: Directly within the `scenes` table in a `content_json TEXT` column. (Storing in DB might simplify transactions and backups).
- **Format:** JSON, representing the serialized state of the Lexical editor.
- **Management:** Managed by `SceneContentService` in the **Main Process**.
- **Markdown:** Used only for **import/export** purposes. A reliable JSON <-> Markdown transformer is required.
- **Versioning (MVP):**
  - **Autosave:** `SceneContentService` saves the incoming JSON state (overwriting the previous state in the file or DB record).
  - **Manual Snapshot:** A command triggers the `VersioningService` to create a snapshot of the current JSON state (either as a file copy or a Git commit of the JSON file/data).

### 3.3. `StorageService` (Main Process)

- **Responsibilities:**
  - Manages the SQLite database connection.
  - Executes schema migrations.
  - Provides a typed API for CRUD operations on all SQLite entities (Books, Sections, Chapters, Scenes metadata, Characters, Events, Comments, Settings, Metadata Versions).
  - Handles database transactions for data integrity.
  - Abstracts SQL queries from other services.

### 3.4. `SceneContentService` (Main Process)

- **Responsibilities:**
  - Manages reading and saving the Lexical JSON state for scenes (from/to files or DB).
  - Handles creation/deletion of scene content storage when scenes are added/removed.
  - Implements the scene autosave logic (receiving JSON state from Renderer).
  - Uses a transformer to convert JSON to Markdown for export or diff generation (MVP).
  - Uses a transformer to convert imported Markdown to JSON state.
  - Interacts with `VersioningService` for manual snapshots of the JSON state.

### 3.5. `VersioningService` (Basic, Main Process)

- **Responsibilities (MVP):**
  - Handles the creation of simple snapshots for metadata entities in the SQLite database (`metadata_versions` table) upon request (e.g., saving a character profile).
  - Handles the creation of manual snapshots for scene Markdown files (either file copies or basic Git commits).
  - Provides an API to list available versions (timestamps/names) for scenes and metadata entities.

## 4. Data Relationships and Flow

- The SQLite database acts as the central index and metadata store.
- The `scenes` table links metadata to the scene's content (either via a file path to the JSON or by storing the JSON directly).
- The `character_events` table links events to characters (`character_id`) and optionally to scenes (`related_scene_id`).
- The `comments` table links comments to scenes (`scene_id`) and specific text ranges.
- Renderer requests data via IPC -> Main process IPC handlers call appropriate services (`StorageService`, `SceneContentService`, `VersioningService`, `CommentService`) -> Services interact with SQLite DB or Markdown files -> Data returned to Renderer.

## 5. Advantages for MVP

- **Offline-First:** Works entirely locally.
- **Data Ownership:** User retains control over their data. JSON state can be exported to Markdown.
- **Simplicity:** Avoids complex synchronization logic needed if storing Markdown + separate metadata.
- **Performance:** Local SQLite and JSON state access are generally fast.
- **Foundation:** Establishes clear separation for future enhancements (cloud sync, advanced versioning).
