# MVP Basic Comment System Architecture

## 1. Overview

This document details the MVP implementation for the basic commenting system in AI-Books IDE. This system allows a **single user** to add, view, and delete **local comments** associated with specific text ranges within scene content.

**Note:** This MVP implementation **does not** include features for multiple users, real-time updates, comment threads, resolution status, or cloud synchronization. It serves as a personal annotation tool for the writer.

## 2. Core Principles (MVP Focus)

- **Local Storage:** Comments are stored in the local SQLite database.
- **Single User:** Designed for the individual writer's notes and feedback to themselves.
- **Text Anchoring:** Comments are linked to specific text selections within scene Markdown files.
- **Basic CRUD:** Allow creating, viewing, and deleting comments.
- **Simple UI:** Provide straightforward ways to add and view comments.

## 3. Components

### 3.1. Data Model (SQLite - Managed by `StorageService`)

- **`comments` Table:** (As defined in `storage.md`)

  - `id`: Primary key.
  - `scene_id`: Foreign key to `scenes`.
  - `book_id`: Foreign key to `books`.
  - `range_start`: Start position/offset of the text selection. (Need a robust way to handle text changes - simple offset is fragile).
  - `range_end`: End position/offset.
  - `selected_text`: The actual text snippet the comment refers to (for context, helps if range breaks).
  - `comment_text`: The content of the user's comment.
  - `status`: Default 'active' (future use: 'resolved').
  - `created_at`, `updated_at`: Timestamps.

  _Self-Correction:_ Simple character offsets (`range_start`, `range_end`) are very fragile and will break easily with edits. A better MVP approach might be to store the `selected_text` and perhaps the surrounding context, and rely on searching for this text to re-anchor the comment visually, or use Lexical's node keys if possible and stable enough for persistence. For MVP, storing `selected_text` and relying on visual cues/search might be sufficient, acknowledging the limitation. A truly robust solution (like using diff/patch or stable identifiers) is Post-MVP. Let's proceed assuming we store `selected_text` and potentially use simple offsets for initial positioning, accepting the fragility for MVP.

### 3.2. `CommentService` (Basic, Main Process)

- **Responsibilities:**
  - Handles CRUD operations for `comments` via `StorageService`.
  - Validates comment data.
- **API (Internal):** Methods like `createComment(commentData)`, `getCommentsForScene(sceneId)`, `deleteComment(commentId)`.

### 3.3. IPC Handlers (Main Process)

- **Responsibilities:** Expose `CommentService` functionality to the Renderer.
- **Channels (Examples):**
  - `invoke('comments:getForScene', { sceneId })`
  - `invoke('comments:create', { sceneId, rangeStart?, rangeEnd?, selectedText, commentText })`
  - `invoke('comments:delete', { commentId })`

### 3.4. Editor Integration (`CommentPlugin`, Lexical Plugin, Renderer Process)

- **Responsibilities:**
  - Provide UI action (e.g., context menu item, button) to create a comment based on the current text selection.
  - Capture `selectedText` and potentially range offsets from the editor state.
  - Open a small UI (e.g., popover, sidebar input) for the user to enter `commentText`.
  - Call `invoke('comments:create', ...)` to save the comment.
  - **Visual Indication:** Use Lexical Decorator Nodes or text formatting (e.g., background color) to highlight text ranges that have associated comments. The highlighting logic would fetch comments for the scene and apply decorators based on matching `selected_text` or approximate range.
  - On clicking/hovering over highlighted text, potentially show the comment text in a tooltip or trigger opening the `CommentView` panel focused on that comment.

### 3.5. `CommentView` (Panel View, Renderer Process)

- **Responsibilities:** Display the list of comments for the currently active scene.
- **Implementation:**
  - Fetches comments via `invoke('comments:getForScene', ...)` when the active scene changes.
  - Displays each comment, including `selected_text` (for context) and `comment_text`.
  - Provides a button to delete a comment (calls `invoke('comments:delete', ...)`).
  - Clicking a comment could attempt to highlight/scroll to the corresponding text in the editor (using `selected_text` for searching or the potentially fragile range offsets).

## 4. Key Workflows (MVP)

- **Adding a Comment:** User selects text -> User triggers "Add Comment" -> UI prompts for comment text -> User confirms -> `CommentPlugin` calls `invoke('comments:create', ...)` -> `CommentService` saves to DB -> `CommentView` refreshes, Editor highlights text.
- **Viewing Comments:** User opens scene -> `CommentView` calls `invoke('comments:getForScene', ...)` -> `CommentService` retrieves from DB -> `CommentView` displays list. Editor highlights commented text ranges.
- **Deleting a Comment:** User clicks "Delete" on a comment in `CommentView` -> UI calls `invoke('comments:delete', ...)` -> `CommentService` deletes from DB -> `CommentView` refreshes, Editor removes highlight.
- **Navigating to Comment:** User clicks comment in `CommentView` -> `CommentView` attempts to find `selected_text` in editor and scroll/highlight.

## 5. Excluded from MVP

- Multiple users / Sharing comments.
- Real-time updates.
- Comment threads / replies.
- Comment resolution status workflows.
- Advanced text anchoring robust to major edits.
- Notifications.
- Assigning comments.
- Filtering/sorting comments.
