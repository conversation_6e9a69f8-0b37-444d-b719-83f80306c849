# MVP AI System Architecture

## 1. Overview

This document details the MVP architecture for the AI Management System in AI-Books IDE. The primary goal for the MVP is
to establish the core infrastructure in the Main Process for interacting with external AI APIs (starting with OpenAI)
and to implement **one specific, high-value feature**: the **AI Character Consistency Check**. This feature demonstrates
the value of integrated, context-aware AI analysis based on the unique event-driven character model.

**Note:** This MVP implementation focuses solely on the infrastructure and the single consistency check feature. Other
AI capabilities (text generation, style analysis, plot suggestions, etc.), local model support, and response streaming
are considered **Post-MVP**, although stubs or considerations for them may be included in the core service.

## 2. Core Principles (MVP Focus)

- **Centralized Core Service:** AI logic (API calls, orchestration, context gathering) resides in a core `AIService`
  within the Main Process.
- **API First:** Initial implementation relies on external AI APIs (OpenAI). Architecture allows for future local model
  integration.
- **Extensibility via Task Registration:** Extensions define and register specific "AI Tasks", providing necessary
  details like prompts and output schemas directly.
- **Structured Output:** AI responses are requested and validated against JSON Schemas provided by the registering
  extension.
- **Context-Aware:** The core `AIService` gathers necessary context from other application parts based on task
  requirements before calling the AI.
- **User-Triggered:** AI features in MVP are initiated manually by the user.
- **Full Response (No Streaming for MVP):** MVP returns the complete analysis result. Streaming is a Post-MVP feature.

## 3. Components (Main Process)

### 3.1. `AIService` (Core Service)

- **Responsibilities:**
    - Acts as the central orchestrator for all AI interactions.
    - Manages secure storage and retrieval of API keys (via `SettingsService`).
    - Instantiates and manages `AIProviderClient` implementations (initially `OpenAIClient`).
    - Maintains a registry of defined "AI Tasks" registered by extensions (see 3.2).
    - Handles incoming IPC requests to run specific AI tasks (e.g., `ai:runTask`).
    - Collaborates with other services (`StorageService`, `SceneContentService`) and potentially extension APIs (
      `context.extensions.getApi`) to gather the necessary context based on the registered task's requirements.
    - Retrieves the prompt template string and output schema directly from the registered task definition.
    - Formats the final prompt using the template string and gathered context.
    - Calls the appropriate `AIProviderClient` to execute the request against the external AI API, passing the prompt
      and the JSON Schema provided in the task definition to request structured output.
    - Receives the response (expected to be JSON).
    - Validates the response against the registered JSON Schema using `ajv`.
    - Returns the validated, structured result (or a standardized `IpcError`) back to the Renderer via the IPC `invoke`
      promise.
    - **(Stub/Placeholder):** May include placeholder methods or interfaces for future streaming capabilities (e.g.,
      `runStreamingTask`).
- **Location:** `src/main/services/ai.service.ts` (Proposed)

### 3.2. AI Task Registry & Definition (Managed by `AIService`)

- **Responsibilities:** `AIService` maintains an internal registry of tasks defined by extensions.
- **Task Definition (`AITaskDefinition` - Interface):**

  ```typescript
  import { JSONSchema7 } from "json-schema";

  interface AITaskDefinition {
    taskId: string; // e.g., 'characters:consistencyCheck' (Must be unique)
    description: string; // For potential UI or logging
    promptTemplate: string; // The actual prompt template string with placeholders (e.g., {{sceneContent}})
    outputSchema: JSONSchema7; // The JSON Schema object for the expected output
    requiredContext?: string[]; // Optional: Hints for AIService on context keys needed
    // Optional: Function provided by extension to gather specific context? (More complex, maybe Post-MVP)
  }
  ```

- **Registration:** Extensions use `context.ai.registerTask(definition: AITaskDefinition): Disposable` during
  activation. The `AIService` stores these definitions.

### 3.3. `AIProviderClient` Interface & Implementations

- **Interface (`AIProviderClient`):** Defines a standard interface for interacting with different AI providers.

  ```typescript
  interface AIProviderClient {
    generateStructuredOutput(
      prompt: string,
      outputSchema: JSONSchema7,
      options?: AIProviderOptions
    ): Promise<any>;
    // (Stub/Placeholder): streamOutput?(prompt: string, options?: AIProviderOptions): AsyncGenerator<string, void, unknown>;
    // ... other potential methods
  }

  interface AIProviderOptions {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    // ... other provider-specific options
  }
  ```

- **`OpenAIClient` (MVP Implementation):**
    - Implements `AIProviderClient`.
    - Handles communication with OpenAI Chat Completions API.
    - Uses `response_format: { type: "json_object" }` or `tools` (function calling) to request structured JSON based on
      the provided `outputSchema`.
    - Includes basic error handling and retry logic.
- **`(Future) LocalModelClient`:** Placeholder for future integration with local models (e.g., via Ollama, llama.cpp).
- **Location:** `src/main/ai/providers/` (Proposed)

### 3.4. Extension Integration (`ExtensionContext` API)

- **Responsibilities:** Allow extensions to register their AI capabilities directly.
- **API (Provided to `activate` function):**
    - `context.ai.registerTask(definition: AITaskDefinition): Disposable`: The primary method for an extension to make
      its AI feature known to the core `AIService`. The extension provides everything needed: ID, prompt, and schema.

## 4. Data Flow & Communication (MVP: Character Consistency Check)

1. **Registration (Startup):** `ai-books.characters` extension calls `context.ai.registerTask` with the definition for
   `characters:consistencyCheck`, including the prompt template string and the JSON Schema object for the expected
   output.
2. **Trigger (Renderer):** User initiates check for Character X in Scene Y.
3. **IPC Invoke (Renderer -> Main):**
   `window.electronAPI.invoke('ai:runTask', { taskId: 'characters:consistencyCheck', context: { sceneId: 'sceneY', characterId: 'charX' } })`
4. **Task Lookup (Main - `AIService`):** `AIService` finds the registered definition for `characters:consistencyCheck`.
5. **Context Gathering (Main - `AIService`):** Based on `requiredContext` (or internal logic), `AIService` fetches scene
   text and character/event data from `SceneContentService` and `CharacterService`.
6. **Prompt Formatting (Main - `AIService`):** Populates the `promptTemplate` string from the task definition with the
   gathered context.
7. **API Call (Main - `AIService` -> `OpenAIClient`):** Calls
   `OpenAIClient.generateStructuredOutput(formattedPrompt, outputSchemaFromTaskDef)`.
8. **Response & Validation (Main - `OpenAIClient` -> `AIService`):** Client gets response, `AIService` validates the
   JSON against the `outputSchema` from the task definition.
9. **IPC Response (Main -> Renderer):** `AIService` returns the validated JSON array (or error) via the `invoke`
   promise.
10. **Display (Renderer):** `AIResultsView` receives the data and renders the list.

## 5. UI Integration (MVP)

- A simple, generic view component (e.g., `AIResultsView`) is added to the `Panel`.
- This view receives the structured JSON result from the `ai:runTask` IPC call.
- It renders the results in a human-readable format (e.g., a list of detected inconsistencies with explanations).
- No complex rendering based on schema introspection in MVP. Extensions do not provide custom UI for results in the core
  panel.

## 6. Excluded from MVP

- Support for multiple AI providers or local models (beyond basic architecture considerations).
- Streaming responses.
- AI text generation features.
- AI analysis beyond character consistency.
- UI for managing prompts, models, or AI settings (beyond basic API key).
- Fine-tuning models.
- Automatic background AI analysis.
- Extension-provided UI for results.
- Central `PromptTemplateService` (prompts/schemas registered directly by extensions).
