# MVP AI Character Consistency Check Architecture

## 1. Overview

This document details the MVP implementation of the AI-powered Character Consistency Check feature in AI-Books IDE. This is the **only** AI feature included in the MVP and serves to demonstrate the value of integrated AI analysis based on the unique event-driven character model.

## 2. Core Principles (MVP Focus)

- **Focused Scope:** Check only for inconsistencies between selected scene text and the character's established profile/event history.
- **Data Integration:** Leverage both character profile data and character event data stored in SQLite.
- **Contextual Analysis:** Analyze the scene text in the context of the specific character(s) present and their known history up to that point in the narrative timeline.
- **Clear Output:** Present potential inconsistencies as clear, actionable warnings or suggestions, not definitive errors.
- **User Triggered:** The check is initiated manually by the user for a selected scene or text block, not running constantly in the background.

## 3. Components

### 3.1. `AI Service` (Basic, Main Process)

- **Responsibilities (MVP):**
  - Manage API Key(s) for external AI providers (e.g., OpenAI, Anthropic) securely.
  - Provide a basic orchestration function `runConsistencyCheck(sceneId, characterId, selectedText?)`.
  - Construct the prompt for the AI model, including relevant context.
  - Call the external AI API.
  - Receive and perform basic validation/parsing of the AI response (expecting a list of potential inconsistencies).
  - Handle API errors gracefully.
- **Technology:** Node.js/TypeScript. Uses libraries like `openai` or `@anthropic-ai/sdk`.

### 3.2. Context Gathering (within `AI Service` or helper)

- **Responsibilities:** Collect necessary data for the AI prompt.
- **Data Sources:**
  - `SceneContentService`: Get the Markdown text of the target scene (or selected portion).
  - `StorageService` (via `CharacterService`):
    - Get the profile data for the relevant character(s) (`characters`, `character_attributes`).
    - Get the relevant `character_events` for the character(s) _up to the scene's approximate timeline position_.
- **Process:**
  1.  Receive `sceneId`, `characterId`, `selectedText?`.
  2.  Determine the scene's `timeline_position` from `scenes` metadata in SQLite.
  3.  Fetch character profile data.
  4.  Fetch character events where `timeline_position <= scene_timeline_position`.
  5.  Fetch scene content (Markdown).
  6.  Format this data concisely for the AI prompt.

### 3.3. Prompt Construction (within `AI Service`)

- **Goal:** Instruct the AI to compare the scene text against the provided character profile and event history, looking for contradictions or inconsistencies.
- **Key Elements:**
  - **Role:** "You are a meticulous editor checking for character consistency."
  - **Context:** Provide character name, key traits (from profile), and a summary of relevant past events (descriptions and types).
  - **Text:** Provide the scene text (or selection).
  - **Task:** "Analyze the provided scene text. Identify any actions, dialogue, or internal thoughts of [Character Name] that seem inconsistent with their established traits or past events listed in the context. For each potential inconsistency, describe it briefly and explain why it seems inconsistent based on the provided context."
  - **Output Format:** Request a structured output (e.g., JSON array of inconsistency objects) using function calling or explicit JSON instructions (see `docs/development/structered-output.md`).
    ```json
    [
      {
        "inconsistent_text": "...", // Snippet from scene text
        "explanation": "...", // Why it's inconsistent with profile/events
        "context_violated": "Trait: Cautious / Event: Previous Trauma", // Which part of context it contradicts
        "severity": "medium" // e.g., low, medium, high
      }
    ]
    ```

### 3.4. IPC Handlers (Main Process)

- **Channel:** `invoke('ai:runCharacterConsistencyCheck', { sceneId, characterId, selectedText? })`
- **Handler:**
  - Receives the request from Renderer.
  - Calls `AIService.runConsistencyCheck(...)`.
  - Returns the structured list of potential inconsistencies (or an error object) to the Renderer.

### 3.5. UI Components (Renderer Process)

- **Trigger:** Button or context menu option in `SceneEditor` or a dedicated `AIResultsView` panel.
- **`AIResultsView` (Panel View):**
  - Displays a loading state while the check runs.
  - Renders the list of potential inconsistencies returned from the Main process.
  - Highlights the `inconsistent_text` snippet.
  - Shows the `explanation` and `context_violated`.
  - Allows the user to dismiss warnings or potentially navigate to the relevant text in the editor.

## 4. Key Workflow (MVP)

1.  **User Action (Renderer):** User selects text in `SceneEditor` (optional) and clicks "Check Character Consistency" for a specific character present in the scene.
2.  **IPC Request (Renderer -> Main):** UI calls `invoke('ai:runCharacterConsistencyCheck', { sceneId, characterId, selectedText })`.
3.  **Context Gathering (Main):** `AI Service` fetches scene text (Markdown) and relevant character profile/event data (SQLite).
4.  **Prompt Construction (Main):** `AI Service` builds the prompt with context and instructions.
5.  **AI API Call (Main):** `AI Service` sends the request to the external AI provider (e.g., OpenAI).
6.  **Response Processing (Main):** `AI Service` receives the response, validates the structured output (JSON).
7.  **IPC Response (Main -> Renderer):** `AI Service` returns the list of potential inconsistencies (or error) via the `invoke` promise.
8.  **Display Results (Renderer):** `AIResultsView` receives the data and displays the list of potential inconsistencies to the user.

## 5. Excluded from MVP

- Automatic background consistency checking.
- Analysis of elements other than character consistency (plot, style, etc.).
- AI-powered suggestions for _fixing_ inconsistencies.
- Direct AI generation or rewriting features.
- Fine-tuning models or complex prompt management UI.
- Analysis based on World Building or other non-character metadata.
