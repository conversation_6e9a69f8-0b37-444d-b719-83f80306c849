# MVP Scene Editor Architecture (Lexical)

## 1. Overview

This document details the MVP implementation of the scene editor within the AI-Books IDE, based on the Lexical framework. The focus is on providing a reliable core writing experience with basic formatting and integration with the hybrid storage model.

## 2. Core Principles (MVP Focus)

- **Core Editing:** Provide essential rich text editing capabilities (bold, italic, lists).
- **JSON State Storage:** Use serialized Lexical editor state (JSON) as the primary persistent storage format for scene content, ensuring fidelity of text, formatting, and future custom nodes. Markdown serves as an import/export format.
- **Lexical Foundation:** Leverage Lexical for the editor's internal state management and rendering.
- **Autosave:** Implement reliable automatic saving of the scene's JSON state.
- **Performance:** Ensure smooth performance for typical scene lengths.

## 3. Components

### 3.1. `SceneEditor` (React Component, Renderer Process)

- **Location:** `src/extensions/books/renderer/editors/SceneEditor.editor.tsx` (or similar).
- **Responsibilities:**
  - Acts as the main container for the Lexical editor instance for a scene.
  - Initializes `LexicalComposer` with the appropriate configuration.
  - Loads initial scene content (JSON state fetched via Main process).
  - Renders the editor UI, including the content area and a basic toolbar.
  - Integrates necessary Lexical plugins.
- **Registration:** Registered as an editor provider via `EditorService` by the `ai-books.books` extension.

### 3.2. Lexical Configuration (MVP Scope)

- **`initialConfig`:**
  - `namespace`: Unique identifier (e.g., "AI-BooksSceneEditor").
  - `theme`: Basic theme defining CSS classes for formatting (bold, italic, etc.).
  - `onError`: Basic error handling callback.
  - `editorState`: Initial state loaded from the persisted JSON state (see Data Flow).
  - `nodes`: Register core Lexical nodes (`ParagraphNode`, `TextNode`, `ListNode`, `ListItemNode`, `HeadingNode` - basic headings h1-h3). **Exclude** complex custom nodes (like character mentions) for MVP, but the JSON format allows adding them later without breaking storage.
- **Core Plugins:**
  - `RichTextPlugin`: Enables rich text editing capabilities.
  - `ContentEditable`: The actual editable area.
  - `HistoryPlugin`: Manages undo/redo history.
  - `AutoFocusPlugin` (Optional): Focuses the editor on load.
  - `MarkdownShortcutPlugin` (Optional/Low Priority): Less critical now that JSON is primary. May still be useful for user convenience.
  - **`AutosavePlugin` (Custom):** Handles periodic saving of the JSON state (see below).
  - **`ImportExportPlugin` (Conceptual/Part of `SceneEditor`):** Handles triggering import/export operations.

### 3.3. JSON State and Markdown Transformation

- **Primary Storage Format:** Serialized Lexical EditorState (JSON) is the source of truth for scene content. Stored as described in `storage.md` (file or DB).
- **Markdown Role:** Used for **import/export only**.
- **Loading:** When a scene is opened, `SceneContentService` (Main) reads the JSON state. The JSON string is sent to the Renderer. `SceneEditor` parses this JSON to create the initial `EditorState`. (If importing Markdown, `SceneContentService` uses a transformer to convert MD to JSON first).
- **Saving:** The `AutosavePlugin` (Renderer) gets the current `EditorState`, serializes it to JSON, and sends the JSON string via IPC to `SceneContentService` (Main). `SceneContentService` saves this JSON state.
- **Transformers (JSON <-> Markdown):** A reliable transformer (e.g., using `@lexical/markdown` utilities) is needed in the **Main Process** (within `SceneContentService`) to handle:
  - Importing Markdown content into Lexical JSON state.
  - Exporting Lexical JSON state to Markdown format.
  - Generating Markdown from JSON state for version diffing (MVP versioning strategy).

### 3.4. `AutosavePlugin` (Custom Lexical Plugin, Renderer Process)

- **Responsibilities:**
  - Listens for changes in the Lexical `EditorState`.
  - Uses `debounce` to trigger saving after a period of inactivity (e.g., 2-5 seconds).
  - On trigger, gets the current `EditorState`, serializes it to JSON.
  - Calls the IPC handler `invoke('books:saveSceneContent', { sceneId, contentJsonString })` to send the **JSON state** to the Main process for saving.
  - Handles success/error responses from the IPC call (e.g., updating a status indicator).

### 3.5. Basic Formatting Toolbar (React Component, Renderer Process)

- **Responsibilities:** Provides UI buttons for basic formatting.
- **Implementation:**
  - Simple React component rendered above or near the `ContentEditable` area.
  - Uses Lexical command API (`editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold')`, etc.) to apply formatting.
  - Listens to editor state changes to update button states (e.g., show 'bold' as active if selection is bold).
  - Includes buttons for: Bold, Italic, Unordered List, Ordered List, Undo, Redo.

## 4. Key Workflows (MVP)

- **Loading a Scene:** `ActiveEditorArea` mounts `SceneEditor` -> `SceneEditor` requests content via IPC -> `SceneContentService` reads JSON state -> Main process sends JSON string -> `SceneEditor` parses JSON to create initial `EditorState` -> Lexical renders content.
- **Editing Text:** User types/formats -> Lexical updates `EditorState`.
- **Autosaving:** User stops typing -> `AutosavePlugin` debounce timer fires -> Plugin gets `EditorState` JSON -> Plugin sends JSON string via IPC -> `SceneContentService` saves the JSON state.
- **Manual Save (Ctrl+S):** Triggers `editor.saveActive` command -> Main sends `core:editor.requestSave` event -> `SceneEditor` (or `AutosavePlugin`) immediately triggers the save IPC call (sending JSON) without debounce.
- **Import Markdown:** User initiates import -> Renderer sends MD content via IPC -> `SceneContentService` transforms MD to JSON -> `SceneContentService` saves JSON state -> Editor reloads with new state.
- **Export Markdown:** User initiates export -> Renderer requests export via IPC -> `SceneContentService` reads JSON state -> `SceneContentService` transforms JSON to MD -> Main process returns MD string or saves to file.

## 5. Excluded from MVP

- Advanced formatting (tables, code blocks, blockquotes, advanced headings, font size/color) - though easier to add later with JSON storage.
- Custom Lexical nodes (character mentions, location tags, AI annotations) - **MVP excludes implementation, but JSON storage makes adding them later feasible without storage format changes.**
- Side panel integration for context (characters, locations, research).
- Distraction-free mode.
- Inline commenting UI (comment _data_ is stored, but editor integration is basic).
- Integration with IntelliSense, Snippets, advanced Analysis Servers.
- Real-time collaboration features (cursors, presence, operational transforms).
