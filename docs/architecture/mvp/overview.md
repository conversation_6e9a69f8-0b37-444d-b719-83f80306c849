# MVP Architecture Overview (AI-Books IDE)

## 1. Introduction

This document outlines the technical architecture specifically for the **Minimum Viable Product (MVP)** of the AI-Books IDE. It focuses on the core components and interactions necessary to deliver the initial value proposition: a reliable, offline-first desktop writing environment with unique character development tracking and integrated AI consistency checking.

This MVP architecture serves as the foundation for future development, prioritizing stability, core functionality, and key differentiators. Features described in the main `docs/architecture/` and `docs/core-systems/` documentation that are not mentioned here are considered **Post-MVP**.

## 2. Core Principles (MVP Focus)

- **Offline-First Desktop App:** All core functionality must work reliably without an internet connection. Data is stored locally.
- **Hybrid Storage:** Structured data (metadata, characters, events, comments) in SQLite; Scene content as serialized editor state (JSON). Markdown is for import/export.
- **Modular (Internal Extensions):** Key features (Book Management, Character Management) implemented as internal extensions interacting via a core API.
- **Event-Driven Characters:** Character development tracked via user-created events linked to the timeline and scenes.
- **Focused AI:** Demonstrate AI value through a single, integrated analysis feature (Character Consistency Check) leveraging the unique data structure.
- **Basic Collaboration:** Local commenting system for user's own notes/feedback.

## 3. Key MVP Components

### 3.1. Main Process (Electron Core)

- **Responsibilities:** Application lifecycle, window management, core service hosting, IPC handling, file system access, SQLite database access.
- **Core Services (MVP Scope):**
  - `ServiceManager`: Loads core services.
  - `WindowManager`: Manages the main application window.
  - `StorageService`: Manages SQLite database (schema, migrations, CRUD for MVP entities).
  - `SceneContentService` (or similar): Manages reading/saving scene JSON state and transforming JSON <-> Markdown.
  - `CommandService`: Registers and executes commands (core and basic extension commands).
  - `SettingsService`: Manages basic application settings (`electron-store`).
  - `IpcService` (or direct `ipcMain` usage): Handles communication with Renderer.
  - `ExtensionManager`: Loads internal MVP extensions (`ai-books.books`, `ai-books.characters`).
  - `AI Service` (Basic): Infrastructure for API key handling and orchestrating the single MVP AI feature.
  - `CommentService` (Basic): Manages CRUD for local comments in SQLite.
  - `VersioningService` (Basic): Handles creation of simple metadata snapshots in SQLite and potentially basic Git commands for scene files.

### 3.2. Renderer Process (React UI)

- **Responsibilities:** Rendering the user interface, handling user input, managing UI state, communicating with Main process via IPC.
- **Core UI Components (MVP Scope):**
  - `Workbench`: Main layout (Activity Bar, Sidebar, Editor Group, Panel, Status Bar).
  - `Sidebar Views`:
    - `BookStructureView`: Displays Book/Section/Chapter/Scene hierarchy (data from Main).
    - `CharacterListView`: Displays list of characters (data from Main).
  - `Editor Group`: Manages tabs for open editors.
  - `ActiveEditorArea`: Dynamically loads and renders the active editor.
  - `SceneEditor (Lexical)`: Edits scene content (working with JSON state) with basic formatting toolbar.
  - `CharacterProfileEditor`: Displays/Edits basic character info and _manages character events_.
  - `Panel Views`:
    - `CommentView`: Displays comments for the active scene.
    - `AIResultsView` (or similar): Displays results from the Character Consistency Check.
  - `CommandPalette`: Basic command execution UI.
  - `SettingsEditor`: Basic UI for viewing/editing settings.
- **State Management:** Zustand (`workbenchStore` for UI layout, open tabs, active elements; potentially separate stores for Characters, Comments).

### 3.3. Preload Script

- **Responsibilities:** Securely exposes necessary IPC functions (`invoke`, `on`, `send`) from Main to Renderer via `contextBridge` (e.g., `window.electronAPI`).

### 3.4. Internal Extensions (MVP Scope)

- **`ai-books.books`:**
  - **Main:** Registers commands/IPC handlers for Book/Section/Chapter/Scene CRUD (interacting with `StorageService` and `SceneContentService`). Registers `BookStructureView`. Registers `SceneEditor` provider.
  - **Renderer:** Provides `BookStructureView` and `SceneEditor` components.
- **`ai-books.characters`:**
  - **Main:** Registers commands/IPC handlers for Character/Event CRUD (interacting with `StorageService`). Registers `CharacterListView`. Registers `CharacterProfileEditor` provider. Provides `AnalysisProvider` for Character Consistency Check.
  - **Renderer:** Provides `CharacterListView` and `CharacterProfileEditor` components.

## 4. Key Interactions (MVP)

- **Loading Data:** UI components request data (book structure, character list, scene JSON state, comments) from Main via `invoke`.
- **Saving Data:** Editor autosaves scene JSON state via `invoke`. User actions (creating/editing characters, events, comments, structure) trigger `invoke` calls to Main.
- **AI Check:** User triggers Character Consistency Check -> Renderer sends `invoke` to Main -> `AI Service` gets scene JSON state, converts to text/Markdown, gets character/event data (SQLite) -> AI Service performs check -> Main returns results -> Renderer displays results.
- **Versioning:** Autosave updates scene JSON state. Manual "Save Snapshot" command triggers Git commit of JSON state (or file copy) and/or `StorageService` snapshot creation for metadata. Diffing in MVP uses generated Markdown.

## 5. Excluded from MVP

- World Building System
- Goals & Achievements System
- Idea & Research Management System
- Advanced Collaboration (Real-time, Sharing, Roles beyond owner)
- Advanced AI Features (Generation, Style/Plot Analysis, etc.)
- Advanced Versioning (Visual JSON Diffing, Branching, Merging)
- Templates (Book, Character)
- Advanced Search
- Task Runner, Snippets, IntelliSense (beyond basic word completion)
- Cloud Sync / Multi-Device Support

This overview provides a focused look at the essential components and interactions for the AI-Books MVP. Detailed specifications for each component will be documented in separate files within this `docs/architecture/mvp/` directory.
