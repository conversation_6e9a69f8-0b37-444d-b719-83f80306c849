# Settings System Refactoring Plan: Central Configuration Service

This plan outlines the steps to refactor the application's settings system, focusing on introducing a central, type-safe `ConfigurationService` inspired by VS Code's architecture.

**Goal:** Address scattered settings access, weak typing, and improve maintainability by creating a single source of truth for configuration management accessible from both main and renderer processes.

**Phase 1: Establish the Core Service**

1.  **Define `IConfigurationService` Interface (Shared):**

    - Create `src/shared/types/configuration.ts`.
    - Define an interface `IConfigurationService` with core methods:
      - `getValue<T>(key: string, defaultValue?: T): T` - Gets a configuration value, strongly typed.
      - `updateValue(key: string, value: any): Promise<void>` - Updates a configuration value (initially user scope).
      - `onDidChangeConfiguration: Event<{ key: string; newValue: any }>` - An event emitter for configuration changes.
      - _(Optional but recommended)_ `inspect<T>(key: string): ConfigurationInspect<T>` - Provides detailed info about a setting's value, default, and potential scope overrides (useful later).

2.  **Implement Main Process `ConfigurationService`:**

    - Create `src/main/services/configuration.service.ts`.
    - This service will replace the core logic currently in `SettingsService` related to getting/setting values and managing declarations.
    - It will implement `IConfigurationService`.
    - **Internal Logic:**
      - Load setting declarations (initially similar to how `SettingsService` does, maybe from a registry).
      - Use `electron-store` (or a similar mechanism) for persistence.
      - Implement `getValue` to retrieve values from the store, falling back to defaults from declarations.
      - Implement `updateValue` to validate against the declaration and persist to the store.
      - Implement the `onDidChangeConfiguration` event emitter, firing when `updateValue` successfully changes a setting.
    - **IPC:** Expose `getValue` and `updateValue` via dedicated IPC channels (e.g., `CONFIGURATION_GET_VALUE`, `CONFIGURATION_UPDATE_VALUE`). Listen for renderer requests. Broadcast changes via a new IPC channel (e.g., `CONFIGURATION_DID_CHANGE`).

3.  **Implement Renderer Process `ConfigurationService` (Proxy):**
    - Create `src/renderer/core/services/configuration.service.ts`.
    - Implement `IConfigurationService`.
    - **Internal Logic:**
      - Hold a cache of configuration values.
      - Implement `getValue` to first check the cache, then use IPC (`CONFIGURATION_GET_VALUE`) to ask the main process if not cached.
      - Implement `updateValue` to use IPC (`CONFIGURATION_UPDATE_VALUE`) to ask the main process to perform the update.
      - Implement `onDidChangeConfiguration` event emitter.
    - **IPC:** Listen for `CONFIGURATION_DID_CHANGE` broadcasts from the main process. When received, update the local cache and fire the renderer's `onDidChangeConfiguration` event.

**Phase 2: Integration and Refactoring**

4.  **✅ Refactor `SettingsService` (Main Process):**
    - Removed value get/set logic (delegated to `ConfigurationService`).
    - Moved core setting registration logic and `ensureDefaultProjectsRootExists` to `ConfigurationService`.
    - Removed `SettingsService` entirely, merging its remaining responsibilities into `ConfigurationService`.
    - Updated `WindowManager`, `ServiceManager`, `MainApplication`, `MenuService`, `CommandService`, `AIService` to use `ConfigurationService` and handle initialization order changes.
5.  **Refactor `SettingsEditor` (Renderer Process):**
    - Inject/use the new renderer `ConfigurationService`.
    - Replace direct IPC calls (`SETTINGS_GET_DECLARATIONS_AND_VALUES`, `COMMANDS_EXECUTE` for setting) with calls to `configurationService.getValue()` and `configurationService.updateValue()`.
    - Subscribe to `configurationService.onDidChangeConfiguration` if needed for reactive updates within the editor itself (though optimistic updates might suffice).
6.  **Refactor Other Code:**
    - Identify any other places in the main or renderer process that currently access settings (e.g., `useWorkbenchPersistence`, theme loading).
    - Refactor them to use the appropriate instance of the new `ConfigurationService` (`getValue`).

**Conceptual Diagram:**

```mermaid
graph TD
    subgraph Renderer Process
        R_UI[UI Components e.g., SettingsEditor]
        R_Other[Other Renderer Code]
        R_ConfService[Renderer ConfigurationService (Proxy & Cache)]
    end

    subgraph Main Process
        M_ConfService[Main ConfigurationService (Source of Truth)]
        M_Store[electron-store]
        M_Registry[Settings Registry (Declarations)]
        M_Other[Other Main Code]
    end

    subgraph Shared
        I_ConfService[IConfigurationService Interface]
        Types[Setting Types]
    end

    IPC[(IPC Channel)]

    R_UI --> R_ConfService
    R_Other --> R_ConfService
    R_ConfService -- getValue/updateValue --> IPC
    R_ConfService -- onDidChangeConfiguration --> R_UI & R_Other

    IPC -- getValue/updateValue Request --> M_ConfService
    M_ConfService -- Read/Write --> M_Store
    M_ConfService -- Read --> M_Registry
    M_ConfService -- onDidChangeConfiguration --> IPC
    M_Other --> M_ConfService

    R_ConfService -- Implements --> I_ConfService
    M_ConfService -- Implements --> I_ConfService
    M_ConfService -- Uses --> Types
    R_ConfService -- Uses --> Types
```

**Benefits:**

- **Centralized Logic:** Configuration access logic is consolidated.
- **Type Safety:** Provides a foundation for strong typing via the interface.
- **Clear API:** Consistent API for accessing settings in both processes.
- **Performance:** Renderer cache minimizes IPC overhead for reads.
- **Extensibility:** Solid base for future enhancements (scopes, extensions).
