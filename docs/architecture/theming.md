# Архитектура Системы Тем Оформления (Theming)

Этот документ описывает архитектуру системы тем оформления в AI-Books IDE, позволяющей пользователям кастомизировать внешний вид приложения.

## 1. Цели

- Позволить пользователям выбирать между предопределенными темами (например, светлая, темная).
- Обеспечить возможность расширениям добавлять новые темы оформления.
- Использовать механизм, который легко применяется ко всему UI, построенному на React.
- Хранить выбор пользователя в настройках.

## 2. Основные Компоненты

### 2.1. CSS Переменные

- **Основа:** Вся система строится на использовании CSS переменных (custom properties) для определения ключевых цветов UI (фон, текст, акценты, цвета синтаксиса и т.д.), а также, возможно, шрифтов и размеров.
- **Определение:** Базовый набор CSS переменных определяется в глобальном CSS файле (например, `src/renderer/index.css` или специальном файле `themes/variables.css`) со значениями по умолчанию (например, для светлой темы).

```css
/* Пример в themes/variables.css или index.css */
:root {
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Helvetica, Arial, sans-serif;

  --background-primary: #ffffff;
  --background-secondary: #f3f3f3;
  --background-tertiary: #e8e8e8;

  --text-primary: #1e1e1e;
  --text-secondary: #5c5c5c;
  --text-muted: #8a8a8a;

  --accent-primary: #007acc;
  --accent-primary-foreground: #ffffff;

  --border-color: #d4d4d4;

  /* ... и т.д. для всех элементов UI */
}
```

- **Использование:** Компоненты React используют эти переменные в своих CSS (или CSS-in-JS) правилах.

```css
/* Пример в Button.css */
.button {
  background-color: var(--accent-primary);
  color: var(--accent-primary-foreground);
  border: 1px solid var(--accent-primary);
  font-family: var(--font-family);
}
```

### 2.2. Файлы Тем (JSON или CSS)

- **Ответственность:** Определяют значения CSS переменных для конкретной темы.
- **Формат (Вариант 1: JSON):**

  - JSON файл, содержащий пары ключ-значение, где ключ - имя CSS переменной (без `--`), а значение - ее цветовое или другое значение.
  - **Преимущество:** Легко парсить и обрабатывать программно.
  - **Недостаток:** Требует механизма для применения этих значений к CSS переменным в DOM.

  ```json
  // Пример dark-theme.theme.json
  {
    "background-primary": "#1e1e1e",
    "background-secondary": "#252526",
    "background-tertiary": "#333333",
    "text-primary": "#d4d4d4",
    "text-secondary": "#cccccc",
    "text-muted": "#9e9e9e",
    "accent-primary": "#007acc",
    "accent-primary-foreground": "#ffffff",
    "border-color": "#3f3f46"
  }
  ```

- **Формат (Вариант 2: CSS):**

  - Обычный CSS файл, который переопределяет CSS переменные внутри селектора (например, `body[data-theme='dark']`).
  - **Преимущество:** Простота применения - достаточно добавить/удалить класс или атрибут на `body`.
  - **Недостаток:** Менее гибкий для программной обработки или генерации тем.

  ```css
  /* Пример dark-theme.css */
  body[data-theme="dark"] {
    --background-primary: #1e1e1e;
    --background-secondary: #252526;
    /* ... и т.д. */
  }
  ```

- **Рекомендация:** Использовать **Вариант 1 (JSON)** из-за гибкости, но реализовать надежный механизм применения этих значений.

### 2.3. `ThemeService` (Сервис, Renderer Process)

- **Расположение:** `src/renderer/core/theme.service.ts` (предполагаемое).
- **Ответственность:** Управление темами оформления.
  - Обнаруживает доступные темы (предопределенные и зарегистрированные расширениями). Получает информацию о темах от Main процесса.
  - Загружает и парсит JSON-файлы тем.
  - Получает текущую выбранную тему из `SettingsService` (через IPC или при инициализации).
  - Применяет CSS переменные активной темы к DOM (например, устанавливая стили на `document.documentElement`).
  - Подписывается на изменения настройки темы и переключает тему при необходимости.
- **Механизм Применения (для JSON):**
  - При загрузке темы, итерирует по ключам JSON.
  - Устанавливает соответствующие CSS переменные: `document.documentElement.style.setProperty('--' + key, value)`.
  - При смене темы удаляет старые переменные (или переопределяет их).

### 2.4. `ThemeRegistry` или `ConfigurationService` (Ядро, Main Process)

- **Ответственность:** Обнаружение и предоставление информации о доступных темах.
  - Находит предопределенные темы (например, в директории `themes/`).
  - Получает информацию о темах, зарегистрированных расширениями через точку вклада `themes`.
  - Передает список доступных тем (`{ id: string, label: string, path: string, uiTheme: 'vs-dark' | 'vs-light' }`) в `ThemeService` (Renderer) через IPC.

### 2.5. Точка Вклада `themes`

- **Ответственность:** Позволяет расширениям регистрировать свои темы.
- **Механизм:** Расширение декларирует в своем манифесте (или регистрирует программно):
  - `id`: Уникальный ID темы (например, `my-theme-dark`).
  - `label`: Отображаемое имя темы.
  - `uiTheme`: Базовый тип темы ('vs-dark' или 'vs-light') для совместимости с компонентами, ожидающими темный или светлый фон.
  - `path`: Относительный путь к файлу JSON (или CSS) темы внутри директории расширения.

### 2.6. Интеграция с Настройками

- **Настройка:** `SettingsService` управляет настройкой `workbench.theme` (ID активной темы).
- **Взаимодействие:**
  - `ThemeService` (Renderer) читает начальное значение `workbench.theme` при запуске.
  - `ThemeService` подписывается на изменения `workbench.theme` (через IPC событие `settings:updated`).
  - UI Настроек (`SettingsEditor`) позволяет пользователю выбрать тему из списка, полученного от `ThemeService` (который получил его от `ThemeRegistry` из Main). Выбор обновляет настройку `workbench.theme`.

## 3. Процесс Работы

1.  **Регистрация/Обнаружение (Main):** `ThemeRegistry` собирает информацию обо всех доступных темах (встроенных и из расширений).
2.  **Инициализация (Main -> Renderer):** `ThemeRegistry` передает список тем в `ThemeService` (Renderer). `SettingsService` передает текущее значение `workbench.theme`.
3.  **Загрузка и Применение (Renderer):** `ThemeService` загружает JSON-файл для активной темы (на основе ID из настроек) и применяет CSS переменные к `document.documentElement`.
4.  **Смена Темы (Renderer -> Main -> Renderer):** Пользователь выбирает новую тему в UI Настроек -> `SettingsEditor` обновляет настройку `workbench.theme` через `invoke('settings:setValue', ...)` -> `SettingsService` сохраняет и отправляет `send('settings:updated', ...)` -> `ThemeService` получает уведомление об изменении `workbench.theme` -> `ThemeService` загружает новый файл темы и применяет новые CSS переменные.

## 4. Преимущества

- **Гибкость:** Использование CSS переменных позволяет легко изменять внешний вид всего приложения.
- **Расширяемость:** Новые темы легко добавляются через расширения.
- **Производительность:** Переключение тем обычно не требует перезагрузки и происходит быстро.
- **Стандартизация:** Подход с CSS переменными является современным веб-стандартом.
