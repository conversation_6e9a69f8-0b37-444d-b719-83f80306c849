# Архитектура Системы Контроля Версий

Этот документ описывает **гибридный подход** к реализации системы контроля версий (СКВ) в AI-Books IDE. Он сочетает использование **Git** для версионирования текстового контента сцен и **внутренний механизм истории/снимков** для структурированных данных, хранящихся в базе данных SQLite.

## 1. Цели

- Предоставить писателям инструмент для отслеживания истории изменений как текстового контента (сцен), так и структурированных данных (персонажи, мир, структура книги).
- Позволить создавать "ветки" для экспериментирования с текстом сцен (используя Git).
- Обеспечить возможность сравнения разных версий контента (текст сцен через Git diff, структурированные данные через кастомный diff).
- Предоставить возможность отката к предыдущим версиям как для сцен, так и для структурированных данных.

## 2. Компоненты Гибридной Системы

### 2.1. Хранение Данных

- **Контент Сцен:** Хранится в виде отдельных **Markdown файлов** в файловой системе проекта.
- **Структурированные Данные:** Вся остальная информация (метаданные книги/глав/сцен, профили персонажей, элементы мира, заметки, цели и т.д.) хранится в **базе данных SQLite**, управляемой `StorageService`.

### 2.2. Версионирование Сцен (Git)

- **`GitService` (Main Process):**
  - Обертка над библиотекой Git (например, `isomorphic-git`).
  - Отвечает за инициализацию Git-репозитория в директории проекта (или поддиректории `scenes/`).
  - Выполняет стандартные операции Git **только для файлов сцен (Markdown)**: `status`, `add`, `commit`, `log`, `diff`, `checkout`, `branch`, `merge`.
  - Предоставляет API для UI и других сервисов.
- **Точка Вклада `scm`:** Позволяет `GitService` зарегистрироваться как основной провайдер СКВ.
- **UI (`SourceControlView`, `DiffEditor`, `TimelineView` в Renderer):**
  - Отображают статус **файлов сцен**.
  - Позволяют индексировать и коммитить изменения в сценах.
  - Отображают историю коммитов для сцен.
  - Используют `DiffEditor` для сравнения версий Markdown файлов.

### 2.3. Версионирование Структурированных Данных (Внутренняя История/Снимки)

- **`StorageService` (Main Process):**
  - **Ответственность:** Помимо CRUD операций, отвечает за реализацию механизма истории/снимков для данных в SQLite.
  - **Механизм (Пример):** При изменении важной записи (например, профиля персонажа), `StorageService` может автоматически создавать копию предыдущего состояния в отдельной таблице истории (`character_history`) с временной меткой и ID основной записи.
  - **API:** Предоставляет методы для:
    - Получения истории изменений для конкретной записи (`getHistory(entityType, entityId)`).
    - Получения данных конкретной исторической версии (`getHistoricalVersion(historyId)`).
    - (Опционально) Восстановления записи к состоянию определенной исторической версии (`revertToVersion(historyId)`).
- **UI (`TimelineView`, Редакторы Сущностей в Renderer):**
  - `TimelineView` может отображать не только Git-коммиты, но и точки сохранения/снимки для структурированных данных из `StorageService`.
  - Редакторы (например, `CharacterProfileEditor`) могут иметь вкладку "История" для просмотра и отката изменений конкретной сущности.
  - Кастомный `DiffViewer` может использоваться для сравнения JSON-представлений разных версий структурированных данных.

## 3. Преимущества Гибридного Подхода

- **Лучшее из Git для Текста:** Мощное версионирование, ветвление, слияние для основного контента писателя – текста сцен.
- **Эффективность для Структуры:** Быстрые и сложные запросы к структурированным данным через SQLite.
- **Управляемая Сложность Версионирования БД:** Избегаем полной реализации СКВ поверх БД, ограничиваясь историей/снимками, что значительно проще.
- **Четкое Разделение:** Git отвечает за файлы, `StorageService` - за данные в БД и их простую историю.

## 4. Процесс Работы (Примеры)

**A. Редактирование Сцены:**

1.  Пользователь редактирует сцену (Markdown файл).
2.  `GitService` обнаруживает изменение файла.
3.  `SourceControlView` отображает файл как измененный.
4.  Пользователь "сохраняет снимок" (коммитит) изменение сцены через UI. `GitService` выполняет `git add` и `git commit`.

**B. Редактирование Персонажа:**

1.  Пользователь изменяет атрибут персонажа в `CharacterProfileEditor`.
2.  UI отправляет запрос на сохранение в `CharacterService` (расширение).
3.  `CharacterService` вызывает `StorageService.updateCharacter(...)`.
4.  `StorageService` перед обновлением основной записи создает запись в `character_history` с предыдущим состоянием.
5.  Пользователь может позже просмотреть историю изменений персонажа через UI, который запрашивает данные у `StorageService.getHistory('character', characterId)`.

## 5. Нерешенные Вопросы

- **Детализация Истории БД:** Насколько гранулярной должна быть история в БД? Снимок всей записи при каждом изменении или отслеживание изменений отдельных полей? Снимки проще, но могут занимать больше места.
- **Синхронизация Истории:** Как синхронизировать Git-коммиты сцен с точками сохранения истории в БД для создания единого таймлайна проекта? Возможно, при коммите сцены создавать и "снимок" связанных метаданных в БД с той же меткой/сообщением.
- **Слияние (Merge) для Данных БД:** Внутренняя история в БД обычно не поддерживает сложного слияния веток, как Git. Откат к предыдущей версии может привести к потере последующих изменений.
