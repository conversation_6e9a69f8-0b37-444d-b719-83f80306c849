# Техническая Архитектура: Goals & Achievements Management System

Этот документ описывает предлагаемую техническую архитектуру для системы управления целями и достижениями (`Goals & Achievements Management System`) в AI-Books IDE.

## 1. Реализация как Расширение или Часть Ядра

- **Вариант А (Расширение):**
  - **ID:** `ai-books.goals` (предполагаемое).
  - **Преимущества:** Модульность, изоляция логики.
  - **Недостатки:** Может потребовать более сложного взаимодействия с ядром для отслеживания событий (например, подсчета слов).
- **Вариант Б (Часть Ядра):**
  - **Компоненты:** `GoalsService`, `AchievementService`, `TrackingService` интегрированы в `src/main/`.
  - **Преимущества:** Более простой доступ к событиям и данным других сервисов ядра.
  - **Недостатки:** Увеличивает размер и сложность ядра.
- **Рекомендация:** Начать с **Варианта А (Расширение)** для лучшей модульности, но обеспечить эффективные механизмы подписки на события ядра (например, через Event Bus или специфичные API).

## 2. Интеграция с Сервисами Ядра

- **`StorageService`:** Используется для хранения:
  - Определений целей пользователя (тип, значение, дедлайн, связанный проект/элемент).
  - Текущего прогресса по целям.
  - Определений достижений (условия разблокировки).
  - Разблокированных пользователем достижений.
  - Данных отслеживаемых сессий (время начала/конца, написанные слова, фокус).
- **`CommandService`:** Регистрирует команды для:
  - Установки/изменения/удаления целей.
  - Просмотра прогресса/достижений.
  - Ручного старта/остановки сессии письма.
- **`ViewService`:** Регистрирует:
  - Вид для отображения целей и прогресса (в `Sidebar` или `Panel`).
  - Возможно, вид для отображения достижений.
- **`SettingsService`:** Может хранить настройки, связанные с уведомлениями о целях/достижениях или параметрами отслеживания сессий.
- **`NotificationService`:** Используется для уведомления пользователя о:
  - Достижении цели.
  - Разблокировке достижения.
  - Напоминаниях о целях или сессиях.
- **`ContextService`:** Может использоваться для установки контекста (например, `isTrackingSessionActive`), влияющего на UI или команды.
- **`IpcService`:** Регистрирует обработчики для запросов от UI (получение списка целей/достижений, данных сессий, старт/стоп отслеживания).
- **(Зависимость) `EditorService` / Редактор:** Необходимо получать события от редактора (изменение контента, подсчет слов) для автоматического отслеживания прогресса по целям, связанным с написанием текста. Это может потребовать специального API или механизма событий.

## 3. Ключевые Компоненты (Вариант Расширения `ai-books.goals`)

### 3.1. Backend (`main/`)

- **`index.ts`:** Активация, регистрация компонентов.
- **`goalsService.ts`:** Логика управления целями (CRUD, проверка выполнения), взаимодействие со `StorageService`.
- **`achievementService.ts`:** Логика управления достижениями (проверка условий разблокировки на основе событий или данных из `goalsService`/`trackingService`), взаимодействие со `StorageService`.
- **`trackingService.ts`:** Логика отслеживания сессий письма. Подписывается на события редактора (через ядро), сохраняет данные сессий в `StorageService`.
- **`commands.ts`:** Обработчики команд.
- **`ipcHandlers.ts`:** Обработчики IPC-запросов.
- **(Опционально) `analysisProvider.ts`:** Может предоставлять анализ продуктивности на основе данных сессий.

### 3.2. Frontend (`renderer/`)

- **`views/GoalsProgressView.view.tsx`:** Отображение списка активных целей, их прогресса. Позволяет добавлять/редактировать цели.
- **`views/AchievementsView.view.tsx`:** Отображение списка разблокированных и доступных достижений.
- **`components/SessionTrackerWidget.tsx`:** (Возможно, в Status Bar) Отображает статус текущей сессии письма, таймер, счетчик слов. Позволяет вручную стартовать/останавливать сессию.

## 4. Модель Данных (Примерная)

- **Goal:** `id`, `userId`, `type` ('wordCount', 'timeSpent', 'deadline', 'custom'), `targetValue`, `currentValue`, `startDate`, `deadlineDate?`, `relatedProjectId?`, `relatedItemId?`, `status` ('active', 'completed', 'failed'), `isRecurring` (boolean), `recurrencePattern?`.
- **Achievement:** `id`, `name`, `description`, `icon`, `unlockConditions` (JSON или строка), `points?`, `isHidden`.
- **UnlockedAchievement:** `userId`, `achievementId`, `unlockTimestamp`.
- **WritingSession:** `id`, `userId`, `startTimestamp`, `endTimestamp?`, `wordCountDelta`, `focusMetric?`, `relatedGoalId?`.

## 5. Механизм Отслеживания Прогресса

- **Подсчет Слов/Времени:** `TrackingService` должен получать уведомления от активного редактора об изменениях контента (для подсчета слов) и о времени активности пользователя в редакторе. Это требует механизма подписки на события редактора, предоставляемого ядром.
- **Проверка Условий:** `GoalsService` и `AchievementService` периодически (или по событиям) проверяют условия выполнения целей и разблокировки достижений на основе данных из `StorageService` (включая данные сессий от `TrackingService`).
- **Уведомления:** При выполнении/разблокировке соответствующие сервисы используют `NotificationService` для информирования пользователя.

## 6. Преимущества

- **Мотивация:** Помогает пользователям оставаться мотивированными и отслеживать свой прогресс.
- **Геймификация:** Элементы достижений могут сделать процесс письма более увлекательным.
- **Аналитика:** Данные сессий и целей могут использоваться для анализа продуктивности.
- **Структурирование Работы:** Помогает разбивать большие проекты на управляемые этапы.
