# Возможности Архитектуры в Стиле VS Code для AI-Books

Этот документ описывает концепции из архитектуры VS Code, которые могут быть адаптированы для IDE писателей AI-Books для повышения гибкости, расширяемости и удобства использования.

## 1. Динамический Workbench и Управление Видами (Views) - [ЧАСТИЧНО РЕАЛИЗОВАНО]

- **Аналогия с VS Code:** VS Code позволяет открывать разные панели (Explorer, Search, Source Control, Extensions) в боковой панели (Sidebar) и нижней панели (Panel), а также управлять группами редакторов.
- **Для IDE Писателя:**
  - ✅ Сделать иконки в Activity Bar интерактивными, чтобы они переключали содержимое Sidebar.
  - ✅ Реализовать **базовую** систему вкладок в `editor-group` (управление состоянием, рендеринг, закрытие).
  - ✅ Реализовать отображение структуры книги (включая Секции) в Sidebar и открытие сцен во вкладках по клику.
  - ✅ Реализовать базовое управление (CRUD) для Книг/Секций/Глав/Сцен из Sidebar.
  - ✅ Реализовать базовую нижнюю панель (Panel) для вывода (например, комментариев или результатов AI).
  - ✅ Реализовать базовый вид для списка Персонажей в Sidebar.
- **Реализация:** ✅ Основные элементы Workbench реализованы. **MVP требует:** Наполнение Sidebar видами для Структуры Книги и Списка Персонажей. Наполнение Panel базовым UI для Комментариев и вывода AI. **Post-MVP:** Search, World виды, продвинутые функции Panel.

## 2. Command Palette (Палитра Команд) - [БАЗОВО РЕАЛИЗОВАНО]

- **Аналогия с VS Code:** Быстрый доступ ко всем командам редактора через `Ctrl/Cmd+Shift+P`.
- **Для IDE Писателя:** Позволит быстро вызывать действия, такие как "Создать новую сцену", "Найти персонажа", "Проверить консистентность главы X", "Экспортировать в PDF", "Запустить AI-анализ темпа", "Перейти к закладке Y" и т.д., без необходимости искать их в меню или на панелях.
- **Реализация:** ✅ Создан реестр команд (Renderer), ✅ UI-компонент палитры, ✅ открытие по горячей клавише, ✅ интегрированы обработчики для переключения видов и сохранения сцены. Требуется добавление команд с IPC и расширение реестра.

## 3. Система Настроек (Settings) - [БАЗОВО РЕАЛИЗОВАНО]

- **Аналогия с VS Code:** Пользовательские и рабочие настройки в формате JSON, доступные через UI.
- **Для IDE Писателя:** Позволит настраивать поведение редактора (шрифты, автосохранение), параметры AI (модель, уровень "креативности"), форматы экспорта, темы оформления, горячие клавиши и т.д.
- **Реализация:** ✅ Сервис настроек в Main Process (`electron-store`), ✅ IPC-каналы для чтения/записи, ✅ базовый UI-компонент в Renderer (`SettingsView`). Требуется расширение схемы настроек и UI.

## 4. "Серверы" Анализа (Адаптация LSP) - [НЕ РЕАЛИЗОВАНО]

- **Аналогия с VS Code:** Language Server Protocol для предоставления языковых функций.
- **Для IDE Писателя:** Фоновые сервисы для анализа текста и метаданных.
- **Реализация MVP:** **Не реализуется** полная архитектура Analysis Servers. **Реализуется:** Инфраструктура AI в Main Process и **одна** конкретная функция анализа (`AI Character Consistency Check`), которая использует данные из `StorageService` (профиль персонажа, события) и контент сцены (Markdown) для выявления несоответствий. Результаты отображаются в UI (например, в Panel). **Post-MVP:** Полноценные Analysis Servers, другие типы анализа (стиль, сюжет).

## 5. Интеграция с Системой Контроля Версий (Git) - [НЕ РЕАЛИЗОВАНО]

- **Аналогия с VS Code:** Встроенная поддержка Git.
- **Для IDE Писателя:** Отслеживание изменений, ветвление, слияние, история.
- **Реализация MVP:** **Не реализуется** полная интеграция Git или сложная система версионирования. **Реализуется:** Надежное **автосохранение** сцен (Markdown) и **ручное создание "снимков"** (через команду, возможно, использующую `git commit` для файлов сцен, если это просто в реализации, или внутренний механизм снимков). Для метаданных в SQLite - **простые снимки** по ручной команде. Базовый UI для просмотра списка версий/снимков. **Post-MVP:** Полноценная интеграция Git (ветки, слияние, diff), продвинутая история для данных БД, визуальный diff.

## Приоритеты для следующих этапов (Обновлено)

1.  ✅ **Electron Core & Offline-First Storage** (Основа MVP)
2.  ✅ **Integrated Book Structure Management** (Базовый CRUD, включая Секции)
3.  ✅ **Contextual Scene Editor (Lexical)** (Базовый редактор, автосохранение)
4.  ✅ **Event-Driven Character Management (Core)** (Профили, События в БД)
5.  ✅ **Reliable Content Versioning (Basic)** (Автосохранение, ручные снимки)
6.  ✅ **Contextual AI Core & Consistency Check** (Инфраструктура + 1 функция)
7.  ✅ **Basic Feedback System** (Комментарии к тексту)
8.  ✅ **Command Palette (Палитра Команд)** (Базово)
9.  ✅ **Система Настроек (Settings)** (Базово)
10. ✅ **Динамический Workbench и Управление Видами** (Каркас, базовые виды MVP)
11. ❌ **"Серверы" Анализа (Полная система)**
12. ❌ **Интеграция с Системой Контроля Версий (Полная, Git)**
13. ❌ **Продвинутая Коллаборация (Real-time)**
14. ❌ **World Building, Goals, Research/Ideas Systems**
15. _Следующие шаги (Post-MVP):_
    - Наполнение Sidebar/Panel видами (World, Search, Goals, Ideas).
    - Реализация продвинутого версионирования (Diff, Branching).
    - Добавление AI функций (генерация, стиль, сюжет).
    - Реализация систем World, Goals, Research/Ideas.
    - Реализация Real-time коллаборации.
