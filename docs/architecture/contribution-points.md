# Точки вклада (Contribution Points) в системе расширений

Точки вклада (Contribution Points) - это механизм, который позволяет расширениям объявлять свои возможности и
интегрироваться с различными частями приложения. Они объявляются в манифесте расширения (`package.json`) и
обрабатываются системой расширений при загрузке расширения.

## Типы точек вклада

### 1. Команды (commands)

Команды - это действия, которые могут быть вызваны пользователем через меню, горячие клавиши или палитру команд.

```json
"commands": [
  { 
    "command": "book-ide.my-extension.command1", 
    "title": "My Command 1",
    "category": "My Extension"
  }
]
```

Обработчики команд регистрируются в методе `activate()` расширения:

```typescript
context.subscriptions.push(
  context.commands.registerCommand({
    id: `${EXTENSION_ID}.command1`,
    title: 'My Command 1',
    category: 'My Extension',
    handler: async () => {
      // Реализация команды
    }
  })
);
```

### 2. Представления (views)

Представления - это компоненты пользовательского интерфейса, которые отображаются в определенных местах приложения (
боковая панель, панель статуса и т.д.).

```json
"views": {
  "book-ide.my-extension": [
    {
      "id": "book-ide.my-extension.view",
      "name": "My Extension View",
      "componentName": "MyExtensionView",
      "location": "sidebar",
      "icon": "Package"
    }
  ]
}
```

### 3. Контейнеры представлений (viewContainers)

Контейнеры представлений - это контейнеры, которые могут содержать несколько представлений.

```json
"viewContainers": [
  {
    "id": "book-ide.my-extension",
    "title": "My Extension",
    "icon": "Package",
    "viewId": "book-ide.my-extension.view"
  }
]
```

### 4. Меню (menus)

Меню - это пункты меню, которые отображаются в различных контекстных меню приложения.

```json
"menus": {
  "explorer/context": [
    { 
      "command": "book-ide.my-extension.command1", 
      "group": "1_modification@1" 
    }
  ],
  "editor/context": [
    { 
      "command": "book-ide.my-extension.command2", 
      "group": "1_modification@1" 
    }
  ]
}
```

### 5. Редакторы (editors)

Редакторы - это компоненты, которые отображаются в основной области редактирования приложения.

```json
"editors": [
  {
    "editorType": "book-ide.my-extension:editor",
    "componentName": "MyEditor",
    "icon": "FileText"
  }
]
```

### 6. Задачи ИИ (aiTasks)

Задачи ИИ - это задачи, которые могут быть выполнены с использованием искусственного интеллекта.

```json
"aiTasks": [
  {
    "taskId": "my-task",
    "description": "My AI task",
    "inputSchema": {
      "type": "object",
      "properties": {
        "input": { "type": "string" }
      },
      "required": ["input"]
    },
    "outputSchema": {
      "type": "object",
      "properties": {
        "output": { "type": "string" }
      },
      "required": ["output"]
    }
  }
]
```

## Обработка точек вклада

Точки вклада обрабатываются системой расширений при загрузке расширения. Каждый тип точки вклада обрабатывается
соответствующим обработчиком, который регистрирует точки вклада в соответствующем сервисе.

### Архитектура обработки точек вклада

1. `ExtensionRegistry` - основной класс, который управляет жизненным циклом расширений.
2. `ContributionHandlersRegistry` - реестр обработчиков точек вклада.
3. `IContributionHandler` - интерфейс для обработчиков точек вклада.
4. Конкретные обработчики точек вклада:
    - `CommandContributionHandler` - обработчик команд
    - `ViewContributionHandler` - обработчик представлений
    - `ViewContainerContributionHandler` - обработчик контейнеров представлений
    - `MenuContributionHandler` - обработчик меню
    - `EditorContributionHandler` - обработчик редакторов
    - `AITaskContributionHandler` - обработчик задач ИИ

### Процесс обработки точек вклада

1. При инициализации `ExtensionRegistry` создается `ContributionHandlersRegistry` и регистрируются все обработчики точек
   вклада.
2. При обнаружении расширения `ExtensionRegistry` вызывает метод `processContributions` для обработки точек вклада из
   манифеста расширения.
3. `ContributionHandlersRegistry` перебирает все типы точек вклада в манифесте и вызывает соответствующие обработчики.
4. Каждый обработчик регистрирует точки вклада в соответствующем сервисе и возвращает массив объектов `Disposable`,
   которые добавляются в `subscriptions` расширения.
5. При деактивации расширения все объекты `Disposable` в `subscriptions` освобождаются, что приводит к удалению всех
   зарегистрированных точек вклада.

## Пример манифеста расширения

Полный пример манифеста расширения с различными типами точек вклада можно найти
в [примере манифеста расширения](../examples/extension-manifest-example.md).
