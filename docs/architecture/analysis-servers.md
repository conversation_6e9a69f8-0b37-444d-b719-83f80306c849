# Архитектура "Серверов" Анализа (Адаптация LSP)

Этот документ описывает предлагаемую архитектуру для фоновых сервисов анализа текста и метаданных в AI-Books IDE,
вдохновленную Language Server Protocol (LSP).

## 1. Цели

- Обеспечить непрерывный или по запросу анализ контента (сцен, заметок) и метаданных (персонажи, мир) на предмет ошибок,
  несоответствий, стилистических проблем и т.д.
- Предоставлять результаты анализа (диагностику, предложения) для отображения в UI (редактор, панель проблем).
- Предлагать контекстно-зависимые действия и информацию (автодополнение, подсказки при наведении).
- Интегрироваться с AI для более глубокого анализа и генерации предложений.
- Обеспечить расширяемость для добавления новых типов анализа через систему расширений.

## 2. Основные Компоненты

### 2.1. `AnalysisService` (Ядро, Main Process)

- **Ответственность:** Центральный координатор для всех провайдеров анализа.
    - Управляет жизненным циклом провайдеров анализа.
    - Получает события об изменении документов (файлов сцен) или данных (в `StorageService`) от других сервисов.
    - Определяет, какие провайдеры применимы к измененному ресурсу (файлу или записи БД).
    - Запрашивает необходимый контент (содержимое файла или данные из `StorageService`).
    - Передает запросы на анализ соответствующим провайдерам.
    - Агрегирует результаты анализа (диагностику) от разных провайдеров.
    - Отправляет агрегированную диагностику в Renderer процесс через IPC (`send('diagnostics:update', ...)`).
    - Обрабатывает запросы от Renderer на получение подсказок при наведении (`Hover`) и быстрых исправлений (
      `CodeAction`) через IPC, делегируя их соответствующим провайдерам. (Запросы на автодополнение (`Completions`)
      обрабатываются `CompletionService`).
- **API для Расширений (`context.analysis`):**
    - `registerAnalysisProvider(provider: AnalysisProvider): Disposable`: Регистрирует провайдера анализа.

### 2.2. `AnalysisProvider` (Интерфейс и Реализации)

- **Ответственность:** Инкапсулирует логику конкретного типа анализа. Реализуется расширениями.
- **Интерфейс (`AnalysisProvider`):**
    - `id: string`: Уникальный идентификатор провайдера (например, `ai-books.narrative-consistency`).
    - `dataType: 'file' | 'databaseEntity' | string`: Тип данных, который анализирует провайдер ('file' для файлов
      сцен, 'databaseEntity' для записей в БД, или специфичный ID сущности).
    - `selector?: { pattern?: string; entityType?: string }`: Селектор для определения применимости провайдера (
      например, `pattern: '**/*.md'` для файлов сцен, `entityType: 'character'` для персонажей).
    -
    `provideDiagnostics(resourceUri: string, contentOrData: string | any, context: AnalysisContext): Promise<Diagnostic[]>`:
    Выполняет анализ контента файла или данных сущности из БД и возвращает список проблем.
    -
    `provideHover?(resourceUri: string, positionOrData: Position | any, context: AnalysisContext): Promise<Hover | null>`:
    Возвращает информацию для подсказки при наведении (для файлов или данных).
    -
    `provideCodeActions?(resourceUri: string, rangeOrData: Range | any, diagnostics: Diagnostic[], context: AnalysisContext): Promise<CodeAction[]>`:
    Возвращает быстрые исправления или рефакторинги.
- **Реализации (Примеры, в расширениях):**
    - `NarrativeConsistencyProvider`: Проверяет упоминания, хронологию.
    - `StyleAnalysisProvider`: Проверяет стиль, читаемость, клише.
    - `AIAnalysisProvider`: Использует AI для более сложного анализа.

### 2.3. `Diagnostic` (Интерфейс)

- **Ответственность:** Описывает одну проблему, найденную анализатором.
- **Поля (аналогично LSP):**
    - `range: Range`: Местоположение проблемы в документе.
    - `severity: DiagnosticSeverity`: Уровень (Ошибка, Предупреждение, Информация, Подсказка).
    - `message: string`: Описание проблемы.
    - `source?: string`: Источник проблемы (например, 'narrative-consistency').
    - `code?: string | number`: Код ошибки (опционально).
    - `relatedInformation?: DiagnosticRelatedInformation[]`: Связанная информация (опционально).

### 2.4. Интеграция с UI (Renderer Process)

- **Редактор (Lexical):**
    - Получает диагностику от Main процесса через IPC (`on('diagnostics:update', ...)`).
    - Отображает подчеркивания/маркеры в тексте (для файлов сцен) на основе `Diagnostic.range` и
      `Diagnostic.severity`. (Для данных из БД может потребоваться другой механизм отображения проблем).
    - Показывает `Diagnostic.message` при наведении на проблему.
    - Запрашивает подсказки (`invoke('analysis:getHover', ...)`), быстрые исправления (
      `invoke('analysis:getCodeActions', ...)`) при соответствующих действиях пользователя и отображает результаты. (
      Автодополнение запрашивается через `CompletionService`).
- **Панель Проблем (`ProblemsView`):**
    - Также подписывается на `on('diagnostics:update', ...)`.
    - Отображает список всех проблем в проекте/открытых файлах, сгруппированных по файлу и/или источнику.
    - Позволяет фильтровать проблемы по серьезности.
    - По клику на проблему переводит фокус редактора на соответствующее место (`range`).

## 3. Процесс Работы (Пример: Диагностика)

1. **Регистрация (Main):** Расширение "Narrative" регистрирует `NarrativeConsistencyProvider` через
   `context.analysis.registerAnalysisProvider(...)`.
2. **Изменение Ресурса (Renderer -> Main):** Пользователь редактирует сцену (файл) или профиль персонажа (данные в БД).
   Соответствующий сервис (`GitService` или `StorageService`) уведомляет `AnalysisService` об изменении ресурса (URI
   файла или ID сущности).
3. **Запрос Анализа (Main):** `AnalysisService` получает уведомление, определяет тип ресурса и его URI/ID. Запрашивает
   актуальный контент/данные.
4. **Делегирование (Main):** `AnalysisService` находит подходящие `AnalysisProvider` на основе `dataType` и `selector`,
   затем вызывает их метод `provideDiagnostics`, передавая URI/ID и контент/данные.
5. **Анализ (Main/Worker):** Провайдеры выполняют свою логику анализа (возможно, асинхронно или в worker'е) и возвращают
   `Promise<Diagnostic[]>`.
6. **Агрегация (Main):** `AnalysisService` собирает результаты от всех провайдеров.
7. **Отправка в UI (Main -> Renderer):** `AnalysisService` отправляет полный список диагностики для данного документа
   через `send('diagnostics:update', { uri: documentUri, diagnostics: allDiagnostics })`.
8. **Отображение (Renderer):** Редактор и `ProblemsView` получают событие и обновляют свое отображение.

## 4. Преимущества

- **Модульность:** Логика анализа инкапсулирована в провайдерах.
- **Расширяемость:** Легко добавлять новые типы анализа.
- **Производительность:** Анализ может выполняться асинхронно или в отдельных процессах/worker'ах, не блокируя UI или
  основной поток Main.
- **Стандартизация:** Использование концепций, схожих с LSP, упрощает понимание и интеграцию.
