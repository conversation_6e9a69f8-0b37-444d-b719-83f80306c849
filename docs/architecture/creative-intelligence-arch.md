# Техническая Архитектура: Creative Intelligence System

Этот документ описывает техническую реализацию и интеграцию системы управления идеями и исследованиями (`Creative Intelligence System`, ранее `idea-and-research-management`) в рамках общей архитектуры AI-Books IDE.

## 1. Реализация как Расширение

- **ID Расширения:** `ai-books.research` или `ai-books.creative-intelligence` (предполагаемое).
- **Структура:** Следует стандартной структуре расширений ([`extension-system.md`](./extension-system.md)).
- **Активация:** Функция `activate` регистрирует компоненты через `ExtensionContext`.

## 2. Интеграция с Сервисами Ядра

- **`StorageService`:** Используется для хранения **структурированных данных исследований и идей** (заметки, идеи, метаданные импортированных файлов, связи) в SQLite и для реализации их **внутренней истории/снимков**.
- **`GitService` ([`version-control.md`](./version-control.md)):** Не используется напрямую для данных идей/исследований, но важен для получения контента сцен (Markdown), с которыми могут быть связаны идеи/заметки.
- **`CommandService`:** Регистрирует команды для:
  - Создания/управления заметками, идеями, коллекциями (в `StorageService`).
  - Импорта исследовательских материалов (взаимодействует с файловой системой и `StorageService`).
  - Поиска связей (запрашивает данные у `StorageService` и `AnalysisService`).
  - Запуска генерации идей (AI).
- **`ViewService`:** Регистрирует:
  - Вид "Research Panel" / "Knowledge Repository" (в `Sidebar` или `Panel`) для просмотра и организации исследований (данные из `StorageService`).
  - Вид "Idea Workshop" / "Idea Sidebar" (в `Sidebar` или `Panel`) для просмотра и управления идеями (данные из `StorageService`).
- **`EditorService`:** Регистрирует:
  - Пользовательский редактор для заметок (например, Markdown с расширенными возможностями или специализированный), работающий с данными из `StorageService`.
  - Возможно, редактор для просмотра PDF или веб-страниц внутри IDE (может использовать `WebviewService`).
- **`AnalysisService` ([`analysis-servers.md`](./analysis-servers.md)):**
  - Расширение может предоставлять `AnalysisProvider` для:
    - Автоматического поиска и предложения связей между заметками, идеями (из `StorageService`) и контентом книги (Markdown файлы).
    - Анализа исследовательских материалов (например, суммаризация).
- **`CompletionService` ([`intellisense.md`](./intellisense.md)):**
  - Может предоставлять `CompletionProvider` для быстрого связывания идей или заметок при написании текста (данные из `StorageService`).
- **`SearchService` ([`search-replace.md`](./search-replace.md)):** Используется для поиска по заметкам и идеям в `StorageService`.
- **`WebviewService` ([`webviews.md`](./webviews.md)):** Используется для:
  - Визуализации графа связей ("Connection Engine") (данные из `StorageService`).
  - Отображения mind-map или других нелинейных представлений идей ("Idea Workshop") (данные из `StorageService`).
  - Возможно, для рендеринга веб-клипов или PDF.
- **`SettingsService`:** Настройки для импорта, организации, AI-функций.
- **`IpcService`:** Обработчики для CRUD операций с заметками/идеями (в `StorageService`), поиска связей, импорта.

## 3. Ключевые Компоненты Расширения (`ai-books.research`)

### 3.1. Backend (`main/`)

- **`index.ts`:** Активация, регистрация.
- **`researchService.ts`:** Логика управления исследовательскими материалами (CRUD, импорт, парсинг метаданных), взаимодействие со **`StorageService`** и файловой системой.
- **`ideaService.ts`:** Логика управления идеями (CRUD, статусы), взаимодействие со **`StorageService`**.
- **`connectionService.ts`:** Логика управления связями между элементами в **`StorageService`**.
- **`analysisProvider.ts`:** Реализация `AnalysisProvider` для поиска связей (работает с `StorageService` и файлами сцен).
- **`commands.ts`:** Обработчики команд.
- **`ipcHandlers.ts`:** Обработчики IPC.

### 3.2. Frontend (`renderer/`)

- **`views/ResearchView.view.tsx`:** UI для отображения, организации, поиска исследовательских материалов (из `StorageService`).
- **`views/IdeaWorkshopView.view.tsx`:** UI для работы с идеями (список, доска, mind-map через Webview) (данные из `StorageService`).
- **`editors/NoteEditor.editor.tsx`:** Редактор для заметок (работает с `StorageService`).
- **`components/ConnectionGraph.webview.html/js`:** Контент для `Webview`, отображающий граф связей.
- **`components/ImportManager.tsx`:** UI для управления импортом материалов.

## 4. Модель Данных (Примерная)

(Хранится в **`StorageService` (SQLite)**)

- **ResearchItem:** `id`, `type` ('note', 'webclip', 'pdf', 'image', 'book'), `title`, `content` (для note), `sourceUri`, `localPath?` (для pdf/image), `metadata: JSON`, `tags: JSON`, `collections: JSON`, `annotations: JSON`.
- **Idea:** `id`, `title`, `description`, `status` ('raw', 'developed', 'implemented'), `tags: JSON`.
- **Connection:** `id`, `sourceType`, `sourceId`, `targetType`, `targetId`, `relationType` ('related', 'supports', 'contradicts', 'inspiredBy'), `description?`.
- **ResearchItemHistory / IdeaHistory:** `historyId`, `itemId`, `timestamp`, `previousState: JSON`.

## 5. Взаимодействие с Другими Расширениями

- **`ai-books.books`:** Позволяет связывать идеи/заметки (из `StorageService`) с конкретными сценами (файлы) / главами (в `StorageService`). Может отображать релевантные заметки в панели редактора.
- **`ai-books.characters`, `ai-books.world`:** Позволяет связывать идеи/заметки с персонажами, локациями и т.д. (все в `StorageService`).
- **`ai-books.ai`:** Использует данные исследований и идей (из `StorageService`) для генерации предложений, суммаризации, поиска связей.

## 6. Ключевые Технические Задачи

- **Импорт и Парсинг:** Реализация надежного импорта из разных источников (веб-страницы, PDF) и извлечения метаданных.
- **Поиск Связей:** Разработка эффективных алгоритмов (включая AI) для поиска релевантных связей между большим количеством неструктурированных данных.
- **Визуализация:** Интеграция с библиотеками для отображения графов и mind-map в `Webviews`.
- **Хранение:** Эффективное хранение и индексация как структурированных (идеи, связи), так и неструктурированных (текст заметок, контент файлов) данных.
