# Техническая Архитектура: World Building Management System

Этот документ описывает техническую реализацию и интеграцию системы управления миром (`World Building Management System`) в рамках общей архитектуры AI-Books IDE.

## 1. Реализация как Расширение

- **ID Расширения:** `ai-books.world` (предполагаемое).
- **Структура:** Следует стандартной структуре расширений ([`extension-system.md`](./extension-system.md)).
- **Активация:** Функция `activate` регистрирует компоненты через `ExtensionContext` и может экспортировать API для использования другими расширениями.

## 2. Интеграция с Сервисами Ядра

- **`StorageService`:** Используется для хранения **структурированных данных мира** (локации, культуры, правила, события истории, их атрибуты и связи) в SQLite и для реализации их **внутренней истории/снимков**.
- **`GitService` ([`version-control.md`](./version-control.md)):** Не используется напрямую для данных мира, но важен для получения контента сцен (Markdown), где анализируются упоминания элементов мира.
- **`CommandService`:** Регистрирует команды для CRUD операций с элементами мира (в `StorageService`).
- **`ViewService`:** Регистрирует:
  - Виды для отображения иерархии мира, списков локаций, культур и т.д. (в `Sidebar` или `Panel`), читающие данные из `StorageService`.
  - Возможно, вид временной шкалы истории мира (читает историю из `StorageService`).
- **`EditorService`:** Регистрирует:
  - Пользовательские редакторы для элементов мира (профиль локации, описание культуры, правило системы магии), работающие с данными из `StorageService`.
- **`AnalysisService` ([`analysis-servers.md`](./analysis-servers.md)):**
  - Расширение может предоставлять `AnalysisProvider` для:
    - Обнаружения упоминаний элементов мира (локаций, терминов) в **тексте сцен (Markdown файлы)**.
    - Проверки консистентности правил мира или исторических событий (данные из `StorageService`).
- **`CompletionService` ([`intellisense.md`](./intellisense.md)):**
  - Расширение может предоставлять `CompletionProvider` для автодополнения названий локаций, культур, терминов (данные из `StorageService`) в редакторе сцен.
- **`SearchService` ([`search-replace.md`](./search-replace.md)):** Используется для поиска по данным мира в `StorageService`.
- **`WebviewService` ([`webviews.md`](./webviews.md)):** Используется для отображения сложных визуализаций (карты, таймлайны, графы связей), данные для которых берутся из `StorageService`.
- **`SettingsService`:** Настройки, связанные с отображением или анализом данных мира.
- **`IpcService`:** Обработчики для запросов от UI (получение данных мира из `StorageService`, CRUD операции).
- **(Будущее) `TimelineService`:** Может использоваться для управления и визуализации временной шкалы истории мира (данные из `StorageService`), возможно, синхронизируясь с Git-коммитами сцен.
- **(Будущее) `TemplateService`:** Для поддержки шаблонов элементов мира (работающих с `StorageService`).

## 3. Ключевые Компоненты Расширения (`ai-books.world`)

### 3.1. Backend (`main/`)

- **`index.ts`:** Активация, регистрация.
- **`worldElementService.ts`:** Общая логика CRUD для элементов мира (локации, культуры, правила, события), взаимодействие со **`StorageService`**.
- **`worldAnalysisProvider.ts`:** Реализация `AnalysisProvider` (работает с `StorageService` и файлами сцен).
- **`worldCompletionProvider.ts`:** Реализация `CompletionProvider` (работает с `StorageService`).
- **`commands.ts`:** Обработчики команд.
- **`ipcHandlers.ts`:** Обработчики IPC.

### 3.2. Frontend (`renderer/`)

- **`views/WorldExplorerView.view.tsx`:** Отображение иерархии или списка элементов мира (из `StorageService`).
- **`editors/LocationEditor.editor.tsx`, `editors/CultureEditor.editor.tsx`, etc.:** Редакторы для различных типов элементов мира (работают с `StorageService`).
- **`components/WorldMap.webview.html/js`:** Контент для `Webview`, отображающий карту (использует Leaflet, Mapbox GL JS или аналоги).
- **`components/WorldTimeline.webview.html/js`:** Контент для `Webview`, отображающий временную шкалу (использует Vis.js Timeline или аналоги).

## 4. Модель Данных (Примерная)

(Хранится в **`StorageService` (SQLite)**)

- **WorldElement:** `id`, `type` ('location', 'culture', 'rule', 'event', 'item'), `name`, `description`, `attributes: JSON`, `tags: string[]`.
- **Location (extends WorldElement):** `parentLocationId?`, `coordinates?`, `mapId?`.
- **Culture (extends WorldElement):** `governance`, `beliefs`, `customs`.
- **Rule (extends WorldElement):** `systemType` ('magic', 'technology', 'physics'), `details`.
- **HistoryEvent (extends WorldElement):** `timestamp` (или `timelinePosition`), `relatedElements: JSON`. // JSON для хранения массива связей
- **WorldConnection:** `id`, `sourceElementId`, `targetElementId`, `relationType` ('contains', 'alliedWith', 'causedBy'), `description?`.
- **WorldElementHistory:** `historyId`, `elementId`, `timestamp`, `previousState: JSON`.

## 5. Взаимодействие с Другими Расширениями

- **`ai-books.books`:** Позволяет связывать сцены (файлы) с локациями, событиями (в `StorageService`). Предоставляет API для получения данных мира (из `StorageService`) для отображения в редакторе.
- **`ai-books.characters`:** Позволяет связывать персонажей с культурами, локациями, историческими событиями (все в `StorageService`).
- **`ai-books.research`:** Может связывать исследовательские заметки с элементами мира (все в `StorageService`).
- **`ai-books.ai`:** Использует данные мира (из `StorageService`) для проверки консистентности, генерации описаний, предложений.
