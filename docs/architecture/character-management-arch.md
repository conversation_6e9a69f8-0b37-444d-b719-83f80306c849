# Техническая Архитектура: Character Management System

Этот документ описывает техническую реализацию и интеграцию системы управления персонажами (`Character Management System`) в рамках общей архитектуры AI-Books IDE.

## 1. Реализация как Расширение

- **ID Расширения:** `ai-books.characters` (предполагаемое).
- **Структура:** Следует стандартной структуре расширений ([`extension-system.md`](./extension-system.md)).
- **Активация:** Функция `activate` в `src/extensions/characters/main/index.ts` регистрирует компоненты через `ExtensionContext` и экспортирует API расширения (например, `CharacterAPI`) для использования другими расширениями.

## 2. Интеграция с Сервисами Ядра

- **`StorageService`:** Используется для хранения **структурированных данных персонажей** (профили, атрибуты, события развития, связи) в SQLite и для реализации их **внутренней истории/снимков**.
- **`GitService` ([`version-control.md`](./version-control.md)):** Не используется напрямую для данных персонажей, но важен для получения контента сцен (Markdown), где анализируются упоминания персонажей.
- **`CommandService`:** Регистрирует команды для CRUD операций с персонажами (в `StorageService`), создания событий развития и т.д.
- **`ViewService`:** Регистрирует:
  - Вид списка персонажей (например, `CharacterListView`) для `Sidebar` (читает данные из `StorageService`).
  - Возможно, вид таймлайна событий персонажа для `Panel` или `Sidebar` (читает историю из `StorageService`).
- **`EditorService`:** Регистрирует:
  - Пользовательский редактор профиля персонажа (например, `CharacterProfileEditor`), работающий с данными из `StorageService`.
  - Возможно, редактор для событий развития (также работает с `StorageService`).
- **`AnalysisService` ([`analysis-servers.md`](./analysis-servers.md)):**
  - Расширение предоставляет `AnalysisProvider` для:
    - Обнаружения упоминаний персонажей в **тексте сцен (Markdown файлы)**.
    - Анализа консистентности атрибутов/событий персонажа (данные из `StorageService`).
    - Оценки качества развития (Character Analysis) (данные из `StorageService`).
    - Предложения AI по развитию.
- **`CompletionService` ([`intellisense.md`](./intellisense.md)):**
  - Расширение предоставляет `CompletionProvider` для автодополнения имен персонажей (данные из `StorageService`) в редакторе сцен.
- **`SearchService` ([`search-replace.md`](./search-replace.md)):** Используется для поиска по данным персонажей в `StorageService`.
- **`WebviewService` ([`webviews.md`](./webviews.md)):** Используется для отображения сложных визуализаций (граф связей, арка развития), данные для которых берутся из `StorageService`.
- **`SettingsService`:** Расширение может регистрировать настройки, связанные с отображением или анализом персонажей.
- **`IpcService`:** Регистрирует обработчики для запросов от UI (получение списка персонажей, данных профиля, событий из `StorageService`, запуск анализа и т.д.).
- **(Будущее) `TimelineService`:** Может потребоваться для синхронизации событий развития персонажа (из `StorageService`) с общей временной шкалой проекта (которая также должна учитывать Git-коммиты сцен).
- **(Будущее) `TemplateService`:** Для поддержки шаблонов персонажей (работающих с `StorageService`).

## 3. Ключевые Компоненты Расширения (`ai-books.characters`)

### 3.1. Backend (`main/`)

- **`index.ts`:** Активация, регистрация, экспорт `CharacterAPI`.
- **`characterService.ts`:** Основная логика CRUD для персонажей, управление событиями, взаимодействие со **`StorageService`**.
- **`characterAnalysisProvider.ts`:** Реализация `AnalysisProvider` для анализа упоминаний (читает файлы сцен), консистентности, качества развития (читает данные из `StorageService`).
- **`characterCompletionProvider.ts`:** Реализация `CompletionProvider` для автодополнения имен (читает данные из `StorageService`).
- **`commands.ts`:** Обработчики команд.
- **`ipcHandlers.ts`:** Обработчики IPC-запросов от Renderer.

### 3.2. Frontend (`renderer/`)

- **`views/CharacterListView.view.tsx`:** Отображение списка персонажей (из `StorageService`), фильтрация, запуск команды открытия профиля.
- **`editors/CharacterProfileEditor.editor.tsx`:** UI для просмотра и редактирования данных персонажа (из `StorageService`), включая атрибуты и список событий развития. Может содержать встроенные `Webviews` для визуализаций.
- **`components/RelationshipGraph.webview.html/js`:** Контент для `Webview`, отображающий граф связей (использует библиотеки типа Vis.js, D3).
- **`components/CharacterArcChart.webview.html/js`:** Контент для `Webview`, визуализирующий арку развития.

## 4. Модель Данных (Примерная)

(Хранится в **`StorageService` (SQLite)**)

- **Character:** `id`, `name`, `role`, `description`, `attributes: JSON`, `creationDate`, `lastUpdate`.
- **CharacterEvent:** `id`, `characterId`, `timestamp` (или `timelinePosition: number`), `type` (ключевое событие, изменение атрибута), `description`, `impactScore`, `relatedScenePath?` (путь к файлу сцены).
- **Relationship:** `id`, `characterId1`, `characterId2`, `type` (семья, дружба, вражда), `description`.
- **CharacterHistory:** `historyId`, `characterId`, `timestamp`, `previousState: JSON`.

## 5. Взаимодействие с Другими Расширениями

- **`ai-books.books`:** Предоставляет API (`CharacterAPI`) для получения данных о персонажах (из `StorageService`) для отображения информации в редакторе сцен. Получает события об изменении **файлов сцен** для обновления анализа упоминаний.
- **`ai-books.world`:** Может связывать персонажей с локациями или культурами (данные в `StorageService`) через API друг друга.
- **`ai-books.ai`:** Использует `AnalysisProvider`'ов персонажей (работающих с `StorageService` и файлами сцен) для предоставления комплексных AI-функций.
- **`ai-books.goals`:** Может использовать данные о развитии персонажей (из `StorageService`) для постановки целей или отслеживания прогресса.
