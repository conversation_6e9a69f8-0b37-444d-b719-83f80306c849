# Архитектура Расширенного Поиска и Замены

Этот документ описывает архитектуру системы расширенного поиска и замены в AI-Books IDE, позволяющей пользователям эффективно находить и модифицировать текст во всем проекте.

## 1. Цели

- Обеспечить поиск текстовых строк и регулярных выражений во всех релевантных файлах проекта (сцены, заметки, возможно, метаданные).
- Предоставить опции поиска (учет регистра, целое слово).
- Отображать результаты поиска в удобном виде (например, в Sidebar) с контекстом и возможностью перехода к месту находки.
- Реализовать функцию замены найденных вхождений (по одному или все сразу).
- (Опционально) Интегрировать семантический поиск или поиск по структурированным данным (например, "найти всех персонажей с чертой 'Храбрый'").

## 2. Основные Компоненты

### 2.1. `SearchView` (Компонент React, Renderer Process)

- **Расположение:** `src/renderer/components/Sidebar/views/SearchView.view.tsx` (предполагаемое).
- **Ответственность:** Предоставление UI для ввода поискового запроса, опций и отображения результатов.
  - Содержит поля для ввода строки поиска и строки замены.
  - Содержит элементы управления для опций (учет регистра, целое слово, регулярное выражение).
  - Отображает дерево результатов поиска, сгруппированных по файлу.
  - Для каждого результата показывает строку с контекстом и выделенным совпадением.
  - Обрабатывает клики по результатам для навигации к месту в редакторе.
  - Предоставляет кнопки для выполнения замены (заменить одно, заменить все).
- **Регистрация:** Должен быть зарегистрирован как вид (`view`) для `Sidebar` через `ViewService`.

### 2.2. `SearchService` (Ядро, Main Process)

- **Расположение:** `src/main/search.service.ts` (предполагаемое).
- **Ответственность:** Выполнение операций поиска и замены в файловой системе или базе данных.
  - Получает запросы на поиск (`invoke('search:find', { query, options })`) и замену (`invoke('search:replace', { query, replacement, options, results? })`) от Renderer.
  - Определяет список файлов/источников данных для поиска (на основе текущего проекта/книги).
  - Выполняет поиск текста или регулярного выражения в контенте файлов/данных, учитывая опции.
    - Для файловой системы может использовать быстрые утилиты поиска (`ripgrep`) или Node.js `fs` API.
    - Для БД может использовать SQL `LIKE` или специфичные функции поиска.
  - Формирует список результатов (`SearchResultItem[]`) с указанием URI, диапазона (`Range`) и контекстной строки.
  - Возвращает результаты поиска в Renderer.
  - При запросе на замену:
    - Перечитывает файлы/данные.
    - Выполняет замену (по одному или все сразу).
    - Сохраняет измененные файлы/данные (возможно, через `StorageService` или напрямую).
    - Уведомляет Renderer об успешном выполнении или ошибках.
- **`SearchResultItem` (Интерфейс):**
  - `uri: string`: URI файла или идентификатор источника данных.
  - `range: Range`: Точный диапазон совпадения.
  - `preview: { line: number; text: string; matchRange: Range }`: Контекстная строка с номером и диапазоном совпадения внутри строки.

### 2.3. Интеграция с Редактором и Навигацией

- **Ответственность:** Открытие файла и переход к месту совпадения.
- **Реализация:**
  - При клике на результат в `SearchView`:
    - Вызывается команда ядра (например, `workbench.action.openEditor`) с `uri` и опциями для выделения `range` результата.
    - Main процесс открывает редактор и передает команду на выделение в Renderer.

## 3. Процесс Работы (Поиск)

1.  **Ввод Запроса (Renderer):** Пользователь вводит текст/regex и опции в `SearchView`.
2.  **Запрос Поиска (Renderer -> Main):** `SearchView` отправляет `invoke('search:find', { query, options })`.
3.  **Выполнение Поиска (Main):** `SearchService` получает запрос, определяет файлы/источники, выполняет поиск по контенту.
4.  **Формирование Результатов (Main):** `SearchService` собирает `SearchResultItem[]`.
5.  **Ответ (Main -> Renderer):** `SearchService` возвращает массив результатов.
6.  **Отображение (Renderer):** `SearchView` получает результаты и рендерит дерево совпадений.
7.  **Навигация (Renderer -> Main -> Renderer):** Пользователь кликает на результат -> `SearchView` вызывает `workbench.action.openEditor` -> Main открывает редактор -> Renderer (редактор) выделяет совпадение.

## 4. Процесс Работы (Замена)

1.  **Ввод Замены (Renderer):** Пользователь вводит текст для замены в `SearchView`.
2.  **Запрос Замены (Renderer -> Main):** Пользователь нажимает "Заменить все" (или "Заменить" для конкретного результата). `SearchView` отправляет `invoke('search:replace', { query, replacement, options, results? })`.
3.  **Выполнение Замены (Main):** `SearchService` получает запрос, перечитывает файлы/данные, выполняет замены в контенте.
4.  **Сохранение (Main):** `SearchService` сохраняет измененные файлы/данные.
5.  **Уведомление (Main -> Renderer):** `SearchService` возвращает статус операции (успех/ошибка, количество замен) и/или отправляет событие для обновления открытых редакторов.

## 5. Преимущества

- **Эффективность:** Быстрый поиск по всему проекту.
- **Мощность:** Поддержка регулярных выражений и опций поиска/замены.
- **Удобство:** Интегрированный UI для поиска, просмотра результатов и выполнения замен.
- **Надежность:** Операции замены выполняются централизованно в Main процессе.
