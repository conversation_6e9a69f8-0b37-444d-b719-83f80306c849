# Архитектура Системы Фрагментов (Snippets)

Этот документ описывает архитектуру системы фрагментов (сниппетов) в AI-Books IDE, предназначенной для ускорения ввода часто используемых текстовых шаблонов.

## 1. Цели

- Позволить пользователям быстро вставлять предопределенные текстовые блоки (например, диалоговые теги, описания действий, шаблонные структуры сцен) с помощью коротких префиксов.
- Поддерживать плейсхолдеры и точки табуляции внутри сниппетов для быстрой кастомизации вставленного текста.
- Обеспечить возможность определения сниппетов как на уровне пользователя, так и на уровне проекта (книги).
- Позволить расширениям предоставлять свои наборы сниппетов для специфичных задач или жанров.

## 2. Основные Компоненты

### 2.1. `SnippetService` (Сервис, Renderer Process)

- **Расположение:** `src/renderer/core/snippet.service.ts` (предполагаемое).
- **Ответственность:** Управление жизненным циклом и предоставление сниппетов.
  - Загружает и парсит файлы сниппетов (JSON) из различных источников (пользовательские, проектные, расширений).
  - Хранит загруженные сниппеты в памяти, индексируя их по префиксу и области видимости (language ID).
  - Предоставляет API для редактора для поиска подходящих сниппетов по текущему префиксу и контексту.
  - Может обрабатывать логику плейсхолдеров и точек табуляции после вставки.
- **Взаимодействие:** Получает информацию о путях к файлам сниппетов от Main процесса через IPC. Предоставляет данные редактору Lexical.

### 2.2. Формат Сниппетов (JSON)

- **Ответственность:** Определяет структуру файлов, содержащих сниппеты. Формат, схожий с VS Code.
- **Структура:** Объект JSON, где каждый ключ - это _название_ сниппета (для организации), а значение - объект со следующими полями:
  - `prefix: string | string[]`: Префикс(ы), по которому(ым) вызывается сниппет.
  - `body: string | string[]`: Тело сниппета. Массив строк для многострочных сниппетов. Поддерживает синтаксис плейсхолдеров (`$1`, `$2`, `${1:placeholder}`) и точек табуляции (`$0` - финальная позиция).
  - `description?: string`: Краткое описание, отображаемое в UI предложений.
  - `scope?: string`: Область видимости (language ID, например, 'scene-text', 'markdown'), определяющая, где активен сниппет. Если не указан, активен везде.

```json
// Пример файла my-snippets.json
{
  "Print to console": {
    "prefix": "log",
    "body": ["console.log('$1');", "$0"],
    "description": "Log output to console"
  },
  "Character Action": {
    "prefix": ["action", "действие"],
    "body": "*${1:Персонаж} ${2|делает,говорит,думает|} ${3:что-то}*",
    "description": "Описание действия персонажа",
    "scope": "scene-text"
  }
}
```

### 2.3. `ConfigurationService` или `SnippetRegistry` (Ядро, Main Process)

- **Ответственность:** Обнаружение и предоставление путей к файлам сниппетов.
  - Находит стандартные файлы сниппетов пользователя и проекта.
  - Получает пути к файлам сниппетов, зарегистрированным расширениями через точку вклада `snippets`.
  - Передает список актуальных путей к файлам сниппетов в `SnippetService` (Renderer) через IPC при инициализации и при изменении конфигурации/расширений.

### 2.4. Точка Вклада `snippets`

- **Ответственность:** Позволяет расширениям регистрировать свои наборы сниппетов.
- **Механизм:** Расширение декларирует в своем манифесте (или регистрирует программно через `activate`):
  - `language`: ID языка/контекста, для которого предназначены сниппеты.
  - `path`: Относительный путь к файлу JSON со сниппетами внутри директории расширения.

### 2.5. Интеграция с Редактором (Lexical, Renderer Process)

- **Ответственность:** Обеспечение пользовательского взаимодействия со сниппетами.
- **Реализация:**
  - Плагин для Lexical, который отслеживает ввод пользователя.
  - При обнаружении потенциального префикса сниппета, запрашивает у `SnippetService` список подходящих сниппетов для текущего контекста/scope.
  - Отображает UI предложений (например, выпадающий список) с описаниями сниппетов.
  - При выборе сниппета пользователем (например, нажатием `Tab` или `Enter`):
    - Вставляет `body` сниппета в редактор.
    - Обрабатывает плейсхолдеры и точки табуляции, позволяя пользователю быстро заполнить их, перемещаясь по ним с помощью `Tab`.

## 3. Процесс Работы

1.  **Регистрация (Main):** Расширения регистрируют пути к своим файлам сниппетов. `ConfigurationService` собирает все пути (пользовательские, проектные, расширений).
2.  **Инициализация (Main -> Renderer):** `ConfigurationService` передает список путей в `SnippetService` (Renderer).
3.  **Загрузка (Renderer):** `SnippetService` загружает и парсит все указанные файлы сниппетов, индексируя их.
4.  **Ввод (Renderer):** Пользователь набирает текст в редакторе Lexical.
5.  **Запрос Предложений (Renderer):** Плагин редактора определяет префикс и запрашивает у `SnippetService` подходящие сниппеты.
6.  **Отображение Предложений (Renderer):** Редактор показывает UI с предложенными сниппетами.
7.  **Выбор и Вставка (Renderer):** Пользователь выбирает сниппет. Редактор вставляет тело сниппета и активирует режим заполнения плейсхолдеров (если они есть), управляемый `SnippetService` или плагином редактора.

## 4. Преимущества

- **Эффективность:** Значительно ускоряет ввод повторяющихся текстовых конструкций.
- **Консистентность:** Помогает поддерживать единый стиль форматирования или терминологии.
- **Расширяемость:** Позволяет легко добавлять новые наборы сниппетов через расширения.
- **Кастомизация:** Пользователи могут определять свои собственные сниппеты.
