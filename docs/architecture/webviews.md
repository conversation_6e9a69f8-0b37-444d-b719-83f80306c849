# Архитектура Веб-представлений (Webviews)

Этот документ описывает архитектуру использования веб-представлений (Webviews) в AI-Books IDE для рендеринга сложного UI, интеграции веб-контента или использования специфичных веб-библиотек.

## 1. Цели

- Позволить отображать контент или UI, который сложно или неэффективно реализовывать с использованием стандартных компонентов React и Electron API (например, сложные графы, 3D-визуализации, интерактивные карты, предпросмотр HTML/EPUB).
- Интегрировать существующие веб-библиотеки или веб-сервисы.
- Обеспечить безопасную изоляцию контента веб-представления от основного Renderer процесса и Node.js API.

## 2. Основные Компоненты и Технологии

### 2.1. Тег `<webview>` (Electron) или `<iframe>`

- **`<webview>`:**
  - **Технология:** Специальный тег Electron, работающий в отдельном процессе.
  - **Преимущества:** Лучшая изоляция и безопасность по сравнению с `<iframe>`. Больше контроля над загрузкой, разрешениями и коммуникацией через API Electron.
  - **Недостатки:** Может быть сложнее в интеграции с React-компонентами. Потребляет больше ресурсов (отдельный процесс).
- **`<iframe>`:**
  - **Технология:** Стандартный HTML-тег.
  - **Преимущества:** Простая интеграция в React. Меньше накладных расходов по сравнению с `<webview>`.
  - **Недостатки:** Меньший уровень изоляции (хотя атрибут `sandbox` помогает). Коммуникация ограничена `postMessage`.
- **Рекомендация:** Использовать **`<iframe>` с атрибутом `sandbox`** для большинства случаев из-за простоты интеграции с React, при условии тщательной настройки `sandbox` и использования `postMessage` для коммуникации. Использовать `<webview>` только если требуется максимальная изоляция или специфичные API Electron для управления представлением.

### 2.2. Компонент-Обертка React (Renderer Process)

- **Ответственность:** Инкапсуляция логики создания, управления и коммуникации с `<iframe>` (или `<webview>`).
  - Принимает `props`, такие как URL для загрузки или HTML-контент, а также данные для передачи в веб-представление.
  - Создает `<iframe>` с необходимыми атрибутами (`src`, `srcdoc`, `sandbox`, `allow`).
  - Устанавливает слушатель событий `message` для получения данных от веб-представления.
  - Предоставляет метод (через `ref` или `props`) для отправки сообщений в веб-представление с использованием `iframeRef.current.contentWindow.postMessage(...)`.

### 2.3. Preload Скрипт для Веб-представления (Опционально, но Рекомендуется)

- **Ответственность:** Безопасное предоставление API для коммуникации с хост-приложением (Renderer процессом) внутри изолированного контекста веб-представления.
- **Реализация:**
  - Загружается в `<iframe>` (если используется `src` для загрузки HTML-файла) или внедряется при использовании `srcdoc`.
  - Использует `window.addEventListener('message', ...)` для получения сообщений от хоста.
  - Предоставляет глобальный объект (например, `window.hostApi`) с методами для отправки сообщений хосту (`window.parent.postMessage(...)`).
  - **Важно:** Не должен предоставлять доступ к Node.js или Electron API.

### 2.4. Контент Веб-представления (HTML, JS, CSS)

- **Ответственность:** Реализация специфического UI или логики внутри веб-представления.
- **Реализация:**
  - Может быть статическим HTML/JS/CSS файлом, загружаемым через `src`.
  - Может быть HTML-строкой, генерируемой динамически и передаваемой через `srcdoc`.
  - Использует API, предоставленное Preload скриптом (или напрямую `postMessage`), для обмена данными с хост-приложением.

### 2.5. Безопасность

- **Ключевые Аспекты:**
  - **`sandbox` атрибут для `<iframe>`:** Обязателен для ограничения возможностей веб-представления. Значения должны быть минимально необходимыми (например, `allow-scripts`, `allow-same-origin` если нужно).
  - **`allow` атрибут для `<iframe>`:** Явно разрешает использование специфичных API (камера, микрофон и т.д.), если это необходимо. По умолчанию следует запрещать.
  - **Проверка Источника (`event.origin`):** При получении сообщений через `postMessage` (как в хосте, так и в веб-представлении) всегда проверять `event.origin`, чтобы убедиться, что сообщение пришло из доверенного источника.
  - **Санитизация Данных:** Любые данные, передаваемые между хостом и веб-представлением, должны быть санитизированы, особенно если они используются для генерации HTML или выполнения скриптов.
  - **Загрузка Ресурсов:** Ограничить загрузку ресурсов из внешних источников, если это возможно. Использовать `Content-Security-Policy`.

## 3. Процесс Работы (Пример: Отображение графа связей)

1.  **Компонент Хоста (Renderer):** React-компонент (например, `RelationshipGraphEditor`) рендерит компонент-обертку `WebviewWrapper`.
2.  **Создание `<iframe>` (Renderer):** `WebviewWrapper` создает `<iframe>` с `src` указывающим на HTML-файл графа (или использует `srcdoc`) и настроенным `sandbox`.
3.  **Загрузка Контента (Webview):** `<iframe>` загружает HTML/JS/CSS. Запускается JS-код для инициализации библиотеки визуализации графа (например, D3, Vis.js).
4.  **Передача Данных (Renderer -> Webview):** `RelationshipGraphEditor` получает данные о связях (например, из `StorageService` через IPC) и передает их в `WebviewWrapper`. `WebviewWrapper` отправляет данные в `<iframe>` через `iframe.contentWindow.postMessage({ type: 'loadGraph', data: graphData }, '*')`. (Вместо `*` лучше использовать конкретный `targetOrigin`).
5.  **Получение Данных (Webview):** JS-код внутри `<iframe>` (или его Preload скрипт) получает сообщение через `window.addEventListener('message', ...)`), проверяет `event.origin` и `event.data.type`, затем использует `event.data.data` для построения графа.
6.  **Взаимодействие (Webview -> Renderer):** Пользователь кликает на узел в графе. JS-код графа отправляет сообщение хосту: `window.parent.postMessage({ type: 'nodeClicked', nodeId: 'char-123' }, '*')`.
7.  **Обработка Взаимодействия (Renderer):** `WebviewWrapper` получает сообщение, проверяет `event.origin` и `event.data.type`, затем вызывает callback-функцию (`onNodeClick`), переданную ему из `RelationshipGraphEditor`. `RelationshipGraphEditor` может, например, открыть редактор для соответствующего персонажа.

## 4. Преимущества

- **Гибкость:** Позволяет использовать лучшие веб-технологии для конкретных задач визуализации или интеграции.
- **Изоляция:** Обеспечивает (при правильной настройке) безопасную среду для выполнения потенциально менее доверенного кода или рендеринга сложного контента.
- **Переиспользование:** Возможность интеграции существующих веб-библиотек.

## 5. Недостатки и Риски

- **Сложность Коммуникации:** Обмен данными через `postMessage` может быть громоздким для сложных взаимодействий.
- **Безопасность:** Неправильная настройка `sandbox` или отсутствие проверки `origin` может привести к уязвимостям.
- **Производительность:** Каждый `<iframe>` (и особенно `<webview>`) потребляет дополнительные ресурсы.
- **Стилизация и UX:** Может быть сложно добиться полной консистентности внешнего вида и поведения между основным UI и веб-представлением.
