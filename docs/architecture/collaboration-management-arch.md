# Техническая Архитектура: Collaboration Management System (Предлагаемая)

Этот документ описывает **предлагаемую** техническую архитектуру для системы управления коллаборацией (`Collaboration Management System`) в AI-Books IDE. Текущая архитектура ядра Electron **недостаточна** для полноценной коллаборации в реальном времени, поэтому требуется введение дополнительных компонентов, вероятно, включая внешний сервер.

## 1. Основные Вызовы и Подход

- **Реальное Время:** Необходимо обеспечить синхронизацию изменений, комментариев и статусов присутствия между несколькими клиентами практически мгновенно.
- **Конфликты:** Требуется надежный механизм разрешения конфликтов при одновременном редактировании (например, Operational Transformation (OT) или Conflict-free Replicated Data Types (CRDT)).
- **Масштабируемость:** Решение должно масштабироваться для поддержки множества одновременных сессий коллаборации.
- **Сложность Гибридного Хранилища:** Принятый гибридный подход (файлы сцен + БД для метаданных) усложняет коллаборацию, так как серверу необходимо синхронизировать изменения как в файлах (через Git или иным способом), так и в базе данных, обеспечивая их консистентность.
- **Предлагаемый Подход:** Использование **централизованного сервера** (Backend) для координации сессий, управления состоянием и разрешения конфликтов. Клиенты (Electron App) подключаются к серверу через WebSockets. Сервер отвечает за синхронизацию и с файловым хранилищем сцен, и с базой данных метаданных.

## 2. Предлагаемые Компоненты

### 2.1. Collaboration Backend Server (Внешний Компонент)

- **Ответственность:**
  - Управление аутентификацией и авторизацией пользователей для сессий коллаборации.
  - Управление "комнатами" или сессиями для каждого документа/проекта, над которым идет совместная работа.
  - Прием изменений (операций) от клиентов через WebSockets.
  - Применение алгоритма разрешения конфликтов (OT или CRDT) для синхронизации изменений.
  - Рассылка синхронизированных изменений всем подключенным клиентам в сессии.
  - Управление статусами присутствия пользователей.
  - Хранение и синхронизация комментариев.
  - Обработка логики блокировок (если используется пессимистичная блокировка для некоторых операций).
- **Технологии (Примеры):** Node.js, Python (Django/Flask), Go; WebSockets (Socket.IO, ws); База данных (PostgreSQL, MongoDB, Redis); Библиотеки для OT/CRDT (ShareDB, Yjs).

### 2.2. `CollaborationService` (Ядро, Main Process)

- **Ответственность:** Управление соединением с Backend сервером и координация на стороне клиента.
  - Устанавливает и поддерживает WebSocket соединение с Collaboration Backend Server.
  - Передает аутентификационные данные пользователя.
  - Отправляет локальные изменения (операции редактирования, комментарии) на сервер.
  - Получает синхронизированные изменения и статусы от сервера.
  - Передает полученные данные в Renderer процесс через IPC (`send('collaboration:update', ...)`).
  - Управляет локальным состоянием сессии (подключен ли, список участников).
- **Интеграция:** Взаимодействует с `AuthenticationService` (если есть), `StorageService` (для метаданных), файловой системой (для сцен), `NotificationService`.

### 2.3. `CollaborationUIManager` (Сервис/Store, Renderer Process)

- **Ответственность:** Управление состоянием и UI коллаборации в Renderer.
  - Получает обновления от `CollaborationService` (Main) через IPC (`on('collaboration:update', ...)`).
  - Обновляет состояние редактора (применяя удаленные изменения).
  - Отображает индикаторы присутствия, курсоры соавторов.
  - Управляет UI комментариев.
  - Отправляет локальные действия (редактирование, добавление комментария) в Main процесс (`invoke('collaboration:sendOperation', ...)`).

### 2.4. Интеграция с Редактором (Lexical, Renderer Process)

- **Ответственность:** Применение удаленных изменений и отправка локальных.
- **Реализация:**
  - Плагин для Lexical, который:
    - Преобразует локальные изменения в операции (OT/CRDT), понятные серверу.
    - Отправляет эти операции через `CollaborationUIManager`.
    - Получает удаленные операции от `CollaborationUIManager`.
    - Применяет удаленные операции к состоянию редактора, разрешая конфликты локально (если используется CRDT) или полагаясь на сервер (если OT).
    - Отображает удаленные курсоры/выделения.

### 2.5. Комментарии и Обратная Связь

- **Данные:** Хранятся на Backend сервере, связаны с конкретным документом и диапазоном текста.
- **UI (Renderer):** Компоненты для отображения комментариев (например, в боковой панели или инлайн), добавления новых, ответа и изменения статуса (resolved). Взаимодействуют с `CollaborationUIManager` и `CollaborationService` для отправки/получения данных комментариев.

### 2.6. Управление Доступом

- **Логика:** Реализуется преимущественно на **Collaboration Backend Server**.
- **Интеграция (Main/Renderer):** `CollaborationService` передает токен пользователя. UI может запрашивать и отображать текущие права доступа, но не принимает решений по авторизации.

## 3. Процесс Работы (Пример: Совместное Редактирование)

1.  **Начало Сессии (Client -> Server):** Пользователь А открывает документ для совместной работы. `CollaborationService` подключается к Backend серверу, аутентифицируется и присоединяется к "комнате" документа.
2.  **Присоединение (Client -> Server):** Пользователь Б присоединяется к той же "комнате".
3.  **Обмен Статусами (Server -> Clients):** Сервер уведомляет А и Б о присутствии друг друга. `CollaborationUIManager` отображает индикаторы.
4.  **Локальное Редактирование (Client A):** Пользователь А вносит изменение в редакторе.
5.  **Отправка Операции (Client A -> Server):** Плагин редактора генерирует операцию (OT/CRDT), `CollaborationUIManager` передает ее `CollaborationService`, который отправляет ее на сервер через WebSocket.
6.  **Обработка и Рассылка (Server -> Clients):** Сервер получает операцию, применяет ее к состоянию документа (разрешая конфликты, если OT), и рассылает синхронизированную операцию всем клиентам в комнате (включая А для подтверждения).
7.  **Применение Удаленных Изменений (Client B):** `CollaborationService` клиента Б получает операцию, передает ее `CollaborationUIManager`. Плагин редактора применяет операцию к локальному состоянию.
8.  **Комментарий (Client Б -> Server -> Clients):** Пользователь Б добавляет комментарий. UI отправляет данные комментария через `CollaborationService` на сервер. Сервер сохраняет комментарий и рассылает уведомление всем клиентам. UI обновляется для отображения нового комментария.

## 4. Выводы

- Реализация полноценной коллаборации требует значительной инфраструктуры, выходящей за рамки стандартного Electron-приложения, включая разработку или интеграцию специализированного Backend сервера.
- Выбор между OT и CRDT является ключевым архитектурным решением, влияющим на сложность сервера и клиента.
- **Гибридное хранилище (файлы + БД) добавляет существенную сложность** в синхронизацию и разрешение конфликтов на стороне сервера.
- Безопасность, управление доступом и обработка конфликтов являются критически важными аспектами.
