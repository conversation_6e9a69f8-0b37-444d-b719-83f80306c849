# Архитектура Системы Запуска Задач (Task Runner)

Этот документ описывает архитектуру системы запуска задач (Task Runner) в AI-Books IDE, предназначенной для
автоматизации рутинных или сложных писательских и проектных задач.

## 1. Цели

- Позволить пользователям определять, находить и запускать предопределенные задачи (например, "Экспорт книги в PDF", "
  Проверка консистентности персонажей", "Создать резервную копию проекта", "Подсчитать статистику по главе").
- Обеспечить возможность расширениям регистрировать собственные задачи.
- Предоставлять обратную связь о ходе выполнения и результатах задачи (например, в панели "Вывод" или через
  уведомления).
- Поддерживать задачи разной природы: выполнение скриптов, вызов внутренних API, взаимодействие с командами ОС.

## 2. Основные Компоненты

### 2.1. `TaskService` (Ядро, Main Process)

- **Ответственность:** Центральный координатор для обнаружения, регистрации и выполнения задач.
    - Обнаруживает задачи, определенные пользователем (например, в файле `.ai-books/tasks.json`).
    - Регистрирует задачи, предоставляемые расширениями через точку вклада `tasks`.
    - Предоставляет API для получения списка доступных задач (`getTasks`).
    - Предоставляет API для запуска задачи по ее идентификатору (`runTask`).
    - Управляет процессом выполнения задачи (запуск, отслеживание состояния, обработка вывода, завершение).
    - Направляет вывод задачи в соответствующий канал (например, панель "Вывод").
- **API для Расширений (`context.tasks`):**
    - `registerTaskProvider(provider: TaskProvider): Disposable`: Регистрирует провайдера, который может динамически
      предоставлять задачи.
    - `registerTaskDefinition(definition: TaskDefinition): Disposable`: Регистрирует статически определенную задачу.

### 2.2. `TaskDefinition` (Интерфейс)

- **Ответственность:** Описывает статически определенную задачу.
- **Поля:**
    - `id: string`: Уникальный идентификатор задачи (например, `ai-books.export:pdf`).
    - `label: string`: Человекочитаемое имя задачи (отображается в UI).
    - `description?: string`: Краткое описание.
    - `type: 'shell' | 'process' | 'internal'`: Тип задачи.
        - `shell`: Выполняет команду в оболочке ОС.
        - `process`: Запускает исполняемый файл.
        - `internal`: Вызывает зарегистрированную команду приложения (`CommandService`).
    - `command?: string`: Команда для `shell` или `process`.
    - `args?: string[]`: Аргументы для команды.
    - `options?: { cwd?: string; env?: { [key: string]: string } }`: Опции запуска.
    - `internalCommandId?: string`: ID команды (`CommandService`) для `internal` задач.
    - `source: string`: Источник задачи (например, 'Workspace', 'Extension: ai-books.export').
    - `group?: 'build' | 'test' | 'export' | string`: Группа задачи (для UI).

### 2.3. `TaskProvider` (Интерфейс)

- **Ответственность:** Динамическое предоставление задач (например, на основе контекста проекта). Реализуется
  расширениями.
- **Интерфейс (`TaskProvider`):**
    - `id: string`: Уникальный идентификатор провайдера.
    - `provideTasks(context: TaskContext): Promise<TaskDefinition[]>`: Возвращает список задач, доступных в данном
      контексте.
    - `resolveTask?(task: TaskDefinition, context: TaskContext): Promise<TaskDefinition>`: Позволяет доопределить детали
      задачи перед выполнением (опционально).

### 2.4. Точка Вклада `tasks`

- **Ответственность:** Позволяет расширениям статически декларировать задачи в своем манифесте.
- **Механизм:** Расширение определяет массив `TaskDefinition` в секции `contributes.tasks` своего `package.json` (или
  аналогичного манифеста). `TaskService` автоматически обнаруживает и регистрирует их.

### 2.5. Интеграция с UI (Renderer Process)

- **Command Palette:**
    - Может включать команду "Запустить задачу..." (`Tasks: Run Task`).
    - При вызове этой команды, Renderer запрашивает у `TaskService` список доступных задач (`invoke('tasks:getList')`).
    - Отображает список задач пользователю (Quick Pick).
    - При выборе задачи, отправляет запрос на ее выполнение (`invoke('tasks:run', { taskId })`).
- **Панель "Вывод" (`OutputView`):**
    - Может отображать вывод выполняющихся задач.
    - `TaskService` (Main) отправляет события с выводом задачи (`send('task:output', { taskId, output })`).
    - `OutputView` подписывается на эти события и отображает вывод в соответствующем канале.
- **Статус Бар:**
    - Может отображать индикатор выполняющихся задач.

## 3. Процесс Работы (Пример: Запуск задачи экспорта)

1. **Регистрация/Обнаружение (Main):** `TaskService` обнаруживает задачу `ai-books.export:pdf`, зарегистрированную
   расширением "Export".
2. **Запрос Списка (Renderer -> Main):** Пользователь вызывает "Tasks: Run Task". Renderer запрашивает
   `invoke('tasks:getList')`.
3. **Предоставление Списка (Main -> Renderer):** `TaskService` собирает все задачи (статические и от провайдеров) и
   возвращает их список.
4. **Выбор Задачи (Renderer):** Пользователь выбирает "Экспорт книги в PDF" (`ai-books.export:pdf`).
5. **Запрос Запуска (Renderer -> Main):** Renderer отправляет `invoke('tasks:run', { taskId: 'ai-books.export:pdf' })`.
6. **Выполнение (Main):** `TaskService` находит определение задачи. Если тип `internal`, вызывает
   `CommandService.executeCommand(internalCommandId, ...)` с необходимыми аргументами. Если `shell` или `process`,
   запускает дочерний процесс.
7. **Перенаправление Вывода (Main -> Renderer):** `TaskService` перехватывает stdout/stderr дочернего процесса (или логи
   внутреннего обработчика) и отправляет их через `send('task:output', ...)`.
8. **Отображение Вывода (Renderer):** `OutputView` получает вывод и отображает его.
9. **Завершение (Main -> Renderer):** `TaskService` определяет завершение задачи (успешное или с ошибкой) и может
   отправить уведомление или обновить статус.

## 4. Преимущества

- **Автоматизация:** Упрощает выполнение повторяющихся или сложных действий.
- **Расширяемость:** Позволяет расширениям добавлять специфичные для них задачи.
- **Интеграция:** Задачи могут использовать внутренние API приложения (для типа `internal`).
- **Обратная связь:** Пользователь видит прогресс и результаты выполнения задач.
