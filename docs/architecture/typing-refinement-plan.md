# Plan for Improving Code Organization and Typing

This document outlines the plan for enhancing the TypeScript code organization and typing practices within the WriterStudio project.

## Prioritized Steps

The following steps will be undertaken, focusing first on refining type definitions:

1.  **Refine Type Definitions:**

    - Audit existing types for consistency (`interface` vs `type`). Establish and document a clear convention.
    - Identify candidates for branded types (e.g., `BookId`, `CharacterId`, `SceneId`, `ExtensionId`) and implement them to improve type safety.
    - Apply the `readonly` modifier to interface and type properties where appropriate to enhance immutability.
    - Refactor existing types using built-in TypeScript utility types (`Partial`, `Required`, `Readonly`, `Pick`, `Omit`, etc.) to reduce duplication.
    - Systematically eliminate any remaining explicit `any` types, replacing them with `unknown` and appropriate type checks or more specific types.
    - Ensure all exported types, interfaces, and complex type aliases have clear JSDoc comments explaining their purpose and usage.

2.  **Optimize Type Organization:**

    - Evaluate the current balance between placing types in `src/shared/types` versus colocating them with the code they relate to (e.g., within specific component or service directories). Define guidelines for future organization.
    - If any files within `src/shared/types` (or extension-specific `shared/types`) become excessively large or complex, split them into more granular, domain-focused files (e.g., `src/shared/types/ui/dialogs.ts`).
    - Review the use of barrel files (`index.ts`) for type directories. Ensure they simplify imports without causing issues like circular dependencies or significantly impacting bundle size/startup time.

3.  **Enhance Tooling:**
    - Update `tsconfig.json`:
      - Change `moduleResolution` from `"node10"` to `"NodeNext"` or `"Bundler"` after testing.
      - Consider enabling `noUnusedLocals` and `noUnusedParameters` and perform necessary cleanup.
      - Explore enabling `exactOptionalPropertyTypes` for stricter optional property checks.
    - Review and update `eslint.config.js`:
      - Add/enable rules like `@typescript-eslint/consistent-type-imports`.
      - Consider rules like `@typescript-eslint/explicit-module-boundary-types` for API clarity.
      - Add rules to enforce the chosen `interface` vs `type` convention.

## Organization Diagram

```mermaid
graph TD
    subgraph Core
        A[src/main] --> C{src/shared/types};
        B[src/renderer] --> C;
        P[src/preload] --> C;
        A --> A_types(src/main/.../types.ts);
        B --> B_types(src/renderer/.../types.ts);
    end

    subgraph Extensions
        Ext1[src/extensions/books/main] --> Ext1_Shared(src/extensions/books/shared/types.ts);
        Ext1_R[src/extensions/books/renderer] --> Ext1_Shared;
        Ext1 --> C;

        Ext2[src/extensions/characters/main] --> Ext2_Shared(src/extensions/characters/shared/types.ts);
        Ext2_R[src/extensions/characters/renderer] --> Ext2_Shared;
        Ext2 --> C;
    end

    style A_types fill:#eee,stroke:#333,stroke-width:1px,stroke-dasharray: 5, 5
    style B_types fill:#eee,stroke:#333,stroke-width:1px,stroke-dasharray: 5, 5

    linkStyle default stroke-width:1px,fill:none,stroke:gray;
```

_(This diagram shows core processes and extensions referencing the central `src/shared/types` as well as their own specific shared types. Dotted boxes indicate optional colocated types.)_
