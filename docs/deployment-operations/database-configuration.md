# Конфигурация Локальной Базы Данных (SQLite)

Этот документ описывает конфигурацию локальной базы данных, используемой десктопным приложением AI-Books на Electron.

## 1. Обзор

Приложение AI-Books использует **локальную базу данных SQLite** как основное хранилище для структурированных данных (метаданные книг, структура, персонажи, настройки и т.д.). Это обеспечивает полную **offline-first** функциональность.

- **Расположение:** Файл базы данных SQLite (`ai_books_data.sqlite` или аналогичное имя) хранится в папке пользовательских данных приложения (определяется Electron API `app.getPath('userData')`).
- **Доступ:** Доступ к базе данных осуществляется исключительно через **Main Process** Electron с использованием библиотеки `sqlite3` или ORM/Query Builder (например, `Knex.js`, `Sequelize` с адаптером SQLite).

## 2. Конфигурация SQLite

Конфигурация SQLite в контексте Electron довольно минималистична по сравнению с серверными БД.

- **Путь к Файлу:** Определяется динамически при запуске приложения.
- **Режим Журналирования (Journal Mode):** Рекомендуется использовать `WAL` (Write-Ahead Logging) для лучшей производительности при одновременном чтении и записи (хотя в однопользовательском десктопном приложении это менее критично, чем в веб). Настраивается через PRAGMA: `PRAGMA journal_mode = WAL;`.
- **Синхронный Режим (Synchronous Mode):** Можно использовать `NORMAL` (`PRAGMA synchronous = NORMAL;`) для баланса между скоростью и надежностью в большинстве случаев. `FULL` обеспечивает максимальную надежность, но медленнее.
- **Размер Кеша (Cache Size):** Можно настроить размер кеша страниц для оптимизации производительности (`PRAGMA cache_size = <количество_страниц>;`). Значение по умолчанию часто достаточно.
- **Внешние Ключи (Foreign Keys):** Рекомендуется включать поддержку внешних ключей для обеспечения целостности данных (`PRAGMA foreign_keys = ON;`). Это должно выполняться при каждом открытии соединения.

Пример инициализации соединения (используя `sqlite3`):

```javascript
// В Main Process
import sqlite3 from "sqlite3";
import path from "path";
import { app } from "electron";

const dbPath = path.join(app.getPath("userData"), "ai_books_data.sqlite");
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error("Error opening database", err.message);
  } else {
    console.log("Database connected successfully.");
    // Включить WAL режим и внешние ключи при каждом подключении
    db.exec(
      "PRAGMA journal_mode = WAL; PRAGMA foreign_keys = ON;",
      (execErr) => {
        if (execErr) {
          console.error("Error setting PRAGMAs", execErr.message);
        }
      }
    );
  }
});

// Экспортировать или использовать db для операций
```

## 3. Миграции Схемы

- Используется инструмент для управления миграциями схемы SQLite (например, `Knex.js`), запускаемый из Main Process при старте приложения для обновления структуры БД до последней версии.

## 4. Резервное Копирование

- Поскольку БД является локальным файлом, резервное копирование может осуществляться:
  - **Пользователем:** Путем копирования файла `ai_books_data.sqlite` вручную.
  - **Приложением:** Реализация функции периодического автоматического копирования файла БД в другую локальную папку или облачное хранилище пользователя (например, Dropbox, Google Drive), если предоставлен доступ.
  - **Через Опциональную Синхронизацию:** Если включена облачная синхронизация, данные реплицируются на сервер, что служит резервной копией.

## 5. Опциональная Облачная База Данных (Для Синхронизации)

- Если реализуется функция синхронизации/коллаборации, используется отдельная облачная база данных (например, PostgreSQL, MongoDB).
- Конфигурация этой БД (хост, порт, имя, пользователь, пароль) управляется на стороне сервера и не является частью конфигурации десктопного клиента, кроме URL API сервера синхронизации.
- Клиент взаимодействует с облачной БД только через защищенный API сервера синхронизации.

## 6. Безопасность Локальной БД

- Файл БД хранится в стандартной папке данных приложения, защищенной правами доступа операционной системы пользователя.
- Дополнительное шифрование файла БД возможно с использованием `sqlcipher` (форк SQLite с поддержкой шифрования), но это усложняет настройку и может повлиять на производительность. Обычно не требуется для стандартного десктопного приложения, если ОС обеспечивает достаточную защиту папки пользователя.
