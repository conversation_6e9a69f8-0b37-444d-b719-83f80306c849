# Стратегия Сборки и Распространения (Electron)

Этот документ описывает процесс сборки, подписи, распространения и обновления десктопного приложения AI-Books, созданного с помощью Electron.

## 1. Среды и Ветки

### Development

- **Источник:** Локальная разработка, feature-ветки.
- **Сборка:** Запускается локально для тестирования (`npm run start`). Не подписывается.

### Beta / Staging

- **Источник:** Ветка `develop` или специальные `beta/*` ветки.
- **Сборка:** Автоматическая через CI/CD при слиянии в `develop` или создании beta-тега.
- **Подпись Кода:** Используются beta/development сертификаты (если применимо).
- **Распространение:** Ограниченное (например, внутренняя команда, группа бета-тестеров) через GitHub Releases, прямой доступ или специализированный канал обновлений.
- **Цель:** Тестирование перед релизом, сбор обратной связи.

### Production

- **Источник:** Ветка `main` или релизные теги (например, `v1.0.0`).
- **Сборка:** Автоматическая через CI/CD при создании релизного тега.
- **Подпись Кода:** **Обязательно** используются production сертификаты для macOS и Windows.
- **Распространение:** Публичное (веб-сайт, App Store/Mac App Store, GitHub Releases).
- **Цель:** Выпуск стабильной версии для конечных пользователей.

## 2. Процесс Сборки (CI/CD)

Используется **Electron Forge** или **Electron Builder** для автоматизации сборки.

1.  **Триггер:** Создание тега релиза (например, `vX.Y.Z`) или слияние в `develop`/`main`.
2.  **Подготовка Среды:** Настройка CI/CD окружения (Node.js, npm/yarn). Установка зависимостей (`npm ci`).
3.  **Линтинг и Тесты:** Запуск линтеров (ESLint) и автоматических тестов (Jest, Playwright/Spectron). Сборка прерывается при ошибках.
4.  **Сборка Приложения:**
    - Запуск команды сборки (`npm run make` для Forge или `npm run build` для Builder).
    - Конфигурация сборки определяет целевые платформы (macOS, Windows, Linux) и форматы пакетов (dmg, pkg, exe, AppImage, deb, rpm и т.д.).
5.  **Подпись Кода (Code Signing):**
    - **macOS:** Использование сертификата "Developer ID Application" (для распространения вне App Store) или "Mac App Distribution" (для Mac App Store). Настройка `notarization` (нотаризация Apple) обязательна для распространения вне App Store. Секреты (сертификаты, пароли) хранятся в защищенном хранилище CI/CD.
    - **Windows:** Использование сертификата Authenticode (EV или OV). Секреты хранятся в защищенном хранилище CI/CD.
6.  **Создание Артефактов:** Сгенерированные установщики и пакеты (например, `.dmg`, `.exe`, `.AppImage`) сохраняются как артефакты сборки.
7.  **Публикация Релиза:**
    - Создание релиза на GitHub Releases (или аналогичной платформе).
    - Загрузка артефактов сборки (установщиков).
    - Публикация `latest.yml` / `latest-mac.yml` (для Electron Builder) или аналогичных файлов для системы автообновления.
    - Автоматическое обновление Changelog (если настроено).

## 3. Подпись Кода (Code Signing)

Подпись кода критически важна для доверия пользователей и обхода защитных механизмов ОС.

- **macOS:**
  - Требуется аккаунт Apple Developer Program.
  - Сертификаты: "Developer ID Application" (для Gatekeeper) и/или "Mac App Distribution" (для App Store).
  - **Нотаризация:** Обязательный процесс для приложений, распространяемых вне Mac App Store, для прохождения Gatekeeper на macOS Catalina и новее. Выполняется после подписи кода.
- **Windows:**
  - Требуется сертификат Authenticode (EV - Extended Validation рекомендуется для лучшего доверия SmartScreen, или OV - Organization Validation).
  - Сертификат приобретается у доверенного центра сертификации (CA).

## 4. Стратегия Распространения

- **Веб-сайт:** Основной канал. Ссылки на скачивание установщиков для macOS и Windows. Может включать ссылку на App Store/Mac App Store.
- **GitHub Releases:** Автоматическая публикация артефактов сборки CI/CD. Служит источником для автообновлений.
- **Магазины Приложений (Опционально):**
  - **Mac App Store:** Требует отдельной конфигурации сборки (`mas`, `mas-dev`) и прохождения ревью Apple. Имеет ограничения (sandbox).
  - **Microsoft Store:** Возможна публикация через MSIX пакеты.
- **Менеджеры Пакетов (Linux):** Публикация `.deb` и `.rpm` пакетов в репозиториях (например, через PPA или собственный репозиторий).

## 5. Автообновление

Используется встроенный модуль `autoUpdater` Electron в связке с Electron Forge/Builder или библиотекой `update-electron-app`.

- **Источник Обновлений:** Обычно GitHub Releases (требуется публичный репозиторий или приватный с токеном доступа) или выделенный сервер обновлений (например, Nuts, Nucleus).
- **Конфигурация:** Настройка URL сервера обновлений в `package.json` или коде приложения.
- **Процесс:**
  1.  Приложение периодически проверяет наличие обновлений при запуске или по интервалу.
  2.  Сравнивает текущую версию с последней доступной на сервере (используя `latest.yml` или аналоги).
  3.  При наличии новой версии скачивает обновление в фоновом режиме.
  4.  Уведомляет пользователя о готовности обновления и предлагает перезапустить приложение для установки.
- **Подпись Обновлений:** Обновления должны быть подписаны тем же сертификатом, что и исходное приложение.

## 6. Мониторинг и Отчеты об Ошибках

- **Сбор Ошибок:** Интеграция с сервисами типа Sentry (`sentry-electron`) для автоматического сбора и агрегации ошибок из Main и Renderer процессов.
- **Аналитика (Опционально):** Интеграция с системами аналитики (с соблюдением приватности пользователя) для отслеживания использования функций и стабильности приложения.
- **Обратная Связь:** Механизм для сбора обратной связи от пользователей внутри приложения.

## 7. Откат (Rollback)

- **Автообновление:** Система автообновления обычно не предоставляет простого механизма отката для конечных пользователей.
- **Стратегия:** Быстрый выпуск исправляющего обновления (hotfix). Пользователи, столкнувшиеся с проблемами, могут вручную скачать и установить предыдущую стабильную версию с GitHub Releases или веб-сайта.

## 8. Безопасность Распространения

- **Подпись Кода:** Обязательна для всех публичных релизов.
- **HTTPS:** Все скачивания и запросы на обновление должны идти через HTTPS.
- **Проверка Хешей:** Установщики и обновления могут сопровождаться хеш-суммами для проверки целостности.
