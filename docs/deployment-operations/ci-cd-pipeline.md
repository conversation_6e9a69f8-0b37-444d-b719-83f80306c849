# Документация CI/CD Пайплайна (Electron)

Этот документ описывает пайплайн Непрерывной Интеграции и Непрерывного Развертывания (CI/CD) для десктопного приложения AI-Books на Electron.

## 1. Обзор

CI/CD пайплайн автоматизирует процессы тестирования, линтинга, сборки, подписи кода и публикации релизов приложения для macOS, Windows и Linux. Он реализован с использованием GitHub Actions.

## 2. Архитектура Workflow (Пример для GitHub Actions)

```mermaid
graph TD
    A[Push / Tag] --> B{Проверка Триггера};
    B -- Pull Request --> C[Линтинг и Тесты];
    B -- Push to develop --> D[Сборка Beta];
    B -- Tag v*.*.* --> E[Сборка Production];

    subgraph "Общие Шаги"
        F[Настройка Среды (Node.js)];
        G[Установка Зависимостей];
        H[Линтинг (ESLint)];
        I[Модульные/Интеграционные Тесты (Jest)];
        J[E2E Тесты (Playwright/Spectron)];
    end

    C --> F --> G --> H --> I --> J;

    subgraph "Сборка и Публикация"
        K[Сборка macOS];
        L[Сборка Windows];
        M[Сборка Linux];
        N[Подпись Кода и Нотаризация macOS];
        O[Подпись Кода Windows];
        P[Публикация Артефактов];
        Q[Создание/Обновление Релиза];
    end

    D --> F --> G --> H --> I --> K;
    D --> L;
    D --> M;
    K --> P;
    L --> P;
    M --> P;
    P --> Q[Создание Beta Релиза];


    E --> F --> G --> H --> I --> J;
    E --> K --> N;
    E --> L --> O;
    E --> M;
    N --> P;
    O --> P;
    M --> P;
    P --> Q[Создание Production Релиза];

```

_Примечание: Сборка для разных ОС может выполняться параллельно на соответствующих runner'ах (macOS, Windows, Linux)._

## 3. Основные Workflows (GitHub Actions)

### 3.1 `ci.yml` (Проверка Pull Request и Push в develop/main)

- **Триггеры:** `pull_request` (на `develop`, `main`), `push` (на `develop`, `main`).
- **Задачи (Jobs):**
  1.  **Lint & Test:**
      - Настройка Node.js.
      - Установка зависимостей (`npm ci`).
      - Запуск ESLint.
      - Запуск модульных/интеграционных тестов (Jest).
      - (Опционально) Запуск E2E тестов (Playwright/Spectron) на одной ОС (например, Linux).

### 3.2 `release.yml` (Сборка и Публикация Релиза)

- **Триггеры:** `push` (теги вида `v*.*.*`).
- **Задачи (Jobs):**
  1.  **Lint & Test:** (Аналогично `ci.yml`, чтобы убедиться в качестве перед релизом).
  2.  **Build macOS:**
      - Запускается на `macos-latest` runner.
      - Настройка Node.js, установка зависимостей.
      - Сборка приложения (`npm run make` или `npm run build`).
      - **Подпись Кода:** Использование секретов для доступа к сертификату "Developer ID Application".
      - **Нотаризация:** Отправка приложения на нотаризацию Apple (требует App Store Connect API Key или учетных данных).
      - Загрузка артефактов (`.dmg`, `.zip`).
  3.  **Build Windows:**
      - Запускается на `windows-latest` runner.
      - Настройка Node.js, установка зависимостей.
      - Сборка приложения (`npm run make` или `npm run build`).
      - **Подпись Кода:** Использование секретов для доступа к сертификату Authenticode.
      - Загрузка артефактов (`.exe` установщик, `.zip`).
  4.  **Build Linux:**
      - Запускается на `ubuntu-latest` runner.
      - Настройка Node.js, установка зависимостей.
      - Сборка приложения (`npm run make` или `npm run build`).
      - Загрузка артефактов (`.AppImage`, `.deb`, `.rpm`).
  5.  **Publish Release:**
      - Запускается после успешного завершения всех сборок.
      - Создает релиз на GitHub Releases.
      - Загружает все артефакты (macOS, Windows, Linux) в релиз.
      - Публикует файлы для автообновления (`latest.yml` и т.д.).

### 3.3 `beta-release.yml` (Сборка и Публикация Beta)

- **Триггеры:** `push` (на `develop` или специальные `beta/*` ветки).
- **Задачи:** Аналогичны `release.yml`, но:
  - Может использовать beta/development сертификаты для подписи (если есть).
  - Публикует релиз как "pre-release" на GitHub Releases.
  - Может публиковать в отдельный канал обновлений.

## 4. Управление Секретами

Следующие секреты должны быть настроены в GitHub Secrets:

- **Apple ID и Пароль/App-Specific Password:** Для нотаризации macOS (`APPLE_ID`, `APPLE_APP_SPECIFIC_PASSWORD`).
- **App Store Connect API Key (Рекомендуется):** Для нотаризации (`APP_STORE_CONNECT_API_KEY_ID`, `APP_STORE_CONNECT_API_ISSUER_ID`, `APP_STORE_CONNECT_API_KEY_P8`).
- **Сертификат macOS (base64):** Сертификат "Developer ID Application" (`MACOS_CERT_P12_BASE64`).
- **Пароль к сертификату macOS:** (`MACOS_CERT_PASSWORD`).
- **Сертификат Windows (base64):** Сертификат Authenticode (`WINDOWS_CERT_P12_BASE64`).
- **Пароль к сертификату Windows:** (`WINDOWS_CERT_PASSWORD`).
- **GitHub Token:** Для публикации релизов (`GITHUB_TOKEN` - обычно предоставляется GitHub Actions автоматически).

## 5. Оптимизация Пайплайна

- **Кеширование:** Кеширование `node_modules` между запусками для ускорения установки зависимостей.
- **Параллелизм:** Запуск задач сборки для разных ОС параллельно.
- **Условное Выполнение:** Пропускать шаги подписи/нотаризации для beta-сборок или PR.

## 6. Мониторинг и Устранение Неполадок

- **Логи GitHub Actions:** Основной источник информации о ходе выполнения и ошибках.
- **Артефакты Сборки:** Анализ артефактов при ошибках сборки или подписи.
- **Локальный Запуск:** Воспроизведение шагов сборки локально для отладки (например, `npm run make --platform=win32`).

## 7. Лучшие Практики

- **Версионирование:** Использовать семантическое версионирование (`vX.Y.Z`). Версия в `package.json` должна обновляться перед созданием тега релиза.
- **Changelog:** Автоматически генерировать или обновлять `CHANGELOG.md` при создании релиза.
- **Тестирование на Разных ОС:** Убедиться, что E2E тесты (если есть) покрывают основные функции на всех целевых платформах.
