# Пример манифеста расширения

Ниже приведен пример манифеста расширения (`package.json`), который демонстрирует различные типы точек вклада (
contribution points).

```json
{
  "name": "my-extension",
  "publisher": "book-ide",
  "version": "0.0.1",
  "main": "main/index.js",
  "activationEvents": [],
  "contributes": {
    "viewContainers": [
      {
        "id": "book-ide.my-extension",
        "title": "My Extension",
        "icon": "Package",
        "viewId": "book-ide.my-extension.view"
      }
    ],
    "views": {
      "book-ide.my-extension": [
        {
          "id": "book-ide.my-extension.view",
          "name": "My Extension View",
          "componentName": "MyExtensionView",
          "location": "sidebar",
          "icon": "Package"
        }
      ]
    },
    "commands": [
      { 
        "command": "book-ide.my-extension.command1", 
        "title": "My Command 1",
        "category": "My Extension"
      },
      { 
        "command": "book-ide.my-extension.command2", 
        "title": "My Command 2",
        "category": "My Extension"
      }
    ],
    "menus": {
      "explorer/context": [
        { 
          "command": "book-ide.my-extension.command1", 
          "group": "1_modification@1" 
        }
      ],
      "editor/context": [
        { 
          "command": "book-ide.my-extension.command2", 
          "group": "1_modification@1" 
        }
      ]
    },
    "editors": [
      {
        "editorType": "book-ide.my-extension:editor",
        "componentName": "MyEditor",
        "icon": "FileText"
      }
    ],
    "aiTasks": [
      {
        "taskId": "my-task",
        "description": "My AI task",
        "inputSchema": {
          "type": "object",
          "properties": {
            "input": { "type": "string" }
          },
          "required": ["input"]
        },
        "outputSchema": {
          "type": "object",
          "properties": {
            "output": { "type": "string" }
          },
          "required": ["output"]
        }
      }
    ]
  }
}
```

## Пример кода расширения

Ниже приведен пример кода расширения, который демонстрирует, как регистрировать обработчики команд и IPC-обработчики.

```typescript
// src/extensions/my-extension/main/index.ts
import type { ExtensionContext } from '@main/extensions/extension.types';
import { Disposable } from '@shared/types/common';;
import { EXTENSION_ID } from '../shared/const';

export async function activate(context: ExtensionContext): Promise<any> {
  const logger = context.logger;
  logger.info(`Activating extension ${EXTENSION_ID}...`);

  // Регистрация обработчиков команд
  context.subscriptions.push(
    context.commands.registerCommand({
      id: `${EXTENSION_ID}.command1`,
      title: 'My Command 1',
      category: 'My Extension',
      handler: async () => {
        logger.info('Command 1 executed');
        // Реализация команды
      }
    })
  );

  context.subscriptions.push(
    context.commands.registerCommand({
      id: `${EXTENSION_ID}.command2`,
      title: 'My Command 2',
      category: 'My Extension',
      handler: async () => {
        logger.info('Command 2 executed');
        // Реализация команды
      }
    })
  );

  // Регистрация IPC-обработчиков
  context.subscriptions.push(
    context.ipc.handle('getData', async () => {
      logger.info('getData IPC handler called');
      return { data: 'some data' };
    })
  );

  logger.info(`Extension ${EXTENSION_ID} activated successfully.`);
}

export function deactivate(): Promise<void> {
  // Очистка ресурсов происходит автоматически через context.subscriptions
  return Promise.resolve();
}
```

## Пример константы EXTENSION_ID

```typescript
// src/extensions/my-extension/shared/const.ts
export const EXTENSION_ID = 'book-ide.my-extension';
```

## Пример компонента представления

```tsx
// src/extensions/my-extension/renderer/views/MyExtensionView.view.tsx
import React, { useEffect, useState } from 'react';
import { ipcRendererService } from '@renderer/core/services/ipcRendererService';
import { EXTENSION_ID } from '../../shared/const';

export const MyExtensionView: React.FC = () => {
  const [data, setData] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const result = await ipcRendererService.invoke(`${EXTENSION_ID}:getData`, {});
        setData(result.data);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div>
      <h3>My Extension View</h3>
      <p>Data: {data}</p>
    </div>
  );
};
```

## Пример компонента редактора

```tsx
// src/extensions/my-extension/renderer/editors/MyEditor.editor.tsx
import React, { useState, useEffect } from 'react';
import { ipcRendererService } from '@renderer/core/services/ipcRendererService';
import { EXTENSION_ID } from '../../shared/const';

interface MyEditorProps {
  dataId: string | null;
}

export const MyEditor: React.FC<MyEditorProps> = ({ dataId }) => {
  const [content, setContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!dataId) {
      setIsLoading(false);
      return;
    }

    const loadContent = async () => {
      try {
        setIsLoading(true);
        const result = await ipcRendererService.invoke(`${EXTENSION_ID}:getContent`, { dataId });
        setContent(result.content);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContent();
  }, [dataId]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
  };

  const handleSave = async () => {
    if (!dataId) return;

    try {
      await ipcRendererService.invoke(`${EXTENSION_ID}:saveContent`, { dataId, content });
    } catch (err) {
      setError(err as Error);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  if (!dataId) {
    return <div>No data ID provided</div>;
  }

  return (
    <div>
      <textarea
        value={content}
        onChange={handleContentChange}
        style={{ width: '100%', height: '300px' }}
      />
      <button onClick={handleSave}>Save</button>
    </div>
  );
};

export default MyEditor;
```
