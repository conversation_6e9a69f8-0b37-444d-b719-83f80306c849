# Encyclopedia & Reference System

## Who This Is For

This documentation is designed for:
- **Fiction writers** managing complex world information
- **Series authors** maintaining consistency across multiple works
- **Worldbuilders** organizing comprehensive fictional settings
- **Game designers** creating reference materials for shared worlds

No technical knowledge is required to understand or use this system.

## Overview

The Encyclopedia & Reference System provides tools for organizing, accessing, and presenting worldbuilding information in a structured format. It helps you maintain comprehensive documentation of your fictional world, access information efficiently while writing, and generate polished reference materials for readers.

## User Personas

### Epic Fantasy Author - <PERSON> is writing a multi-book fantasy series with dozens of characters, locations, and historical events. She uses the Encyclopedia system to:
- Organize thousands of worldbuilding details in a searchable format
- Track which characters know which information in her world
- Ensure consistent terminology across all her books
- Find relevant world information instantly while writing scenes
- Generate reader-friendly appendices for her published books
- Track which world details have been revealed to readers
- Create reference materials for her own use while writing

### Science Fiction Worldbuilder - <PERSON> is developing an expansive science fiction universe with multiple alien cultures and advanced technologies. He uses the Encyclopedia system to:
- Document technological systems with consistent principles
- Create comprehensive profiles of alien species and cultures
- Track the history and evolution of interstellar politics
- Visualize complex relationships between planetary systems
- Generate diagrams and visual references for technologies
- Create a glossary of specialized terminology and alien languages
- Produce sharable reference materials for collaborators

### Game Master - <PERSON> creates detailed worlds for tabletop role-playing games. He uses the Encyclopedia system to:
- Organize world lore in a format accessible during gameplay
- Control which information is revealed to players
- Generate handouts and reference materials for players
- Track character knowledge as the campaign progresses
- Create maps and visual aids for important locations
- Maintain a consistent history and timeline
- Adapt information presentation based on player discoveries

## Core Concepts

### Encyclopedia Structure

The system organizes worldbuilding knowledge in a structured framework:

- **Categories**: Top-level groupings of related information (geography, culture, etc.)
- **Entries**: Individual articles about specific world elements
- **Cross-References**: Links between related entries
- **Media Attachments**: Maps, images, diagrams, and other visuals
- **Tagging System**: Flexible categorization across multiple dimensions
- **Version Control**: Tracking changes to encyclopedia content over time

### Knowledge Management

The system supports different perspectives on world information:

- **Author Knowledge**: Complete information including secrets and future plans
- **Character Knowledge**: Information filtered by what specific characters know
- **Reader Knowledge**: Information filtered by what has been revealed in the narrative
- **Temporal Context**: Information appropriate to specific points in the timeline
- **Cultural Perspective**: Information colored by cultural viewpoints

### Reference Integration

The reference system integrates with the writing process:

- **Contextual Lookup**: Quick access to relevant world information while writing
- **Consistency Checking**: Verification against established facts
- **Terminology Management**: Consistent usage of world-specific terms
- **Background Generation**: Quick summaries of relevant world context
- **Progressive Disclosure**: Tracking revealed information across the narrative

## Auto-Generation System

The Encyclopedia includes tools for automatically generating content:

- **Entry Generation**:
  - Auto-creation from world element data
  - Structured format based on element type
  - Consistent style and terminology
  - Appropriate detail level based on importance
  - Cross-reference suggestion

- **Media Generation**:
  - Automatic map creation from location data
  - Relationship diagrams for characters and cultures
  - Timeline visualizations for historical events
  - Organizational charts for political structures
  - Family trees for character relationships

- **Update Tracking**:
  - Detection of changed world elements
  - Flagging outdated encyclopedia entries
  - Suggestion of new entries based on narrative content
  - Tracking inconsistencies between entries and world data
  - Version comparison for significant changes

## Search & Navigation

The system provides robust tools for finding information:

- **Multi-Faceted Search**:
  - Full-text search across all entries
  - Category and tag filtering
  - Attribute-specific searches (e.g., all mountain locations)
  - Relationship-based queries (e.g., all events involving a character)
  - Timeline-based filtering (e.g., all entries relevant to a specific era)

- **Navigation Systems**:
  - Hierarchical category browsing
  - Alphabetical index
  - Recently viewed entries
  - Related entry suggestions
  - Custom collections and bookmarks
  - Visualization-based navigation (maps, timelines, etc.)

## Knowledge Context System

The Encyclopedia provides contextual filtering of information:

- **Character Knowledge Filter**:
  - Shows only information a specific character would know
  - Adjusts detail level based on character expertise
  - Applies cultural bias based on character background
  - Updates as character learns new information
  - Identifies knowledge gaps for character development

- **Reader Knowledge Filter**:
  - Shows only information revealed in the narrative so far
  - Tracks progressive disclosure of world elements
  - Prevents spoilers in reference materials
  - Adjusts based on reading progress
  - Supports multiple reading paths for non-linear narratives

- **Timeline Context Filter**:
  - Adjusts information to specific points in the timeline
  - Shows world state as of a particular date
  - Hides future developments
  - Identifies historical knowledge available at a given time
  - Tracks changes to world elements over time

## User Interface Features

- **Encyclopedia Dashboard**: Overview of world encyclopedia with statistics
- **Entry Browser**: Interface for browsing and filtering entries
- **Entry Editor**: Detailed interface for creating and editing entries
- **Reference Browser**: Tool for navigating cross-references between entries
- **Category Manager**: Interface for organizing encyclopedia structure
- **Media Gallery**: Collection of visual elements attached to entries
- **Knowledge Filter**: Tool for viewing encyclopedia through different perspectives
- **Search Interface**: Advanced search with multiple filtering options
- **Export Designer**: Interface for customizing encyclopedia exports
- **Consistency Checker**: Tool for identifying inconsistencies and gaps

## Reader Material Generation

The Encyclopedia system supports generating reader-facing materials:

- **Book Appendices**:
  - Selective inclusion based on narrative content
  - Reader-appropriate knowledge filtering
  - Consistent style and formatting
  - Custom organization options
  - Integration with book publishing process

- **Interactive References**:
  - Digital encyclopedia with hyperlinked entries
  - Interactive maps with location details
  - Timeline visualizations with event information
  - Character relationship browsers
  - Multimedia integration with illustrations and audio

- **In-Book References**:
  - Footnotes or margin notes for complex concepts
  - Chapter-specific glossaries
  - Character reminders for large casts
  - Location context for scene changes
  - Historical reminders for complex timelines

## Writing Integration

The Encyclopedia integrates with the writing process:

- **Context Panel**:
  - Displays relevant encyclopedia entries while writing
  - Updates based on scene location and characters
  - Provides quick reference for world details
  - Allows quick creation of new entries for undefined elements
  - Tracks references to world elements in the narrative

- **Consistency Checking**:
  - Verifies narrative content against encyclopedia facts
  - Flags contradictions with established world elements
  - Identifies anachronisms and timeline inconsistencies
  - Checks character knowledge against revealed information
  - Validates terminology usage against glossary

- **Reference Insertion**:
  - Easy insertion of world element descriptions
  - Consistent terminology usage from glossary
  - Appropriate detail level based on context
  - Character-appropriate knowledge filtering
  - Style-consistent phrasing

## Common User Workflows

### Creating a Comprehensive World Encyclopedia

1. Define encyclopedia structure with main categories
2. Auto-generate entries from existing world elements
3. Refine and expand auto-generated content
4. Add cross-references between related entries
5. Upload and attach media elements
6. Organize entries with tags and categories
7. Check for consistency and completeness
8. Create custom collections for specific purposes
9. Export reference materials in desired formats

### Using Encyclopedia During Writing

1. Open writing interface with encyclopedia context panel
2. Reference location details for scene setting
3. Check character knowledge for dialogue accuracy
4. Verify historical timeline for event references
5. Access terminology glossary for consistent usage
6. Create new entries for newly developed world elements
7. Update existing entries as world details evolve
8. Track narrative references to world elements
9. Use consistency checker to validate narrative against encyclopedia

### Preparing Reader References for Publication

1. Decide which world elements should be included in reader references
2. Filter encyclopedia to only show reader-appropriate information
3. Organize entries into logical categories for reader navigation
4. Add appropriate visual elements like maps and diagrams
5. Adjust detail level for reader-friendly consumption
6. Ensure all terminology is consistent with the narrative
7. Create cross-references between related entries
8. Format appendix for your publication format (print, ebook, online)
9. Include only information revealed up to the current book in series

### Managing Collaborative Worldbuilding

1. Establish category structure and entry templates
2. Assign responsibility areas to different collaborators
3. Set up entry approval workflow for quality control
4. Use version tracking to monitor changes to entries
5. Run consistency checks to identify potential conflicts
6. Generate reports on recently modified entries
7. Use knowledge filters to verify character perspective accuracy
8. Create specialized views for different team members
9. Export reference materials for team use

## Integration with Other Systems

The Encyclopedia & Reference system integrates with:

- **Character System**: Track character knowledge and generate character histories
- **Location System**: Store detailed location information with maps
- **Timeline System**: Document historical events with proper chronology
- **Rules System**: Document magical systems, technologies, and world laws
- **Editor System**: Provide contextual information while writing
- **Export System**: Generate reader-appropriate reference materials

## For Technical Documentation

For implementation details, database schema, API endpoints, and service implementations, please refer to the [Encyclopedia & Reference Technical Documentation](/docs/technical/world-building/encyclopedia-reference-technical.md).

---

_Last Updated: March 11, 2025_  
_Version: 2.0.0_