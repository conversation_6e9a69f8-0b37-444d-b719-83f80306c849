# Geography & Locations Management

## Who This Is For

This documentation is designed for:
- **Fiction authors** creating detailed worlds with multiple locations
- **Game designers** establishing settings for player exploration
- **Worldbuilders** crafting coherent geographical systems
- **Series managers** maintaining consistency across multiple works

No technical knowledge is required to understand or use this system.

## Overview

The Geography & Locations component provides tools for creating and managing the physical spaces of your fictional world. It helps you organize locations hierarchically, establish spatial relationships, manage geographical features, and visualize your world through maps.

## User Personas

### Epic Fantasy Author - <PERSON> is writing a multi-book fantasy series with dozens of kingdoms, cities, and important landmarks. She uses the Geography system to:
- Create a continents-to-buildings hierarchy of all important locations
- Track political boundaries between rival kingdoms
- Calculate realistic travel times for character journeys
- Maintain consistency in environmental descriptions
- Ensure that locations mentioned in earlier books remain consistent

### Science Fiction Worldbuilder - <PERSON> is developing a science fiction universe with multiple planets and star systems. He uses the Geography system to:
- Create a galaxy with star systems, planets, and space stations
- Define environmental conditions on different planets
- Track travel routes and distances between celestial bodies
- Create detailed colony city maps with distinct districts
- Manage alien homeworlds with unique environmental features

### Game Narrative Designer - <PERSON> is creating the setting for a role-playing game. She uses the Geography system to:
- Design locations that will serve as quest hubs
- Create maps that can be revealed to players gradually
- Define environmental hazards and features that affect gameplay
- Link locations to specific in-game events and characters
- Create a consistent travel network between game areas

## Core Concepts

### Location Hierarchy

The system uses a hierarchical model for organizing locations:

- **Universe**: The highest level container (may contain multiple planets/realms)
- **World/Planet**: A discrete planetary body or primary realm
- **Continent/Realm**: Major landmass or dimensional space
- **Region/Country**: Political or geographical division
- **City/Settlement**: Population center
- **District/Area**: Section within a larger settlement
- **Building/Structure**: Individual constructed space
- **Room/Space**: Interior or specific location

Each level can contain multiple child locations while belonging to a single parent location, creating a tree-like structure that allows for precise navigation through the world's geography.

### Location Types

Locations can be classified into different types, each with specialized attributes:

- **Natural Locations**: Mountains, forests, rivers, oceans, etc.
- **Political Entities**: Countries, states, territories, etc.
- **Settlements**: Cities, towns, villages, outposts, etc.
- **Structures**: Buildings, monuments, ruins, etc.
- **Transit Locations**: Roads, bridges, ports, etc.
- **Mystical/Special**: Magical places, dimensional gateways, etc.

### Location Relationships

The system tracks several types of spatial relationships:

- **Containment**: Parent-child hierarchy (continent contains countries)
- **Adjacency**: Locations that border each other
- **Connection**: Locations linked by routes or passages
- **Proximity**: Relative distance between locations
- **Temporal**: Historical changes to locations over time

## Map Integration

### Map Types

The system supports multiple map representations:

- **World Maps**: Overall geographic representation
- **Regional Maps**: Detailed view of specific areas
- **City Maps**: Urban layout and districts
- **Building Blueprints**: Interior layouts
- **Conceptual Maps**: Relationship or transit maps

### Map Features

- **Multi-layer Maps**: Toggle visibility of different elements
- **Scalable Representation**: Zoom from world to local level
- **Annotation System**: Add notes and markers
- **Location Linking**: Connect map points to location database
- **Custom Legends**: Define map symbols and meaning
- **Distance Calculation**: Measure travel distances
- **Historical Versioning**: Show maps from different time periods

## Environmental System

The location system includes environmental attributes to create consistent settings:

- **Climate Zones**: Define temperature, precipitation, and seasonal patterns
- **Terrain Types**: Specify landscape characteristics
- **Flora & Fauna**: Associate plants and animals with regions
- **Resource Distribution**: Track natural resources by location
- **Weather Patterns**: Define typical and exceptional weather
- **Day/Night Cycles**: Track celestial patterns that affect the world
- **Natural Phenomena**: Special environmental events or features

## Location Context for Writing

The location system provides context-aware information during the writing process:

- **Scene Setting Panel**: Quick reference for current location details
- **Environmental Prompts**: Suggestions for sensory details based on location
- **Consistency Checking**: Verifies location details against established facts
- **Travel Time Calculation**: Estimates realistic journey durations
- **Location-Based Character Tracking**: Shows which characters are present

## User Interface Features

- **Location Hierarchy Browser**: Tree view of location structure
- **Location Detail View**: Comprehensive location information
- **Map Viewer/Editor**: Interactive map interface with editing tools
- **Relationship Diagram**: Visual representation of location connections
- **Location Finder**: Search and filter tool for locations
- **Scene-Location Linker**: Tool to connect scenes with locations
- **Travel Calculator**: Tool for determining journey times and routes
- **Climate & Environment Editor**: Interface for managing environmental attributes

## Common Workflows

### Creating a New World Geography

1. Start with a world-level location as the root
2. Add continent/region-level locations as children
3. Create political divisions within regions
4. Add settlements and points of interest
5. Define relationships between neighboring locations
6. Establish travel routes and connections
7. Upload or create maps for each level
8. Define environmental characteristics

### Using Locations in Writing

1. Select a scene's location from the hierarchy
2. Reference location details from context panel
3. Get automatic suggestions for environmental descriptions
4. Check distance and travel time when characters move
5. Verify consistency with previous mentions of the location
6. Add scene-specific details that don't affect the master location
7. Tag important location elements mentioned in the scene

### Planning Character Journeys

1. Identify starting and ending locations
2. Use the travel calculator to determine realistic travel time
3. View the route on maps to visualize the journey
4. Identify interesting locations along the route
5. Consider environmental factors that might affect travel
6. Link journey segments to specific scenes in your story
7. Track character positions throughout the narrative

### Developing Location-Based Plots

1. Browse the location hierarchy to find interesting settings
2. Review location details, history, and environmental factors
3. Consider political relationships between locations
4. Identify natural resources or special features that could drive conflict
5. Link characters and cultures to specific locations
6. Use the relationship diagram to find unexpected connections
7. Create location-specific story events tied to unique features

## Integration with Other Systems

The Geography & Locations system integrates with:

- **Character System**: Track character origins, current locations, and travel
- **Culture System**: Map cultural territories and distribution
- **Timeline System**: Show geographical changes over time
- **Scene System**: Provide setting details for narrative scenes
- **AI Assistant**: Generate location descriptions and consistency checks

## For Technical Documentation

For implementation details, database schema, API endpoints, and service implementations, please refer to the [Geography & Locations Technical Documentation](/docs/technical/world-building/geography-locations-technical.md).

---

_Last Updated: March 11, 2025_  
_Version: 2.0.0_