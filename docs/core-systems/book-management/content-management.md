# Book Content Management

## Overview

The Book Content Management system provides tools and services for managing the actual written content of books (primarily scenes) and associated metadata, leveraging the **hybrid storage and versioning architecture**. This document outlines how content is stored, edited, versioned, and analyzed.

(See [Book Management Technical Architecture](../../architecture/book-management-arch.md) and [Version Control Architecture](../../architecture/version-control.md) for technical details).

## Core Components

### Content Storage & Editing

- **Scene Content Storage**: Primary text content for scenes is stored as individual **Markdown files** in the project's file system.
- **Metadata Storage**: Information about scenes (title, position, path to the Markdown file), chapters, sections, and the book itself is stored in the **SQLite database** managed by `StorageService`.
- **Content Editor**: A rich text editor (based on Lexical, registered via `EditorService`) is used for editing the Markdown scene files. It handles the conversion between Markdown and the editor's internal state.

### Versioning System (Hybrid)

Content and metadata changes are tracked using a hybrid approach:

- **Scene Content Versioning (Git)**: Changes to **Markdown scene files** are tracked using **Git**, managed by `GitService`. This allows for branching, merging, history viewing, and comparison of scene text. Users interact with this via the Source Control view.
- **Metadata Versioning (Internal History)**: Changes to **structured data in SQLite** (book/chapter/scene metadata, character profiles, etc.) are tracked using a simpler internal history/snapshot mechanism within `StorageService`. This allows viewing previous states and potentially reverting metadata changes.
- **Version Comparison**: Git tools are used for comparing scene file versions. Custom viewers might be needed to compare snapshots of structured data.

### Content Analysis

(Provided primarily by **Analysis Servers** - see [`analysis-servers.md`](../../architecture/analysis-servers.md)).

The system provides tools for analyzing scene content (from files) and structure (from DB):

- **Word Count**: Calculated from scene files, aggregated per chapter/book.
- **Reading Time**: Estimated based on word count and complexity.
- **Text Statistics**: Readability, complexity, vocabulary metrics (calculated by `AnalysisProvider`s).
- **Content Patterns**: Analysis of structural and stylistic patterns (calculated by `AnalysisProvider`s).

## User Flows

### Content Creation and Editing

1. **Scene Selection**:

   - Navigate to scene in book structure (using `BookStructureView`).
   - View scene metadata (title, position from `StorageService`).
   - Select scene for editing (triggers opening the Markdown file in `SceneEditor`).

2. **Content Editing**:

   - Edit content in the Lexical-based rich text editor (which internally works with Markdown).
   - Format text using Markdown syntax or toolbar buttons.
   - Insert special elements (notes, comments - potentially managed by other systems).

3. **Content Saving**:
   - Automatic saving (debounced) triggers saving the editor state as a Markdown file via `BookStorage.saveSceneContent`.
   - `BookStorage` writes the file and stages it using `GitService`.
   - Manual saving (`Cmd/Ctrl+S`) forces the same process.
   - Creating explicit Git commits ("Save Snapshot") is done via the Source Control view.

### Version Management

1. **Version Creation**:

   - **Scene Files:** Primarily through Git commits initiated by the user via the Source Control view. Autosave stages changes but doesn't commit.
   - **Metadata (DB):** Automatic snapshots created by `StorageService` upon saving changes to entities like characters, world elements, or potentially book structure.

2. **Version Browsing**:

   - **Scene Files:** View Git history using `TimelineView` or `SourceControlView`.
   - **Metadata (DB):** View history snapshots via specific UI elements (e.g., "History" tab in editors).
   - Compare versions visually (Git diff for scenes, custom diff for metadata).

3. **Version Restoration**:
   - **Scene Files:** Use Git checkout/revert operations via `GitService` and UI.
   - **Metadata (DB):** Use `StorageService` API to revert to a specific historical snapshot.

### Content Analysis

1. **Basic Statistics**:

   - View word counts (calculated from scene files).
   - See reading time estimates.
   - Check writing pace (requires **Goals & Achievements System**).

2. **Advanced Analysis**: (Provided by **Analysis Servers**)

   - Analyze writing style.
   - Check readability metrics.
   - View vocabulary distribution.

3. **Content Patterns**: (Provided by **Analysis Servers**)
   - Identify repeated phrases.
   - Check dialogue/description ratio.
   - Analyze sentence structure patterns.

## AI Management System Integration

### Content Enhancement, Version Analysis, Content Generation

(These AI features leverage the **AI Management System** and **Analysis Servers**, operating on scene file content and potentially metadata from `StorageService`.)

## Best Practices

1. **Content Creation**

   - Utilize Markdown effectively for structure and basic formatting within scene files.
   - Keep scenes focused; split very long scenes into multiple files if editor performance degrades.

2. **Version Management**

   - Commit changes to scene files regularly using Git via the Source Control view with descriptive messages.
   - Rely on the internal history for tracking changes to metadata (characters, world, etc.). Understand its limitations (simpler history, no complex merging).

3. **Content Editing**

   - Focus on content creation first, then refinement using analysis tools.
   - Leverage editor features (like Outline view derived from Markdown headings) for navigation.

4. **Performance Considerations**
   - Git operations are generally fast for text files.
   - Performance of internal history for metadata depends on `StorageService` implementation.
   - Analysis server performance depends on the complexity of the analysis and whether it runs in a worker thread.
