# Технический Долг и План Улучшений MVP (AI-Books IDE)

Этот документ отслеживает технический долг, выявленный при сравнении документации MVP и текущей реализации (по состоянию на 04.04.2025), а также намечает план по его устранению.

## Основные Расхождения и Проблемы

1.  **Формат Хранения Сцен (Критично):** `SceneService` работает с файлами Markdown (`.md`), в то время как документация и схема БД (`BookStorage`) предполагают использование JSON-состояния редактора Lexical.
2.  ~~**Отсутствие Событий/Атрибутов Персонажей (Критично):**~~ **✅ РЕШЕНО (14.06.2025)** - События и атрибуты персонажей полностью реализованы. `CharacterStorage` теперь включает таблицы `character_events` и `character_attributes` с полными CRUD операциями, IPC обработчиками и UI компонентами. AI-задача `consistencyCheckTask` имеет доступ ко всем необходимым данным.
3.  **`GitService` - Заглушка:** Сервис для работы с Git является лишь заглушкой, что делает версионирование нефункциональным.
4.  **Отсутствие Секций:** Иерархический уровень "Секция" (Section) в структуре книги не реализован в `BookStorage`.
5.  **Сбор Контекста для AI:** Ответственность за сбор контекста для AI-задач переложена с `AIService` на обработчики IPC, что может усложнить их.
6.  **Устаревший `MarkdownInitializerPlugin`:** Файл `src/extensions/books/renderer/editors/MarkdownInitializerPlugin.tsx` больше не используется редактором (инициализация идет через JSON) и должен быть удален.

## Уточненный План Улучшений

1.  **Исправить Формат Хранения Сцен (Приоритет: Высокий):**

    - Модифицировать `SceneService` для работы с файлами JSON (`.json`), содержащими сериализованное состояние Lexical, вместо Markdown (`.md`).
    - Реализовать/интегрировать трансформер JSON <-> Markdown в `SceneService` для импорта/экспорта и генерации diff.
    - Убедиться, что `BookStorage` использует пути к `.json` файлам в поле `jsonFilePath`.

2.  ~~**Реализовать Хранение Событий и Атрибутов Персонажей (Приоритет: Высокий):**~~ **✅ ЗАВЕРШЕНО (14.06.2025)**

    - ✅ Добавлены таблицы `character_events` и `character_attributes` в схему БД (`CharacterStorage`).
    - ✅ Реализованы CRUD-операции для событий и атрибутов в `CharacterStorage`.
    - ✅ Реализована связь персонажей с книгами (поле `bookId` в `characters`).
    - ✅ Определены соответствующие типы TypeScript и реализованы обработчики IPC для событий/атрибутов.
    - ✅ Создана полнофункциональная UI для управления событиями (`CharacterProfileEvents` компонент).
    - ✅ Добавлена миграция БД для обновления существующих таблиц.
    - ✅ Интегрирована AI-задача проверки согласованности с событиями персонажей.

3.  **Реализовать `GitService` (Приоритет: Средний):**

    - Выбрать и интегрировать библиотеку для работы с Git (например, `simple-git`).
    - Реализовать методы интерфейса `GitServiceAPI`.
    - Определить конкретные типы для результатов Git-операций (вместо `any`).

4.  **Реализовать Секции (Sections) (Приоритет: Низкий):**

    - Добавить таблицу `sections` в схему БД (`BookStorage`).
    - Реализовать CRUD и логику управления секциями в `BookStorage`.
    - Обновить UI для поддержки секций.

5.  **Улучшить Типизацию (Приоритет: Средний):**

    - Заменить заполнители `any` на конкретные типы (особенно в `ipc.ts` для результатов/аргументов расширений и в `git.service.ts` для результатов Git).
    - Строго типизировать все IPC-взаимодействия.

6.  **Рефакторинг и Обработка Ошибок (Приоритет: Средний):**

    - Пересмотреть логику сбора контекста для AI-задач (в IPC-обработчике `characters`), особенно после исправления форматов данных и реализации событий (пункты 1 и 2). Решить, следует ли переносить логику обратно в `AIService`.
    - Улучшить обработку ошибок во всех сервисах (файловая система, БД, Git), используя стандартный формат `IpcErrorData`.
    - Обеспечить надежную работу ручного сохранения (Ctrl+S).

7.  **Расширить парсер `when` в `ContextService` (Приоритет: Низкий):**

    - Текущий парсер условий `when` поддерживает только базовые проверки (`key`, `!key`, `key == value`, `key != value`). Рассмотреть добавление поддержки `&&`, `||` или использование более мощной библиотеки для сложных контекстных правил.

8.  **Реакция `AIService` на изменение настроек (Приоритет: Средний):**

    - Реализовать прослушивание изменений настроек `ai.provider` и `ai.apiKey` в `AIService` для автоматической переинициализации AI-провайдера без перезапуска приложения.

9.  **Рефакторинг Крупных Файлов Ядра (Приоритет: Низкий):**

    - Файлы `app.ts`, `service.manager.ts`, `extensions/extension.registry.ts`, `ipc/handlers.ts`, `services/settings.service.ts`, `services/menu.service.ts`, `services/dialog.service.ts` имеют значительный размер (>200 строк). Рассмотреть рефакторинг (выделение хелперов, разделение по доменам) для улучшения читаемости и поддержки.

10. **Улучшение Расширяемости `AIService` (Приоритет: Средний):**

    - Модифицировать `AIService` для поддержки регистрации новых AI-провайдеров через механизм плагинов/регистрации, чтобы избежать необходимости изменять сам сервис при добавлении поддержки других моделей или API.

11. **Документирование Зависимостей Сервисов (Приоритет: Очень Низкий):**
    - Рассмотреть возможность явного документирования или визуализации графа зависимостей между основными сервисами для улучшения понимания и поддержки системы.

## Детальный План Реализации (Приоритетный)

### Фаза 1: Character Events System (Критичный приоритет)

**Цель:** Реализовать систему событий персонажей - ключевую дифференцирующую функцию продукта.

#### 1.1 Database Schema Enhancement

- [x] Добавить таблицы `character_events` и `character_attributes` в `CharacterStorage`
- [x] Добавить поле `bookId` в таблицу `characters` для связи с книгами
- [ ] Реализовать миграции БД для обновления существующих данных

#### 1.2 Backend Implementation

- [x] Расширить `CharacterStorage` CRUD-операциями для событий и атрибутов
- [x] Добавить IPC-обработчики для управления событиями
- [x] Обновить типы TypeScript для новых сущностей
- [x] Реализовать связывание событий со сценами

#### 1.3 Frontend Components

- [ ] Расширить `CharacterProfileEditor` вкладкой "События"
- [ ] Создать форму создания/редактирования событий
- [ ] Добавить визуализацию временной шкалы персонажа
- [ ] Реализовать UI для связывания событий со сценами

#### 1.4 AI Integration Fix

- [x] Исправить AI-задачу консистентности персонажей для использования реальных данных событий
- [x] Обновить сбор контекста в IPC-обработчиках
- [ ] Тестирование работы AI-анализа с реальными данными

**Ожидаемый результат:** Полнофункциональная система отслеживания развития персонажей через события.

### Фаза 2: Scene Content Format Fix (Высокий приоритет)

**Цель:** Исправить несоответствие формата хранения сцен.

#### 2.1 SceneService Refactoring

- [ ] Модифицировать `SceneService` для работы с JSON-файлами вместо Markdown
- [ ] Реализовать трансформер JSON ↔ Markdown для импорта/экспорта
- [ ] Обновить все пути файлов с `.md` на `.json`

#### 2.2 Data Migration

- [ ] Создать скрипт миграции существующих Markdown-файлов в JSON
- [ ] Обновить схему БД для использования `jsonFilePath`
- [ ] Тестирование миграции на тестовых данных

#### 2.3 Editor Integration

- [ ] Убедиться в корректной работе Lexical editor с JSON-состоянием
- [ ] Удалить устаревший `MarkdownInitializerPlugin`
- [ ] Обновить автосохранение для работы с JSON

**Ожидаемый результат:** Единообразное использование JSON для хранения состояния редактора.

### Фаза 3: GitService Implementation (Средний приоритет)

**Цель:** Реализовать полнофункциональное версионирование.

#### 3.1 Git Library Integration

- [ ] Установить и настроить `simple-git` или аналогичную библиотеку
- [ ] Реализовать методы `GitServiceAPI`
- [ ] Добавить типизацию для результатов Git-операций

#### 3.2 Version Control Features

- [ ] Реализовать автоматические коммиты при сохранении
- [ ] Добавить UI для просмотра истории версий
- [ ] Реализовать сравнение версий (diff)

**Ожидаемый результат:** Полнофункциональная система версионирования контента.

## Статус Выполнения

### Завершено (14 июня 2025):

#### Character Events System - Backend (75% готово)

- [x] **Типы и схема данных:** Полностью определены типы для CharacterEvent, CharacterAttribute, CharacterProfile
- [x] **База данных:** Реализованы таблицы character_events и character_attributes с каскадным удалением
- [x] **CRUD операции:** Полный набор операций для событий и атрибутов персонажей
- [x] **IPC интеграция:** Добавлены все необходимые IPC-обработчики для событий и атрибутов
- [x] **AI интеграция:** Исправлена задача консистентности для работы с реальными данными событий
- [x] **Временная шкала:** Реализованы запросы для получения событий по временным диапазонам

### В работе:

- **Character Events System - Frontend** - требуется реализация UI компонентов

### Следующие задачи по приоритету:

1. Scene Content Format Fix
2. GitService Implementation
3. Sections Support
4. UI/UX полировка

---

## ✅ Завершенные Значимые Задачи

### Character Events System Implementation (14.06.2025)

**Статус:** ✅ Полностью завершено и протестировано

**Реализованная функциональность:**

- Полнофункциональная система отслеживания развития персонажей на основе событий
- База данных: таблицы `character_events` и `character_attributes` с правильными связями
- CRUD операции: создание, чтение, обновление и удаление событий и атрибутов
- UI компоненты: `CharacterProfileEvents` с полным интерфейсом управления событиями
- IPC инфраструктура: все необходимые каналы связи между Main и Renderer процессами
- AI интеграция: подготовлена инфраструктура для проверки согласованности персонажей
- Миграция БД: автоматическое обновление существующих таблиц без потери данных
- Timeline система: события упорядочены по позиции на временной шкале (0-100)
- Impact система: оценка влияния событий на развитие персонажа (1-10)
- Связи со сценами: события могут быть привязаны к конкретным сценам

**Типы событий:**

- Personality: изменения личности персонажа
- Goals: цели и мотивации
- Relationships: отношения с другими персонажами
- Conflicts: конфликты и моральные дилеммы

**Технические достижения:**

- Надежная архитектура с правильным разделением ответственности
- Полная типизация TypeScript для всех компонентов
- Обработка ошибок и валидация данных
- Готовность к интеграции с AI-системами
- Масштабируемая структура для будущих расширений

Система характеризует ключевую дифференцирующую возможность AI-Books IDE и готова к продуктивному использованию.

**Обновлено:** 14 июня 2025 г.
