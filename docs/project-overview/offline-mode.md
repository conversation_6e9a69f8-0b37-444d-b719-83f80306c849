# Система Локального Хранения и Опциональной Синхронизации (Electron)

## 1. Обзор Архитектуры

AI-Books как десктопное приложение на Electron спроектировано по принципу **offline-first**. Основное хранилище данных находится локально на компьютере пользователя, обеспечивая полную функциональность без постоянного подключения к интернету. Опциональная облачная синхронизация позволяет создавать резервные копии и работать на нескольких устройствах.

```mermaid
graph TD
    A[Локальное Хранилище] --> B[Файловая Система / SQLite]
    A --> C[Управление Данными (Main Process)]
    A --> D[Доступ к Данным (Renderer через IPC)]

    subgraph Опциональная Синхронизация
        E[Менеджер Синхронизации (Main Process)]
        F[Обнаружение Сети]
        G[Разрешение Конфликтов (при Multi-Device)]
        H[API Сервер (Облако)]
    end

    C --> B
    D -- IPC --> C
    E --> F
    E --> G
    E -- HTTPS --> H
```

**Ключевые принципы:**

1.  **Локальное первенство:** Все данные создаются и хранятся локально. Приложение полностью работоспособно офлайн.
2.  **Нативное хранилище:** Используется файловая система или встроенная база данных (например, SQLite) для надежного хранения данных.
3.  **Опциональная синхронизация:** Подключение к сети используется для резервного копирования и синхронизации между устройствами (если включено пользователем).
4.  **Безопасность:** Данные хранятся в пользовательском пространстве, доступ к ним контролируется приложением.

## 2. Основные Компоненты

### 2.1 Локальное Хранилище (Файловая Система / SQLite)

-   **Структура данных:** Четко определенная структура папок и файлов или схема базы данных SQLite для хранения книг, глав, сцен, персонажей, настроек и т.д.
-   **Надежность:** Использование механизмов файловой системы или транзакций БД для обеспечения целостности данных.
-   **Производительность:** Оптимизированный доступ к локальным данным для быстрой загрузки и сохранения.
-   **Управление версиями:** Локальная система версионирования контента (может быть интегрирована с Git или использовать собственную реализацию).
-   **Шифрование (Опционально):** Возможность шифрования локальных файлов для дополнительной безопасности.

### 2.2 Управление Данными (Main Process)

-   **Централизованный доступ:** Модуль в основном процессе Electron отвечает за все операции чтения/записи в локальное хранилище.
-   **API для Renderer:** Предоставляет асинхронный API для renderer-процессов через IPC (Inter-Process Communication).
-   **Валидация данных:** Обеспечение корректности данных перед сохранением.
-   **Миграции:** Управление изменениями схемы данных при обновлениях приложения.

### 2.3 Менеджер Синхронизации (Опционально, Main Process)

-   **Отслеживание изменений:** Мониторинг локальных изменений для последующей отправки в облако.
-   **Очередь синхронизации:** Управление порядком и приоритетом отправки/получения данных.
-   **Взаимодействие с API:** Обмен данными с облачным сервером по защищенному протоколу (HTTPS).
-   **Обработка ошибок:** Механизмы повторных попыток при сбоях сети.
-   **Фоновый режим:** Синхронизация выполняется в фоновом режиме, не блокируя интерфейс.

### 2.4 Обнаружение Сети (Для Синхронизации)

-   **Проверка доступности сервера:** Использует Node.js модули для проверки сетевого подключения и доступности API сервера.
-   **Управление состоянием:** Информирует Менеджер Синхронизации о наличии или отсутствии подключения.

### 2.5 Разрешение Конфликтов (Для Синхронизации)

-   **Актуально при многоустройственной синхронизации:** Если пользователь редактирует данные на нескольких устройствах одновременно.
-   **Стратегии:** Могут использоваться стратегии "последняя запись побеждает", временные метки или предоставление пользователю интерфейса для ручного слияния.
-   **Версионирование:** Использование версий данных для облегчения обнаружения и разрешения конфликтов.

## 3. Модель Данных (Пример для Файловой Системы)

Данные могут храниться в структурированных файлах (JSON, Markdown, XML) в выделенной папке приложения.

```
UserData/
├── books/
│   ├── book_id_1/
│   │   ├── metadata.json       # { title: "...", author: "...", ... }
│   │   ├── structure.json      # Описание глав и сцен
│   │   ├── chapters/
│   │   │   ├── chapter_id_1.md # Содержимое главы 1
│   │   │   └── chapter_id_2.md # Содержимое главы 2
│   │   ├── characters/
│   │   │   ├── char_id_1.json  # Данные персонажа 1
│   │   │   └── char_id_2.json  # Данные персонажа 2
│   │   ├── world/
│   │   │   └── locations.json
│   │   └── versions/           # Локальная история версий
│   │       └── ...
│   └── book_id_2/
│       └── ...
├── settings/
│   └── app_settings.json
└── sync/
    ├── queue.json              # Очередь на синхронизацию (если включена)
    └── last_sync.json          # Информация о последней синхронизации
```

*Примечание: Использование SQLite может быть более производительным для сложных запросов и управления связями.*

## 4. Ключевые Функции

### 4.1 Нативная Офлайн-Работа

-   **Полная функциональность:** Все основные функции (редактирование, управление структурой, персонажами и т.д.) доступны без подключения к сети.
-   **Мгновенное сохранение:** Изменения сохраняются непосредственно в локальное хранилище.
-   **Надежность:** Минимизирован риск потери данных из-за проблем с сетью или браузером.

### 4.2 Опциональная Облачная Синхронизация

-   **Резервное копирование:** Автоматическое создание резервных копий в облаке.
-   **Многоустройственный доступ:** Возможность продолжить работу на другом устройстве с синхронизированными данными.
-   **Контроль пользователя:** Пользователь решает, включать ли синхронизацию и какие данные синхронизировать.
-   **Безопасность:** Передача данных по HTTPS, возможно дополнительное шифрование.

### 4.3 Управление Локальными Данными

-   **Прозрачность:** Пользователь может (опционально) иметь доступ к папке с данными приложения.
-   **Импорт/Экспорт:** Возможность легко импортировать и экспортировать данные книг в стандартных форматах.
-   **Локальное версионирование:** Отслеживание истории изменений непосредственно на диске.

## 5. Интеграция с Рабочими Процессами

### 5.1 Процесс Написания

-   Пользователь открывает приложение.
-   Данные загружаются из локального хранилища (файлы или SQLite).
-   Пользователь редактирует контент.
-   Изменения автоматически сохраняются локально через заданные интервалы или по действиям пользователя.
-   Если синхронизация включена и есть сеть, изменения добавляются в очередь на отправку в облако.

### 5.2 Процесс Синхронизации (Если Включена)

-   Приложение проверяет наличие сети и доступность сервера.
-   При наличии сети Менеджер Синхронизации:
    1.  Запрашивает изменения с сервера.
    2.  Применяет серверные изменения к локальным данным (при необходимости разрешая конфликты).
    3.  Отправляет локальные изменения из очереди на сервер.
    4.  Обновляет статус синхронизации.
-   Пользователь видит индикатор статуса синхронизации.

### 5.3 Разрешение Конфликтов (При Синхронизации)

-   Если Менеджер Синхронизации обнаруживает конфликт (локальные и серверные изменения одного и того же элемента), он помечает элемент как конфликтующий.
-   Пользователю предлагается интерфейс для разрешения конфликта (выбрать версию, объединить).
-   После разрешения конфликтная версия отправляется на сервер.

## 6. Архитектура Реализации (Electron)

### 6.1 Основные Сервисы (Main Process)

-   `StorageService`: Абстракция для работы с локальным хранилищем (FS или SQLite).
-   `SyncService` (Опционально): Управляет процессом синхронизации с облаком.
-   `ConflictResolver` (Опционально): Логика разрешения конфликтов.
-   `NetworkMonitor` (Опционально): Отслеживает состояние сети для `SyncService`.

### 6.2 Коммуникация (IPC)

-   Renderer-процессы (UI) отправляют запросы на чтение/запись данных в Main Process через `ipcRenderer.invoke`.
-   Main Process обрабатывает запросы с помощью `ipcMain.handle` и взаимодействует с `StorageService` или `SyncService`.
-   Main Process может отправлять события (например, обновление статуса синхронизации) в Renderer-процессы через `webContents.send`.

## 7. Компоненты Пользовательского Интерфейса

### 7.1 Индикаторы Статуса

-   **Индикатор Сохранения:** Показывает, что все локальные изменения сохранены.
-   **Индикатор Синхронизации (Если Включена):**
    -   Синхронизировано.
    -   Идет синхронизация.
    -   Ошибка синхронизации / Конфликт.
    -   Сеть недоступна.
-   **Детальный статус:** Панель или всплывающее окно с информацией о последней синхронизации, ожидающих изменениях, ошибках.

### 7.2 Настройки Синхронизации

-   Включение/выключение облачной синхронизации.
-   Вход в аккаунт для синхронизации.
-   Настройки частоты синхронизации.
-   Управление локальным хранилищем (просмотр размера, очистка кеша синхронизации).

### 7.3 Интерфейс Разрешения Конфликтов

-   Визуальное сравнение локальной и серверной версий.
-   Инструменты для выбора версии или ручного объединения.

## 8. Заключение

Архитектура AI-Books на Electron обеспечивает надежную **offline-first** работу, используя локальное хранилище. Опциональная облачная синхронизация добавляет гибкость для резервного копирования и многоустройственной работы, но не является обязательной для основной функциональности приложения. Это дает пользователям полный контроль над своими данными и возможность работать в любых условиях.
