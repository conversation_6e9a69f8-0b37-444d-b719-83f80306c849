# Архитектура текстового редактора для AI Books на основе Lexical

В этом документе описывается архитектура и принципы реализации текстового редактора - ключевого компонента приложения AI Books, основанного на фреймворке Lexical.

## Требования к текстовому редактору

### Функциональные требования

1. **Базовое редактирование текста**

   - Редактирование в режиме WYSIWYG (What You See Is What You Get)
   - Поддержка основных операций форматирования текста
   - Автосохранение
   - История изменений (undo/redo)

2. **Структурирование текста**

   - Разделение на главы и сцены
   - Иерархическая организация контента
   - Навигация по структуре

3. **Разметка и аннотирование**

   - Выделение персонажей
   - Маркировка локаций
   - Отметка сюжетных линий
   - Временные метки
   - Пользовательские теги

4. **Интеграция с AI-функциями**

   - Возможность выделения текста для анализа
   - Отображение рекомендаций в контексте
   - Визуализация результатов анализа
   - Интерактивная реакция на изменения

5. **Экспорт/импорт**
   - Экспорт в популярные форматы (PDF, EPUB, DOCX)
   - Импорт из текстовых форматов
   - Настраиваемые шаблоны экспорта

### Нефункциональные требования

1. **Производительность**

   - Быстрая загрузка редактора
   - Плавная работа с большими текстами (до 1000 страниц)
   - Эффективное использование памяти

2. **Пользовательский опыт**

   - Интуитивно понятный интерфейс
   - Отзывчивость интерфейса
   - Адаптивный дизайн
   - Поддержка тем оформления

3. **Надежность**
   - Предотвращение потери данных
   - Стабильная работа
   - Устойчивость к сбоям

## Почему Lexical

[Lexical](https://lexical.dev/) - это JavaScript-фреймворк для построения текстовых редакторов, разработанный Meta (Facebook). Он имеет ряд преимуществ, которые делают его оптимальным выбором для проекта AI Books:

1. **Производительность**

   - Оптимизирован для работы с большими документами
   - Эффективный рендеринг и управление состоянием
   - Минимальные затраты ресурсов

2. **Модульность и расширяемость**

   - Гибкая архитектура плагинов
   - Возможность создания кастомных узлов и функций
   - Легко интегрируется с React и другими фреймворками

3. **Современный подход**

   - Основан на последних технологиях JavaScript
   - Поддержка TypeScript
   - Активное развитие и сообщество

4. **Стабильность**
   - Разработан и используется в продакшене Meta
   - Хорошо протестирован
   - Надежная архитектура

## Архитектура редактора на основе Lexical

### Обзор компонентов

```mermaid
graph TD
    A[Текстовый редактор] --> B[Ядро Lexical]
    A --> C[Панель инструментов]
    A --> D[Система разметки]
    A --> E[Система автосохранения]
    A --> F[AI-интеграция]

    B --> G[Lexical Editor State]
    B --> H[Lexical Commands]
    B --> I[Lexical DOM Converters]

    D --> J[Кастомные ноды разметки]
    D --> K[Декораторы визуализации]

    F --> L[Интерфейс AI-рекомендаций]
    F --> M[Обработчик AI-анализа]
```

### Структура компонентов редактора

```
- TextEditor (корневой компонент)
  |- LexicalComposer (основной контейнер Lexical)
  |  |- LexicalRichTextPlugin (плагин для форматирования)
  |  |- LexicalAutoLinkPlugin (автоматические ссылки)
  |  |- LexicalHistoryPlugin (история изменений)
  |  |- AIBooksNodesPlugin (кастомные ноды)
  |  |- AutosavePlugin (автосохранение)
  |  |- AIAnnotationsPlugin (AI-разметка)
  |- EditorToolbar (панель инструментов)
  |  |- FormattingTools (инструменты форматирования)
  |  |- StructureTools (работа со структурой)
  |  |- AnnotationTools (инструменты разметки)
  |  |- AIAnalysisTools (инструменты AI-анализа)
  |- Sidebar (боковая панель)
  |  |- DocumentOutline (структура документа)
  |  |- CharacterList (список персонажей)
  |  |- LocationList (список локаций)
  |  |- TagManager (управление тегами)
  |- AIPanel (панель AI)
  |  |- RecommendationsView (просмотр рекомендаций)
  |  |- AnalysisResults (результаты анализа)
  |  |- ContextualFeedback (контекстная обратная связь)
```

### Пользовательские ноды Lexical

Для реализации специфичных для AI Books функций потребуется создать собственные типы узлов Lexical:

1. **ChapterNode** - для глав книги
2. **SceneNode** - для сцен внутри глав
3. **CharacterMarkNode** - для выделения персонажей
4. **LocationMarkNode** - для выделения локаций
5. **AIRecommendationNode** - для отображения рекомендаций

## Реализация кастомных узлов в Lexical

### Пример реализации ChapterNode

```typescript
// nodes/ChapterNode.ts
import {
  ElementNode,
  LexicalNode,
  NodeKey,
  SerializedElementNode,
} from "lexical";

export type SerializedChapterNode = SerializedElementNode & {
  chapterId: string;
  chapterTitle: string;
  type: "chapter";
  version: 1;
};

export class ChapterNode extends ElementNode {
  __chapterId: string;
  __chapterTitle: string;

  constructor(chapterId: string, chapterTitle: string, key?: NodeKey) {
    super(key);
    this.__chapterId = chapterId;
    this.__chapterTitle = chapterTitle;
  }

  static getType(): string {
    return "chapter";
  }

  static clone(node: ChapterNode): ChapterNode {
    return new ChapterNode(node.__chapterId, node.__chapterTitle, node.__key);
  }

  createDOM(): HTMLElement {
    const element = document.createElement("div");
    element.classList.add("chapter");
    element.setAttribute("data-chapter-id", this.__chapterId);
    element.setAttribute("data-chapter-title", this.__chapterTitle);
    return element;
  }

  updateDOM(): boolean {
    // Возвращаем false, т.к. DOM не нуждается в обновлении
    return false;
  }

  setChapterTitle(chapterTitle: string): void {
    const writable = this.getWritable();
    writable.__chapterTitle = chapterTitle;
  }

  getChapterTitle(): string {
    return this.__chapterTitle;
  }

  getChapterId(): string {
    return this.__chapterId;
  }

  exportJSON(): SerializedChapterNode {
    return {
      ...super.exportJSON(),
      chapterId: this.__chapterId,
      chapterTitle: this.__chapterTitle,
      type: "chapter",
      version: 1,
    };
  }

  static importJSON(serializedNode: SerializedChapterNode): ChapterNode {
    const node = $createChapterNode(
      serializedNode.chapterId,
      serializedNode.chapterTitle
    );
    return node;
  }
}

export function $createChapterNode(
  chapterId: string,
  chapterTitle: string
): ChapterNode {
  return new ChapterNode(chapterId, chapterTitle);
}

export function $isChapterNode(
  node: LexicalNode | null | undefined
): node is ChapterNode {
  return node instanceof ChapterNode;
}
```

### Пример реализации CharacterMarkNode

```typescript
// nodes/CharacterMarkNode.ts
import { TextNode, LexicalNode, NodeKey, SerializedTextNode } from "lexical";

export type SerializedCharacterMarkNode = SerializedTextNode & {
  characterId: number;
  characterName: string;
  type: "character-mark";
  version: 1;
};

export class CharacterMarkNode extends TextNode {
  __characterId: number;
  __characterName: string;

  constructor(
    text: string,
    characterId: number,
    characterName: string,
    key?: NodeKey
  ) {
    super(text, key);
    this.__characterId = characterId;
    this.__characterName = characterName;
  }

  static getType(): string {
    return "character-mark";
  }

  static clone(node: CharacterMarkNode): CharacterMarkNode {
    return new CharacterMarkNode(
      node.__text,
      node.__characterId,
      node.__characterName,
      node.__key
    );
  }

  createDOM(config: any): HTMLElement {
    const element = super.createDOM(config);
    element.classList.add("character-mark");
    element.setAttribute("data-character-id", String(this.__characterId));
    element.setAttribute("data-character-name", this.__characterName);
    element.style.backgroundColor = "#e8f0fe";
    element.style.borderRadius = "2px";
    element.style.padding = "0 2px";
    return element;
  }

  exportJSON(): SerializedCharacterMarkNode {
    return {
      ...super.exportJSON(),
      characterId: this.__characterId,
      characterName: this.__characterName,
      type: "character-mark",
      version: 1,
    };
  }

  static importJSON(
    serializedNode: SerializedCharacterMarkNode
  ): CharacterMarkNode {
    const node = $createCharacterMarkNode(
      serializedNode.text,
      serializedNode.characterId,
      serializedNode.characterName
    );
    return node;
  }
}

export function $createCharacterMarkNode(
  text: string,
  characterId: number,
  characterName: string
): CharacterMarkNode {
  return new CharacterMarkNode(text, characterId, characterName);
}

export function $isCharacterMarkNode(
  node: LexicalNode | null | undefined
): node is CharacterMarkNode {
  return node instanceof CharacterMarkNode;
}
```

## Регистрация кастомных узлов

Для использования кастомных узлов в редакторе, их необходимо зарегистрировать:

```typescript
// plugins/AIBooksNodesPlugin.tsx
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useEffect } from "react";
import { ChapterNode } from "../nodes/ChapterNode";
import { SceneNode } from "../nodes/SceneNode";
import { CharacterMarkNode } from "../nodes/CharacterMarkNode";
import { LocationMarkNode } from "../nodes/LocationMarkNode";
import { AIRecommendationNode } from "../nodes/AIRecommendationNode";

export function AIBooksNodesPlugin(): null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (
      !editor.hasNodes([
        ChapterNode,
        SceneNode,
        CharacterMarkNode,
        LocationMarkNode,
        AIRecommendationNode,
      ])
    ) {
      editor.registerNodes([
        {
          replace: undefined,
          with: (node) => node,
          node: ChapterNode,
        },
        {
          replace: undefined,
          with: (node) => node,
          node: SceneNode,
        },
        {
          replace: undefined,
          with: (node) => node,
          node: CharacterMarkNode,
        },
        {
          replace: undefined,
          with: (node) => node,
          node: LocationMarkNode,
        },
        {
          replace: undefined,
          with: (node) => node,
          node: AIRecommendationNode,
        },
      ]);
    }
  }, [editor]);

  return null;
}
```

## Интеграция с AI-функциями

### AIAnnotationsPlugin

```typescript
// plugins/AIAnnotationsPlugin.tsx (Renderer Process)
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useEffect, useState } from "react";
// import { AIRecommendationService } from "../services/AIRecommendationService"; // Сервис теперь в Main Process

type AIRecommendation = {
  id: number;
  type: "style" | "plot" | "consistency" | "character";
  message: string;
  startOffset: number;
  endOffset: number;
  severity: "low" | "medium" | "high";
};

export function AIAnnotationsPlugin({
  bookId,
  chapterId,
}: {
  bookId: number;
  chapterId: number;
}): null {
  const [editor] = useLexicalComposerContext();
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>(
    []
  );

  // Загрузка рекомендаций при монтировании
  useEffect(() => {
    const loadRecommendations = async () => {
      try {
        // Запрашиваем рекомендации через IPC
        const result = await window.electronAPI?.getAIRecommendations(bookId, chapterId);
        if (result && !isIpcError(result)) { // Проверяем на ошибку IPC
             setRecommendations(result);
        } else if (isIpcError(result)) {
             console.error("Failed to load AI recommendations:", result.message);
             // Показать ошибку пользователю
        }
      } catch (ipcError) {
        console.error("IPC Error loading AI recommendations:", ipcError.message);
        // Показать ошибку пользователю
      }
    };

    loadRecommendations();
  }, [bookId, chapterId]);

  // Применение декораций для рекомендаций
  useEffect(() => {
    if (!recommendations.length) return;

    const applyRecommendations = () => {
      editor.update(() => {
        // Применение рекомендаций к тексту
        // Код для создания AIRecommendationNode
      });
    };

    applyRecommendations();
  }, [editor, recommendations]);

  return null;
}
```

## Автосохранение (Renderer -> Main)

```typescript
// plugins/AutosavePlugin.tsx (Renderer Process)
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useEffect } from "react";
import { debounce } from "lodash";
// import { ChapterService } from "../services/ChapterService"; // Сервис теперь в Main Process

export function AutosavePlugin({
  bookId,
  chapterId,
  interval = 5000,
}: {
  bookId: number;
  chapterId: number;
  interval?: number;
}): null {
  const [editor] = useLexicalComposerContext();
  // serviceRef больше не нужен, используем IPC

  useEffect(() => {
    let isMounted = true;

    const saveContent = debounce(async () => {
      if (!isMounted || !editor.isEditable()) return; // Не сохраняем, если редактор не активен

      try {
        const editorState = editor.getEditorState();
        const jsonContent = JSON.stringify(editorState.toJSON());

        // Отправляем контент на сохранение через IPC
        const result = await window.electronAPI?.saveChapterContent(bookId, chapterId, jsonContent);

        if (isIpcError(result)) {
             console.error("Autosave failed via IPC:", result.message);
             // Показать ошибку пользователю
        } else {
             console.log("Content autosaved via IPC");
             // Можно обновить UI статус сохранения
        }

      } catch (ipcError) {
        console.error("IPC Error during autosave:", ipcError.message);
        // Показать ошибку пользователю
      }
    }, interval);

    // Подписываемся на изменения редактора
    const removeUpdateListener = editor.registerUpdateListener(() => {
      saveContent();
    });

    return () => {
      isMounted = false;
      removeUpdateListener();
      saveContent.cancel();
    };
  }, [editor, bookId, chapterId, interval]);

  return null;
}
```

## Основной компонент редактора

```tsx
// components/editor/DocumentEditor.tsx
import { useState, useEffect } from "react";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import LexicalErrorBoundary from "@lexical/react/LexicalErrorBoundary";
import { AIBooksNodesPlugin } from "./plugins/AIBooksNodesPlugin";
import { AIAnnotationsPlugin } from "./plugins/AIAnnotationsPlugin";
import { AutosavePlugin } from "./plugins/AutosavePlugin";
import { EditorToolbar } from "./EditorToolbar"; // Компоненты UI остаются в Renderer
import { Sidebar } from "./Sidebar";
import { AIPanel } from "./AIPanel";
// import { ChapterService } from "../services/ChapterService"; // Сервис теперь в Main Process
import { isIpcError } from "../types/ipc"; // Импортируем проверку ошибки IPC

interface DocumentEditorProps {
  bookId: number;
  chapterId: number;
}

export function DocumentEditor({ bookId, chapterId }: DocumentEditorProps) {
  const [initialContent, setInitialContent] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadChapterContent = async () => {
      try {
        setIsLoading(true);
        // Загружаем контент через IPC
        const contentResult = await window.electronAPI?.getChapterContent(bookId, chapterId);

        if (isIpcError(contentResult)) {
             console.error("Failed to load chapter content:", contentResult.message);
             setError(`Не удалось загрузить содержимое главы: ${contentResult.message}`);
             setInitialContent(null); // Устанавливаем null или пустой стейт при ошибке
        } else {
             // contentResult здесь это SerializedEditorState | null
             setInitialContent(contentResult); // Lexical сам обработает null или пустой стейт
        }

      } catch (ipcError) {
        console.error("IPC Error loading chapter content:", ipcError.message);
        setError(`Ошибка загрузки: ${ipcError.message}`);
        setInitialContent(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadChapterContent();
  }, [bookId, chapterId]);

  if (isLoading) {
    return <div>Загрузка редактора...</div>;
  }

  if (error) {
    return <div>Ошибка: {error}</div>;
  }

  const initialConfig = {
    namespace: "AIBooksEditor",
    theme: {
      paragraph: "editor-paragraph",
      text: {
        bold: "editor-text-bold",
        italic: "editor-text-italic",
        underline: "editor-text-underline",
      },
    },
    onError: (error: Error) => {
      console.error("Lexical Editor Error:", error);
    },
    // Устанавливаем editorState только если он не null, иначе Lexical использует пустой стейт
    editorState: initialContent ? JSON.stringify(initialContent) : undefined,
  };

  return (
    <div className="editor-container">
      {/* Обертка LexicalComposer должна быть условной или иметь ключ,
          чтобы пересоздаться при смене chapterId или при начальной загрузке */}
      {initialContent !== undefined && ( // Рендерим только после попытки загрузки
         <LexicalComposer initialConfig={initialConfig}>
           <div className="editor-inner">
             <EditorToolbar />
             <div className="editor-content-container">
               <Sidebar bookId={bookId} /> {/* Sidebar тоже может получать данные через IPC */}
               <RichTextPlugin
                 contentEditable={<ContentEditable className="editor-content" />}
                 placeholder={
                   <div className="editor-placeholder">Начните писать...</div>
                 }
                 ErrorBoundary={LexicalErrorBoundary}
               />
               <AIPanel bookId={bookId} chapterId={chapterId} /> {/* AIPanel вызывает AI через IPC */}
             </div>
           </div>
           <HistoryPlugin />
           <AIBooksNodesPlugin />
           <AIAnnotationsPlugin bookId={bookId} chapterId={chapterId} />
           <AutosavePlugin bookId={bookId} chapterId={chapterId} />
           {/* Другие плагины */}
         </LexicalComposer>
      )}
    </div>
  );
}
```

## Оптимизация производительности

Для обеспечения быстрой работы с большими текстами в Lexical:

1. **Виртуализация DOM**

   - Lexical уже оптимизирован для работы с большими документами
   - При необходимости можно дополнительно интегрировать react-window

2. **Оптимизация обновлений**

   - Использование селективного обновления при изменении содержимого
   - Отложенная обработка декораций и разметки

3. **Эффективное использование Transform API**

   - Пакетная обработка трансформаций для минимизации обновлений DOM
   - Использование editor.update() для атомарных изменений

4. **Оптимизация плагинов**
   - Минимизация количества подписок на изменения
   - Использование debounce для обработчиков событий

## Экспорт и импорт

### Экспорт в различные форматы

```typescript
// services/ExportService.ts
import { LexicalEditor } from "lexical";
import { $generateHtmlFromNodes } from "@lexical/html";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import { EPub } from "epub-gen";

export class ExportService {
  // Экспорт в HTML
  async exportToHTML(editor: LexicalEditor): Promise<string> {
    let html = "";

    editor.update(() => {
      html = $generateHtmlFromNodes(editor);
    });

    return html;
  }

  // Экспорт в PDF
  async exportToPDF(editor: LexicalEditor, title: string): Promise<Blob> {
    const html = await this.exportToHTML(editor);

    // Создаем временный элемент для рендеринга HTML
    const tempElement = document.createElement("div");
    tempElement.innerHTML = html;
    tempElement.style.width = "210mm"; // A4 width
    document.body.appendChild(tempElement);

    try {
      const canvas = await html2canvas(tempElement);
      const pdf = new jsPDF("p", "mm", "a4");

      pdf.addImage(
        canvas.toDataURL("image/png"),
        "PNG",
        0,
        0,
        210,
        (canvas.height * 210) / canvas.width
      );

      return pdf.output("blob");
    } finally {
      document.body.removeChild(tempElement);
    }
  }

  // Экспорт в EPUB
  async exportToEPUB(
    editor: LexicalEditor,
    title: string,
    author: string,
    coverImage?: string
  ): Promise<Blob> {
    const html = await this.exportToHTML(editor);

    // Конфигурация EPUB
    const options = {
      title,
      author,
      cover: coverImage,
      content: [
        {
          title: title,
          data: html,
        },
      ],
    };

    // Создаем EPUB
    const epub = new EPub(options);
    return await epub.generateEpub();
  }
}
```

## Конвертация между форматами хранения

Для сохранения и загрузки контента в формате JSON:

```typescript
// services/ChapterService.ts
import { createFromJSON, DatasetRecord, SerializedEditorState } from "lexical";
// services/ChapterService.ts - ЭТОТ ФАЙЛ ТЕПЕРЬ В MAIN PROCESS

// import { API } from "../lib/api"; // Больше не используется напрямую в Renderer

// Логика getChapterContent и saveChapterContent переносится в Main Process
// и будет вызываться через IPC хендлеры, используя StorageService.

// Пример хендлера в Main Process:
/*
ipcMain.handle('get-chapter-content', async (event, bookId: number, chapterId: number) => {
  try {
    const contentJsonString = await storageService.getChapterContent(bookId, chapterId);
    if (!contentJsonString) {
       // Возвращаем пустой стейт Lexical
       return { root: { children: [{ type: 'paragraph', children: [{ type: 'text', text: '' }] }] } };
    }
    return JSON.parse(contentJsonString); // Возвращаем десериализованный EditorState
  } catch (error) {
     logger.error('IPC: Failed to get chapter content', { bookId, chapterId, error: error.message });
     return { code: 'storage_error', message: `Не удалось загрузить главу: ${error.message}` };
  }
});

ipcMain.handle('save-chapter-content', async (event, bookId: number, chapterId: number, contentJsonString: string) => {
   try {
     await storageService.saveChapterContent(bookId, chapterId, contentJsonString);
     return { success: true };
   } catch (error) {
     logger.error('IPC: Failed to save chapter content', { bookId, chapterId, error: error.message });
     return { code: 'storage_error', message: `Не удалось сохранить главу: ${error.message}` };
   }
});
*/
```

## Рекомендации по реализации

1. **Постепенная разработка**

   - Начать с базовой версии редактора с поддержкой форматирования
   - Последовательно добавлять кастомные ноды и плагины
   - Тестировать производительность на каждом этапе

2. **Модульная архитектура**

   - Разделение на отдельные плагины и компоненты
   - Изоляция бизнес-логики в сервисах
   - Четкие границы между слоями приложения

3. **Оптимизация для больших документов**

   - Тестирование на документах разного размера
   - Профилирование производительности
   - Оптимизация критических путей рендеринга

4. **Интеграционное тестирование**
   - Тесты для кастомных нод
   - Тесты для плагинов и их взаимодействия
   - E2E тестирование пользовательских сценариев
