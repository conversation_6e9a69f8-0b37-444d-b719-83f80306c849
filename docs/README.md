# AI-Books Documentation Index

This document provides a complete index of all documentation files in the project. Use this as a reference to find specific documentation.

## How to Use This Documentation

### User Roles

- **Non-Technical Users**: Start with the [User Guide](guides/user_guide.md) and the overview documents in each core system
- **Project Managers**: Focus on the [Application Overview](project-overview/application-overview.md) and integration documents
- **Developers**: Begin with the [Development Guide](development/development.md) and technical architecture documents

### Version Control

Documents follow [Semantic Versioning](https://semver.org/). See our [version history](guides/version-history.md) for detailed changelog.

### Quick Links

## Project Overview

- [Application Overview](project-overview/application-overview.md) - High-level design and components
- [Features List](project-overview/features.md) - Complete feature set overview
- [MVP Plan](project-overview/mvp-plan.md) - Development phases and roadmap
- [User Stories](project-overview/user-stories.md) - Detailed user scenarios
- [Text Editor Architecture](project-overview/text-editor.md) - Rich text editor design
- [Offline Mode](project-overview/offline-mode.md) - Offline functionality implementation

## Core Systems

### AI Management System

- [Overview](core-systems/ai-management/overview.md) - AI system design and purpose
- [Technical Architecture](core-systems/ai-management/technical-architecture.md) - Technical implementation
- [Privacy and Ethics](core-systems/ai-management/privacy-and-ethics.md) - Data privacy and ethical AI usage
- [Integration Hub](core-systems/ai-management/integration-hub.md) - How AI connects with other systems
- [Core Components](core-systems/ai-management/core-components.md) - Key system components
- [Model Management](core-systems/ai-management/model-management.md) - AI model configuration
- [Prompt Engineering](core-systems/ai-management/prompt-engineering.md) - Prompt design guidelines
- [Cross-System Workflows](core-systems/ai-management/cross-system-workflows.md) - AI integration patterns

#### Creative Tools

- [Overview](core-systems/ai-management/creative-tools/overview.md) - Creative assistance tools
- [Technical Architecture](core-systems/ai-management/creative-tools/technical-architecture.md) - Implementation details
- [Character Voice](core-systems/ai-management/creative-tools/character-voice.md) - Character dialogue generation
- [Description Workshop](core-systems/ai-management/creative-tools/description-workshop.md) - Scene description tools
- [Scene Expansion](core-systems/ai-management/creative-tools/scene-expansion.md) - Scene enhancement tools

### Book Management System

- [Overview](core-systems/book-management/overview.md) - Book system design and purpose
- [Book Creation](core-systems/book-management/book-creation.md) - Writing process management
- [Structure Management](core-systems/book-management/structure-management.md) - Book organization features
- [Content Management](core-systems/book-management/content-management.md) - Managing book content
- [Export & Publishing](core-systems/book-management/export-publishing.md) - Publishing options
- [AI Integration](core-systems/book-management/ai-integration.md) - AI features integration
- [Integration Points](core-systems/book-management/integration-points.md) - System connections
- [User Flows](core-systems/book-management/user-flows.md) - Common usage patterns
- [User Personas](core-systems/book-management/user-personas.md) - Target user profiles

#### Technical Documentation

- [Book Creation](core-systems/book-management/technical/book-creation-technical.md)
- [Content Management](core-systems/book-management/technical/content-management-technical.md)
- [Export & Publishing](core-systems/book-management/technical/export-publishing-technical.md)
- [Structure Management](core-systems/book-management/technical/structure-management-technical.md)

### Character Management System

- [Overview](core-systems/character-management/overview.md) - Character system design
- [Character Development](core-systems/character-management/character-development.md) - Development tools
- [Character Management](core-systems/character-management/character-management.md) - Management features
- [AI Integration](core-systems/character-management/ai_integration.md) - AI assistance features
- [Integration Points](core-systems/character-management/integration-points.md) - System connections
- [User Flows](core-systems/character-management/user-flows.md) - Common usage patterns
- [User Personas](core-systems/character-management/user-personas.md) - Target user profiles

#### Technical Documentation

- [Architecture](core-systems/character-management/technical/architecture.md)
- [Technical Architecture](core-systems/character-management/technical/technical-architecture.md)
- [Character Development API](core-systems/character-management/technical/character-development-api.md)

### Content Versioning System

- [Overview](core-systems/content-versioning/overview.md) - Version control design
- [Technical Architecture](core-systems/content-versioning/technical-architecture.md) - Implementation details
- [Data Model](core-systems/content-versioning/data-model.md) - Data structure
- [Diff Service](core-systems/content-versioning/diff-service.md) - Version comparison
- [Version Management](core-systems/content-versioning/version-management.md) - Version control
- [Frontend Implementation](core-systems/content-versioning/frontend-implementation.md) - UI implementation
- [System Integration](core-systems/content-versioning/system-integration.md) - System connections
- [AI Integration](core-systems/content-versioning/ai-integration.md) - AI features
- [Integration Points](core-systems/content-versioning/integration-points.md) - Connection points
- [User Personas](core-systems/content-versioning/user-personas.md) - Target users

#### Technical Documentation

- [AI Integration](core-systems/content-versioning/technical/ai-integration-technical.md)
- [Data Model](core-systems/content-versioning/technical/data-model-technical.md)
- [Diff Service](core-systems/content-versioning/technical/diff-service-technical.md)
- [Frontend Implementation](core-systems/content-versioning/technical/frontend-implementation-technical.md)
- [System Integration](core-systems/content-versioning/technical/system-integration-technical.md)
- [Version Management](core-systems/content-versioning/technical/version-management-technical.md)

### World Building System

- [Overview](core-systems/world-building-management/overview.md) - World building design
- [Technical Architecture](core-systems/world-building-management/technical-architecture.md) - Implementation
- [Geography & Locations](core-systems/world-building-management/geography-locations.md) - Location management
- [Cultures & Societies](core-systems/world-building-management/cultures-societies.md) - Culture management
- [Timeline & History](core-systems/world-building-management/timeline-history.md) - Historical tracking
- [Encyclopedia Reference](core-systems/world-building-management/encyclopedia-reference.md) - World encyclopedia
- [Rules Systems](core-systems/world-building-management/rules-systems.md) - World rules
- [AI Integration](core-systems/world-building-management/ai-integration.md) - AI features
- [System Integration](core-systems/world-building-management/system-integration.md) - System connections

#### Technical Documentation

- [AI Integration](core-systems/world-building-management/technical/ai-integration-technical.md)
- [Cultures & Societies](core-systems/world-building-management/technical/cultures-societies-technical.md)
- [Encyclopedia Reference](core-systems/world-building-management/technical/encyclopedia-reference-technical.md)
- [Geography & Locations](core-systems/world-building-management/technical/geography-locations-technical.md)
- [Rules Systems](core-systems/world-building-management/technical/rules-systems-technical.md)
- [System Integration](core-systems/world-building-management/technical/system-integration-technical.md)
- [Technical Architecture](core-systems/world-building-management/technical/technical-architecture.md)
- [Timeline & History](core-systems/world-building-management/technical/timeline-history-technical.md)

### Other Core Systems

#### Collaboration Management

- [Overview](core-systems/collaboration-management/overview.md) - Collaboration features
- [Access Control](core-systems/collaboration-management/access-control.md) - Permission management
- [Activity Notification](core-systems/collaboration-management/activity-notification.md) - User notifications
- [AI Integration](core-systems/collaboration-management/ai-integration.md) - AI assistance
- [Feedback & Review](core-systems/collaboration-management/feedback-review.md) - Review process
- [Realtime Collaboration](core-systems/collaboration-management/realtime-collaboration.md) - Live editing
- [System Integration](core-systems/collaboration-management/system-integration.md) - System connections
- [UI Components](core-systems/collaboration-management/ui-components.md) - Interface elements

#### Goals Management

- [Overview](core-systems/goals-management/overview.md) - Goals system design
- [Goal Management](core-systems/goals-management/goal-management.md) - Goal tracking
- [Achievement System](core-systems/goals-management/achievement-system.md) - Achievements
- [Analytics & Insights](core-systems/goals-management/analytics-insights.md) - Progress analytics
- [Session Tracking](core-systems/goals-management/session-tracking.md) - Writing sessions
- [AI Integration](core-systems/goals-management/ai-integration.md) - AI features
- [System Integration](core-systems/goals-management/system-integration.md) - System connections
- [Integration Points](core-systems/goals-management/integration-points.md) - Connection points
- [User Personas](core-systems/goals-management/user-personas.md) - Target users

##### Technical Documentation

- [Achievement System](core-systems/goals-management/technical/achievement-system-technical.md)
- [Goal Management](core-systems/goals-management/technical/goal-management-technical.md)
- [Session Tracking](core-systems/goals-management/technical/session-tracking-technical.md)
- [System Integration](core-systems/goals-management/technical/system-integration-technical.md)

#### Idea and Research Management

- [Overview](core-systems/idea-and-research-management/overview.md) - System design
- [AI Integration](core-systems/idea-and-research-management/ai-integration.md) - AI features
- [Connection Engine](core-systems/idea-and-research-management/connection-engine.md) - Idea linking
- [Idea Workshop](core-systems/idea-and-research-management/idea-workshop.md) - Idea development
- [Inspiration Framework](core-systems/idea-and-research-management/inspiration-framework.md) - Creative tools
- [Knowledge Repository](core-systems/idea-and-research-management/knowledge-repository.md) - Research storage
- [System Integration](core-systems/idea-and-research-management/system-integration.md) - System connections
- [UI Components](core-systems/idea-and-research-management/ui-components.md) - Interface elements

##### Technical Documentation

- [Data Model](core-systems/idea-and-research-management/technical/data-model.md)

## Development

- [Development Guide](development/development.md) - Development setup
- [Development Scripts](development/development-scripts.md) - Helper scripts
- [Code Quality](development/code-quality.md) - Quality standards
- [Error Handling](development/errors-handling.md) - Error standards
- [Function Calling](development/function-calling.md) - OpenAI integration
- [Structured Output](development/structered-output.md) - AI output format
- [Backend Notes](development/backend-implementation-notes.md) - Backend architecture
- [Frontend Notes](development/frontend-implementation-notes.md) - Frontend architecture

## Deployment & Operations

- [Database Configuration](deployment-operations/database-configuration.md) - Database setup
- [Database Connection Pooling](deployment-operations/database-connection-pooling.md) - Connection management
- [Deployment Strategy](deployment-operations/deployment-strategy.md) - Production deployment
- [CI/CD Pipeline](deployment-operations/ci-cd-pipeline.md) - Continuous integration
- [Docker Image Management](deployment-operations/docker-image-management.md) - Container management
- [Redis Configuration](deployment-operations/redis-configuration.md) - Redis setup
- [Logging & Monitoring](deployment-operations/logging-monitoring.md) - Observability
- [Environment Variables](deployment-operations/environment-variables.md) - Configuration
- [Vector Capabilities](deployment-operations/vector-capabilities.md) - Vector features
- [Vector Search](deployment-operations/vector-search.md) - Search implementation
- [Volume Management](deployment-operations/volume-management.md) - Storage management

## External Documentation

- [Sudowrite Comparison](external/sudowrite.md) - Feature comparison

## Business Documentation

- [Business Plan](business/business-plan.md) - Commercial strategy

## Guides

- [Common Questions](guides/answers.md) - FAQ
- [User Guide](guides/user_guide.md) - End user guide
- [Documentation Guidelines](guides/documentation-guidelines.md) - Doc standards

## Marketing Materials

- [Features Overview](business/marketing/features.md) - Feature highlights
- [Product Overview](business/marketing/overview.md) - Product summary
- [Features Overview (RU)](business/marketing/features.ru.md) - Russian features
- [Product Overview (RU)](business/marketing/overview.ru.md) - Russian summary

---

_Last updated: March 13, 2024_

[Contributing Guidelines](guides/contributing.md) | [Documentation Standards](guides/documentation-guidelines.md) | [Support](guides/support.md)
