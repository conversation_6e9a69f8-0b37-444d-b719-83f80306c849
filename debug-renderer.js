// Simple debugging tool to test IPC events
// This should be run in the renderer console (dev tools)

console.log('[DEBUG] Setting up character creation test...');

// Test the event subscription
window.electronAPI.ipc.on('characters-updated', (data) => {
    console.log('[DEBUG] Received characters-updated event:', data);
});

// Test the test-event subscription
window.electronAPI.ipc.on('test-event', (data) => {
    console.log('[DEBUG] Received test-event:', data);
});

// Function to manually trigger character creation
window.testCreateCharacter = async (name = 'Test Character') => {
    console.log(`[DEBUG] Attempting to create character: ${name}`);
    try {
        const result = await window.electronAPI.ipc.invoke('core:commands.execute', {
            commandId: 'book-ide.characters:commands.createCharacter',
            args: {}
        });
        console.log('[DEBUG] Character creation result:', result);
    } catch (error) {
        console.error('[DEBUG] Error creating character:', error);
    }
};

console.log('[DEBUG] Test setup complete. Use testCreate<PERSON>haracter() to test.');
