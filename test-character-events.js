const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Test Character Events System
async function testCharacterEventsSystem() {
  console.log('=== Testing Character Events System ===\n');
  
  const characterId = 'char-1743758339667-ax64da'; // <PERSON>
  
  try {
    // 1. Test getting existing events
    console.log('1. Getting existing character events...');
    const existingEvents = await ipcRenderer.invoke('book-ide.characters:channels.getCharacterEvents', { characterId });
    console.log('Existing events:', existingEvents);
    console.log('');
    
    // 2. Test creating a new event
    console.log('2. Creating a new character event...');
    const newEventData = {
      characterId,
      eventType: 'Goals',
      description: '<PERSON> discovers his true parentage and struggles with his identity as both a Stark and a Targaryen',
      timelinePosition: 75,
      impact: 10,
      relatedSceneId: 's-1744794557808-26dxkm'
    };
    
    const createResult = await ipcRenderer.invoke('book-ide.characters:channels.createCharacterEvent', newEventData);
    console.log('Create result:', createResult);
    console.log('');
    
    if (createResult && createResult.id) {
      const newEventId = createResult.id;
      
      // 3. Test updating the event
      console.log('3. Updating the character event...');
      const updateData = {
        eventType: 'Goals',
        description: 'Jon discovers his true parentage (R+L=J) and must decide between his Stark loyalty and Targaryen heritage',
        timelinePosition: 80,
        impact: 10,
        relatedSceneId: 's-1744794557808-26dxkm'
      };
      
      const updateResult = await ipcRenderer.invoke('book-ide.characters:channels.updateCharacterEvent', {
        id: newEventId,
        ...updateData
      });
      console.log('Update result:', updateResult);
      console.log('');
      
      // 4. Test getting events again to verify the update
      console.log('4. Getting events after update...');
      const updatedEvents = await ipcRenderer.invoke('book-ide.characters:channels.getCharacterEvents', { characterId });
      console.log('Updated events:', updatedEvents);
      console.log('');
      
      // 5. Test creating another event of different type
      console.log('5. Creating a Relationships event...');
      const relationshipEventData = {
        characterId,
        eventType: 'Relationships',
        description: 'Jon forms a deep bond with Daenerys Targaryen, complicating his political and personal loyalties',
        timelinePosition: 85,
        impact: 8,
        relatedSceneId: 's-1744794786263-6ad5no'
      };
      
      const relationshipResult = await ipcRenderer.invoke('book-ide.characters:channels.createCharacterEvent', relationshipEventData);
      console.log('Relationship event result:', relationshipResult);
      console.log('');
      
      // 6. Test creating a Conflicts event
      console.log('6. Creating a Conflicts event...');
      const conflictEventData = {
        characterId,
        eventType: 'Conflicts',
        description: 'Jon faces the moral dilemma of executing his oath to the Night\'s Watch versus his duty to family',
        timelinePosition: 60,
        impact: 7,
        relatedSceneId: 's-1744809697376-bix7p9'
      };
      
      const conflictResult = await ipcRenderer.invoke('book-ide.characters:channels.createCharacterEvent', conflictEventData);
      console.log('Conflict event result:', conflictResult);
      console.log('');
      
      // 7. Get all events to see the complete timeline
      console.log('7. Getting complete event timeline...');
      const allEvents = await ipcRenderer.invoke('book-ide.characters:channels.getCharacterEvents', { characterId });
      console.log('Complete timeline:');
      if (Array.isArray(allEvents)) {
        allEvents
          .sort((a, b) => a.timelinePosition - b.timelinePosition)
          .forEach((event, index) => {
            console.log(`  ${index + 1}. [Position ${event.timelinePosition}] ${event.eventType}: ${event.description.substring(0, 80)}...`);
            console.log(`     Impact: ${event.impact}/10, Scene: ${event.relatedSceneId || 'None'}`);
          });
      }
      console.log('');
      
      // 8. Test deleting one of the events
      console.log('8. Testing event deletion...');
      if (relationshipResult && relationshipResult.id) {
        const deleteResult = await ipcRenderer.invoke('book-ide.characters:channels.deleteCharacterEvent', {
          id: relationshipResult.id
        });
        console.log('Delete result:', deleteResult);
        
        // Verify deletion
        const eventsAfterDelete = await ipcRenderer.invoke('book-ide.characters:channels.getCharacterEvents', { characterId });
        console.log('Events after deletion:', eventsAfterDelete.length, 'events remaining');
      }
      console.log('');
    }
    
    console.log('=== Character Events System Test Complete ===');
    console.log('✅ All tests passed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testCharacterEventsSystem();
